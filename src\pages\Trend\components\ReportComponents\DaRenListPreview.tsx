import type { DaRenList } from '@/api/marketTestData/NewType'
import { copyToClipboard } from '@jl-org/tool'
import { message } from 'antd'
import { memo, useEffect, useState } from 'react'
import { Checkbox } from '@/components/Checkbox/Checkbox'
import { cn } from '@/utils'

/** 定义组件属性类型 */
export type DaRenListPreviewProps = {
  data: DaRenList
  className?: string
  style?: React.CSSProperties
}

/**
 * 从文本中提取邮箱地址
 * @param text 要提取邮箱的文本
 * @returns 提取到的邮箱地址，如果没有则返回null
 */
function extractEmail(text?: string): string | null {
  if (!text)
    return null

  const emailRegex = /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g
  const match = text.match(emailRegex)

  return match
    ? match[0]
    : null
}

export const DaRenListPreview = memo<DaRenListPreviewProps>(({
  data,
  className,
  style,
}) => {
  const [category, setCategory] = useState<keyof DaRenList>('kol')

  useEffect(() => {
    if (data && !data[category]) {
      /** 如果当前分类不存在数据，则选择第一个有数据的分类 */
      const firstKey = Object.keys(data).find(key => (data[key as keyof DaRenList] ?? []).length > 0) as keyof DaRenList
      if (firstKey) {
        setCategory(firstKey)
      }
    }
  }, [data, category])

  /** 获取当前分类的达人 */
  const currentInfluencers = (data?.[category] ?? [])
    .map((item) => {
      // `info` 属性现在是一个对象，不再是数组。我们直接映射每个 item。
      /** 为防止数据不一致，这里进行防御性检查。 */
      if (!item?.info) {
        return null
      }

      /** 提取邮箱 */
      const email = extractEmail(item.info.userText)

      return {
        ...item.info,
        type: category.toUpperCase(),
        link: item.anchorLink,
        email,
      }
    })
    /** 过滤掉上面 map 操作中可能产生的 null 值 */
    .filter(Boolean)

  /** 处理打开链接 */
  const handleOpenLink = (link?: string) => {
    if (link) {
      window.open(link, '_blank')
    }
  }

  /** 处理复制邮箱 */
  const handleCopyEmail = (influencer: any) => {
    const textToCopy = influencer.email || influencer.userText || ''
    copyToClipboard(textToCopy)

    if (influencer.email) {
      message.success('Success to copy email')
    }
  }

  return (
    <div
      className={ cn(
        'flex flex-col h-full',
        className,
      ) }
      style={ style }
    >
      {/* 头部分类选择 */}
      <div className="flex-none">
        <div className="my-3 flex border-b border-t border-slate-200 py-3 space-x-4">
          <div>Influencer Categories: </div>

          {data && Object.keys(data).map((key) => {
            const displayName = key === 'suren'
              ? 'Regular Creators'
              : key.toUpperCase()
            return (
              <Checkbox
                key={ key }
                label={ displayName }
                size={ 20 }
                checked={ category === key }
                onChange={ () => {
                  setCategory(key as keyof DaRenList)
                } }
                labelClassName="text-sm text-gray-700 font-medium dark:text-gray-300"
              />
            )
          })}
        </div>

        <h2 className="mb-2 text-2xl text-gray-900 font-bold dark:text-gray-200">
          Influencer Collaboration List
        </h2>
        <p className="mb-4 text-sm text-gray-600 dark:text-gray-400">
          Select an influencer from the list below for potential collaboration opportunities.
        </p>
      </div>

      {/* 可滚动的达人列表 */}
      <div className="flex-1 overflow-y-auto pr-1">
        <div className="w-full flex flex-col">
          {currentInfluencers.map((influencer, index) => (
            <div
              key={ index }
              className="flex items-center justify-between border-b border-gray-200 py-4 dark:border-gray-700"
            >
              <div className="flex items-center gap-4">
                <div>
                  <div className="flex items-center gap-2">
                    <h3 className="font-medium">{influencer?.nick || 'Unknown'}</h3>
                    <div className={ cn(
                      'px-2 py-0.5 text-xs rounded',
                      influencer?.type === 'KOL'
                        ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
                        : influencer?.type === 'KOC'
                          ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
                          : 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300',
                    ) }>
                      {influencer?.type || 'Unknown'}
                    </div>
                  </div>

                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    {influencer?.userText?.split('\n')?.[0] || influencer?.tagList || '未知职业'}
                  </div>
                </div>
              </div>

              <div className="flex items-center gap-8">
                {/* 粉丝数据 */}
                <div className="flex flex-col items-center">
                  <span className="font-medium">
                    {Number(influencer?.fans_count || 0) >= 10000
                      ? `${(Number(influencer?.fans_count || 0) / 1000000).toFixed(1)}M`
                      : influencer?.fans_count || 0}
                  </span>
                  <span className="text-xs text-gray-500">Followers</span>
                </div>

                {/* 点赞收藏数据 */}
                <div className="flex flex-col items-center">
                  <span className="font-medium">
                    {Number(influencer?.likeCollCount || 0) >= 10000
                      ? `${(Number(influencer?.likeCollCount || 0) / 1000000).toFixed(1)}M`
                      : influencer?.likeCollCount || 0}
                  </span>
                  <span className="text-xs text-gray-500">Likes</span>
                </div>

                {/* 操作按钮 */}
                <div className="flex items-center gap-4">
                  <button
                    className={ cn(
                      'rounded-full p-2 hover:bg-gray-100 dark:hover:bg-gray-800',
                      !influencer?.email && 'opacity-50 cursor-not-allowed',
                    ) }
                    onClick={ () => handleCopyEmail(influencer) }
                    title={ influencer?.email
                      ? `${influencer.email}`
                      : '' }
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
                      <polyline points="22,6 12,13 2,6"></polyline>
                    </svg>
                  </button>

                  <button
                    className="rounded-full p-2 hover:bg-gray-100 dark:hover:bg-gray-800"
                    onClick={ () => handleOpenLink(influencer?.link) }
                    title={ influencer?.link || '打开链接' }
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M5 12h14"></path>
                      <path d="M12 5l7 7-7 7"></path>
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
})

DaRenListPreview.displayName = 'DaRenListPreview'
