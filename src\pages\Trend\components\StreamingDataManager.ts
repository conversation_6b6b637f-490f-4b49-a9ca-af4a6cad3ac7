/**
 * 流式数据管理器
 * 用于管理多个独立的数据流，支持实时数据推送和订阅
 */

export interface StreamData {
  id: string
  content: string
  isActive: boolean
  isCompleted: boolean
  lastUpdated: number
}

export type StreamSubscriber = (data: StreamData) => void

/**
 * 流式数据管理器 - 单例模式
 */
class StreamingDataManager {
  private static instance: StreamingDataManager
  private streams: Map<string, StreamData> = new Map()
  private subscribers: Map<string, Set<StreamSubscriber>> = new Map()

  private constructor() {}

  /**
   * 获取单例实例
   */
  static getInstance(): StreamingDataManager {
    if (!StreamingDataManager.instance) {
      StreamingDataManager.instance = new StreamingDataManager()
    }
    return StreamingDataManager.instance
  }

  /**
   * 创建新的数据流
   */
  createStream(streamId: string): void {
    if (!this.streams.has(streamId)) {
      const streamData: StreamData = {
        id: streamId,
        content: '',
        isActive: false,
        isCompleted: false,
        lastUpdated: Date.now(),
      }
      this.streams.set(streamId, streamData)
      this.subscribers.set(streamId, new Set())
    }
  }

  /**
   * 启动数据流
   */
  startStream(streamId: string): void {
    const stream = this.streams.get(streamId)
    if (stream) {
      stream.isActive = true
      stream.isCompleted = false
      stream.lastUpdated = Date.now()
      this.notifySubscribers(streamId, stream)
    }
  }

  /**
   * 推送数据到指定流
   */
  pushData(streamId: string, data: string, append: boolean = true): void {
    let stream = this.streams.get(streamId)

    /** 如果流不存在，自动创建 */
    if (!stream) {
      this.createStream(streamId)
      stream = this.streams.get(streamId)!
    }

    /** 更新流数据 */
    if (append) {
      stream.content += data
    }
    else {
      stream.content = data
    }

    stream.isActive = true
    stream.lastUpdated = Date.now()

    /** 通知所有订阅者 */
    this.notifySubscribers(streamId, stream)
  }

  /**
   * 完成数据流
   */
  completeStream(streamId: string): void {
    const stream = this.streams.get(streamId)
    if (stream) {
      stream.isActive = false
      stream.isCompleted = true
      stream.lastUpdated = Date.now()
      this.notifySubscribers(streamId, stream)
    }
  }

  /**
   * 订阅数据流
   */
  subscribe(streamId: string, callback: StreamSubscriber): () => void {
    /** 确保流存在 */
    if (!this.streams.has(streamId)) {
      this.createStream(streamId)
    }

    const subscribers = this.subscribers.get(streamId)!
    subscribers.add(callback)

    /** 立即发送当前数据 */
    const currentStream = this.streams.get(streamId)!
    callback(currentStream)

    /** 返回取消订阅函数 */
    return () => {
      subscribers.delete(callback)
    }
  }

  /**
   * 获取流数据
   */
  getStream(streamId: string): StreamData | undefined {
    return this.streams.get(streamId)
  }

  /**
   * 清空指定流的数据
   */
  clearStream(streamId: string): void {
    const stream = this.streams.get(streamId)
    if (stream) {
      stream.content = ''
      stream.isActive = false
      stream.isCompleted = false
      stream.lastUpdated = Date.now()
      this.notifySubscribers(streamId, stream)
    }
  }

  /**
   * 重置指定流
   */
  resetStream(streamId: string): void {
    this.clearStream(streamId)
  }

  /**
   * 删除数据流
   */
  deleteStream(streamId: string): void {
    this.streams.delete(streamId)
    this.subscribers.delete(streamId)
  }

  /**
   * 获取所有流的状态
   */
  getAllStreams(): Map<string, StreamData> {
    return new Map(this.streams)
  }

  /**
   * 推送 thinking1 数据到聚合流
   * 用于将三个工作流的 thinking1 数据按顺序拼接
   */
  pushThinking1Data(workflowName: string, data: string): void {
    const combinedStreamId = STREAM_IDS.COMBINED_THINKING1

    /** 确保聚合流存在 */
    if (!this.streams.has(combinedStreamId)) {
      this.createStream(combinedStreamId)
    }

    const stream = this.streams.get(combinedStreamId)!

    /** 根据工作流添加适当的分隔符 */
    let separator = ''
    if (stream.content && workflowName !== 'data_report') {
      separator = '\n\n' // 在不同工作流之间添加换行分隔
    }

    /** 追加数据 */
    stream.content += separator + data
    stream.isActive = true
    stream.lastUpdated = Date.now()

    /** 通知订阅者 */
    this.notifySubscribers(combinedStreamId, stream)
  }

  /**
   * 推送 planning_scheme thinking1 数据到专用流
   */
  pushPlanningThinking1Data(data: string): void {
    const streamId = STREAM_IDS.PLANNING_THINKING1

    /** 确保流存在 */
    if (!this.streams.has(streamId)) {
      this.createStream(streamId)
    }

    const stream = this.streams.get(streamId)!

    /** 追加数据 */
    stream.content += data
    stream.isActive = true
    stream.lastUpdated = Date.now()

    /** 通知订阅者 */
    this.notifySubscribers(streamId, stream)
  }

  /**
   * 完成 planning_scheme thinking1 数据流
   */
  completePlanningThinking1Stream(): void {
    const streamId = STREAM_IDS.PLANNING_THINKING1
    const stream = this.streams.get(streamId)

    if (stream) {
      stream.isActive = false
      stream.isCompleted = true
      stream.lastUpdated = Date.now()
      this.notifySubscribers(streamId, stream)
    }
  }

  /**
   * 推送一次性完整数据并模拟流式效果
   * 用于处理 distill_daren_list 工作流的 biji_thinking 数据
   */
  pushOperationsThinking1Data(data: string): void {
    const streamId = STREAM_IDS.OPERATIONS_THINKING1

    /** 确保流存在 */
    if (!this.streams.has(streamId)) {
      this.createStream(streamId)
    }

    const stream = this.streams.get(streamId)!

    /** 设置完整数据（一次性推送） */
    stream.content = data
    stream.isActive = true
    stream.lastUpdated = Date.now()

    /** 通知订阅者 */
    this.notifySubscribers(streamId, stream)
  }

  /**
   * 完成 distill_daren_list biji_thinking 数据流
   */
  completeOperationsThinking1Stream(): void {
    const streamId = STREAM_IDS.OPERATIONS_THINKING1
    const stream = this.streams.get(streamId)

    if (stream) {
      stream.isActive = false
      stream.isCompleted = true
      stream.lastUpdated = Date.now()
      this.notifySubscribers(streamId, stream)
    }
  }

  /**
   * 完成聚合流
   * 当所有相关工作流完成时调用
   */
  completeThinking1Stream(): void {
    const combinedStreamId = STREAM_IDS.COMBINED_THINKING1
    const stream = this.streams.get(combinedStreamId)

    if (stream) {
      stream.isActive = false
      stream.isCompleted = true
      stream.lastUpdated = Date.now()
      this.notifySubscribers(combinedStreamId, stream)
    }
  }

  /**
   * 通知订阅者
   */
  private notifySubscribers(streamId: string, stream: StreamData): void {
    const subscribers = this.subscribers.get(streamId)
    if (subscribers) {
      subscribers.forEach((callback) => {
        try {
          callback({ ...stream }) // 传递副本避免引用问题
        }
        catch {
          // Error handling without console output
        }
      })
    }
  }
}

/** 导出单例实例 */
export const streamingDataManager = StreamingDataManager.getInstance()

/** 导出预定义的流ID常量 */
export const STREAM_IDS = {
  INSIGHT_THINKING: 'insight-thinking',
  PLANNING_THINKING: 'planning-thinking',
  OPERATIONS_THINKING: 'operations-thinking',
  COMBINED_THINKING1: 'combined-thinking1', // 聚合三个工作流的thinking1数据
  PLANNING_THINKING1: 'planning-thinking1', // planning_scheme工作流的thinking1数据
  OPERATIONS_THINKING1: 'operations-thinking1', // distill_daren_list工作流的biji_thinking数据
  TEXT_LINK_THINKING: 'text-link-thinking', // text_link_disassemble工作流的thinking数据
  ORIGINAL_WORK_THINKING: 'original-work-thinking', // original_work工作流的thinking数据
} as const

export type StreamId = typeof STREAM_IDS[keyof typeof STREAM_IDS]
