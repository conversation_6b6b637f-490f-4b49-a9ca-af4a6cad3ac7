import { getWorkflowOutput, setWorkflowOutput, setWorkflowExecutionParams } from './cozeStreamApi'
import { fetchEventSourceWithTimeout } from '@/utils/streamTimeout'

export interface DuibiaoDisassemblyTextParams {
  product_description: string // 产品描述（从表单获取）
  biji_content: string // 笔记内容（从distill_daren_list工作流获取）
  biji_url: string // 笔记URL（从distill_daren_list工作流的biji_list中获取）
  biji_title: string // 笔记标题（从distill_daren_list工作流的biji_list中获取）
  daren_list: string // 达人列表（从distill_daren_list工作流的daren_list中获取）
  brand_name: string // 品牌名称（从表单获取）
  product_name: string // 产品名称（从表单获取）
  brand_report: string // 品牌报告（从data_reporte工作流获取）
  pic: string // 图片URL（从表单获取）
  platform: string // 平台（固定为rednote）
}

export interface ParsedStreamData {
  nodeTitle: string
  content: string
  type?: string
}

/**
 * 调用 duibiao_disassembly_text 流式 API
 * 用于对标拆解文本生成
 */
export async function callDuibiaoDisassemblyTextAPI(
  onData: (data: ParsedStreamData) => void,
  onError?: (error: Error) => void,
  onComplete?: () => void,
  taskInstanceId?: string,
  parameters?: DuibiaoDisassemblyTextParams,
): Promise<void> {
  try {
    console.log('[DuibiaoDisassemblyTextAPI] 开始执行 duibiao_disassembly_text 工作流')
    console.log('[DuibiaoDisassemblyTextAPI] 参数:', parameters)

    // 获取 token 和 taskInstanceId
    const { userStore } = await import('@/store/userStore')
    const token = userStore.token
    if (!token) {
      throw new Error('用户未登录，无法获取认证token')
    }
    const instanceId = taskInstanceId || localStorage.getItem('confirmed_taskInstanceId') || localStorage.getItem('taskInstanceId') || ''

    if (!instanceId) {
      throw new Error('未找到有效的 taskInstanceId')
    }

    if (!parameters) {
      throw new Error('duibiao_disassembly_text 缺少必要参数')
    }

    const apiUrl = import.meta.env.VITE_API_BASE_URL || '/api'

    // 存储响应数据
    const responseData: Record<string, any> = {}
    // 存储所有 content 节点的内容，按顺序拼接
    let contentParts: { seq: number; text: string }[] = []

    fetchEventSourceWithTimeout(`${apiUrl}/app/market/stream/execute-main`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
      body: JSON.stringify({
        taskInstanceId: instanceId,
        platform: 'rednote',
        workflowName: 'duibiao_disassembly_text',
        parameters,
      }),
      onmessage(ev) {
        if (!ev.data || ev.event === 'end')
          return

        // 处理 meta 事件，提取 executionId 和 taskInstanceId
        if (ev.event === 'meta') {
          try {
            const metaData = JSON.parse(ev.data)
            console.log('[DuibiaoDisassemblyTextAPI] 接收到meta数据:', metaData)
            
            if (metaData.executionId && metaData.taskInstanceId) {
              // 存储执行参数以供后续使用
              setWorkflowExecutionParams('duibiao_disassembly_text', metaData.executionId, metaData.taskInstanceId)
              console.log('[DuibiaoDisassemblyTextAPI] 已保存 duibiao_disassembly_text 执行参数:', {
                executionId: metaData.executionId,
                taskInstanceId: metaData.taskInstanceId
              })
            }
          } catch (e) {
            console.error('[DuibiaoDisassemblyTextAPI] 解析meta数据失败:', e)
          }
          return
        }

        try {
          const parsed = JSON.parse(ev.data)
          console.log('[DuibiaoDisassemblyTextAPI] 接收数据:', {
            node_title: parsed.node_title,
            node_seq_id: parsed.node_seq_id,
            hasContent: !!parsed.content,
          })

          // 特殊处理 content 节点，收集所有片段
          if (parsed.node_title === 'content' && parsed.content) {
            const seqId = parseInt(parsed.node_seq_id) || 0
            contentParts.push({ seq: seqId, text: parsed.content })
            console.log('[DuibiaoDisassemblyTextAPI] 收集 content 片段:', seqId, parsed.content)
            
            // 如果节点已完成，拼接所有内容
            if (parsed.node_is_finish) {
              // 按序号排序并拼接
              contentParts.sort((a, b) => a.seq - b.seq)
              const fullContent = contentParts.map(p => p.text).join('')
              responseData['笔记正文'] = fullContent
              responseData['content'] = fullContent // 兼容性
              console.log('[DuibiaoDisassemblyTextAPI] 完整正文:', fullContent)
              
              // 更新缓存
              const duibiaoData = getWorkflowOutput('duibiao_disassembly_text')
              const updatedDuibiaoData = { 
                ...duibiaoData, 
                '笔记正文': fullContent,
                'content': fullContent 
              }
              setWorkflowOutput('duibiao_disassembly_text', updatedDuibiaoData)
            }
          }
          // 处理其他节点
          else if (parsed.node_title && parsed.content) {
            // 对于 title 和 tag 节点，直接使用最终值
            if (parsed.node_title === 'title') {
              responseData['笔记title'] = parsed.content
              responseData['title'] = parsed.content // 兼容性
            } else if (parsed.node_title === 'tag') {
              responseData['标签'] = parsed.content
              responseData['tag'] = parsed.content // 兼容性
            } else {
              responseData[parsed.node_title] = parsed.content
            }

            // 存储到全局缓存
            const duibiaoData = getWorkflowOutput('duibiao_disassembly_text')
            const updatedDuibiaoData = { ...duibiaoData, [parsed.node_title]: parsed.content }
            setWorkflowOutput('duibiao_disassembly_text', updatedDuibiaoData)
          }

          // 转换为 ParsedStreamData 格式并回调
          if (parsed.content) {
            onData({
              nodeTitle: parsed.node_title || '',
              content: parsed.content,
              type: parsed.type,
            })
          }
        }
        catch (error) {
          console.error('[DuibiaoDisassemblyTextAPI] 解析数据失败:', error, 'raw data:', ev.data)
        }
      },
      onclose() {
        console.log('[DuibiaoDisassemblyTextAPI] 工作流完成')
        
        // 确保 content 已经拼接完成
        if (contentParts.length > 0 && !responseData['笔记正文']) {
          contentParts.sort((a, b) => a.seq - b.seq)
          const fullContent = contentParts.map(p => p.text).join('')
          responseData['笔记正文'] = fullContent
          responseData['content'] = fullContent
        }
        
        console.log('[DuibiaoDisassemblyTextAPI] 最终数据:', responseData)

        // 存储最终完整数据
        setWorkflowOutput('duibiao_disassembly_text', responseData)
        onComplete?.()
      },
      onerror(err) {
        console.error('[DuibiaoDisassemblyTextAPI] 工作流错误:', err)
        onError?.(err as Error)
      },
    })
  }
  catch (error) {
    console.error('[DuibiaoDisassemblyTextAPI] API 调用失败:', error)
    onError?.(error as Error)
  }
}