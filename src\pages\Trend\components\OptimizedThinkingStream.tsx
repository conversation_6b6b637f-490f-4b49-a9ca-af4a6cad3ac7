import type { StreamId } from './StreamingDataManager'
import { memo, useCallback, useEffect, useRef, useState } from 'react'
import { STREAM_IDS } from './StreamingDataManager'
import { StreamingThinkingStream } from './StreamingThinkingStream'

/**
 * Thinking流配置接口
 */
interface ThinkingStreamConfig {
  defaultExpanded: boolean
  autoStart: boolean
  className: string
  priority: 'high' | 'medium' | 'low' // 加载优先级
  description: string // 用于调试和文档
}

/**
 * 预定义的Thinking流配置
 * 根据不同业务场景优化用户体验
 */
const THINKING_STREAM_CONFIGS: Record<StreamId, ThinkingStreamConfig> = {
  [STREAM_IDS.COMBINED_THINKING1]: {
    defaultExpanded: true, // 默认展开
    autoStart: true,
    className: 'mt-4',
    priority: 'high',
    description: '聚合三个工作流的thinking数据',
  },
  [STREAM_IDS.PLANNING_THINKING1]: {
    defaultExpanded: true, // 策略规划重要，默认展开
    autoStart: true,
    className: 'mt-4',
    priority: 'high',
    description: 'planning_scheme工作流的thinking数据',
  },
  [STREAM_IDS.OPERATIONS_THINKING1]: {
    defaultExpanded: true, // 运营管理重要，默认展开
    autoStart: true,
    className: 'mt-4',
    priority: 'medium',
    description: 'distill_daren_list工作流的thinking数据',
  },
  [STREAM_IDS.OPERATIONS_THINKING]: {
    defaultExpanded: true, // 运营管理重要，默认展开
    autoStart: true,
    className: 'mt-4',
    priority: 'medium',
    description: 'OperationsManager的thinking数据',
  },
  [STREAM_IDS.INSIGHT_THINKING]: {
    defaultExpanded: true,
    autoStart: true,
    className: 'mt-4',
    priority: 'high',
    description: 'insight_report工作流的thinking数据',
  },
  [STREAM_IDS.PLANNING_THINKING]: {
    defaultExpanded: true,
    autoStart: true,
    className: 'mt-4',
    priority: 'medium',
    description: 'planning相关的thinking数据',
  },
  [STREAM_IDS.TEXT_LINK_THINKING]: {
    defaultExpanded: true, // 默认展开，直接显示thinking内容
    autoStart: true,
    className: 'mt-4',
    priority: 'high',
    description: 'text_link_disassemble工作流的thinking数据',
  },
  [STREAM_IDS.ORIGINAL_WORK_THINKING]: {
    defaultExpanded: true, // 默认展开
    autoStart: true,
    className: 'mt-4',
    priority: 'high',
    description: 'original_work工作流的thinking数据',
  },
}

/**
 * 条件渲染组件Props
 */
interface ConditionalThinkingStreamProps {
  streamId: StreamId
  show?: boolean
  dependencies?: any[]
  overrides?: Partial<ThinkingStreamConfig>
  onComplete?: () => void // 完成回调
}

/**
 * 条件渲染的Thinking流组件
 * 简化条件逻辑，统一渲染控制
 */
export const ConditionalThinkingStream = memo<ConditionalThinkingStreamProps>(({
  streamId,
  show = true,
  dependencies = [],
  overrides = {},
  onComplete,
}) => {
  /** 检查所有依赖条件 */
  const shouldShow = show && dependencies.every((dep) => {
    if (typeof dep === 'boolean')
      return dep
    if (typeof dep === 'string')
      return dep.trim().length > 0
    return !!dep
  })

  /** 合并配置 */
  const config = { ...THINKING_STREAM_CONFIGS[streamId], ...overrides }

  if (!shouldShow) {
    return null
  }

  return (
    <LazyThinkingStream
      streamId={ streamId }
      config={ config }
      onComplete={ onComplete }
    />
  )
})

ConditionalThinkingStream.displayName = 'ConditionalThinkingStream'

/**
 * 懒加载Thinking流组件Props
 */
interface LazyThinkingStreamProps {
  streamId: StreamId
  config: ThinkingStreamConfig
  onComplete?: () => void
}

/**
 * 懒加载的Thinking流组件
 * 使用Intersection Observer优化性能
 */
const LazyThinkingStream = memo<LazyThinkingStreamProps>(({
  streamId,
  config,
  onComplete,
}) => {
  const [isVisible, setIsVisible] = useState(false)
  const [hasBeenVisible, setHasBeenVisible] = useState(false)
  const ref = useRef<HTMLDivElement>(null)

  // Intersection Observer实现懒加载
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        const visible = entry.isIntersecting
        setIsVisible(visible)

        /** 一旦可见过，就标记为已可见（避免重复卸载/挂载） */
        if (visible && !hasBeenVisible) {
          setHasBeenVisible(true)
        }
      },
      {
        threshold: 0.1,
        rootMargin: '50px', // 提前50px开始加载
      },
    )

    if (ref.current) {
      observer.observe(ref.current)
    }

    return () => observer.disconnect()
  }, [hasBeenVisible])

  /** 高优先级组件立即渲染，低优先级组件懒加载 */
  const shouldRenderImmediately = config.priority === 'high'
  const shouldRender = shouldRenderImmediately || hasBeenVisible

  return (
    <div ref={ ref } className="min-h-[60px]">
      {shouldRender
        ? (
            <StreamingThinkingStream
              streamId={ streamId }
              className={ config.className }
              autoStart={ config.autoStart }
              defaultExpanded={ config.defaultExpanded }
              onComplete={ onComplete }
            />
          )
        : (
            <ThinkingPlaceholder config={ config } />
          )}
    </div>
  )
})

LazyThinkingStream.displayName = 'LazyThinkingStream'

/**
 * Thinking占位符组件
 */
const ThinkingPlaceholder = memo<{ config: ThinkingStreamConfig }>(({ config }) => (
  <div className={ `${config.className} animate-pulse` }>
    <div className="flex items-center rounded-lg bg-gray-50 p-3 space-x-2">
      <div className="h-4 w-4 rounded-full bg-gray-300"></div>
      <div className="flex-1 space-y-2">
        <div className="h-3 w-1/4 rounded bg-gray-300"></div>
        <div className="h-2 w-3/4 rounded bg-gray-200"></div>
      </div>
    </div>
  </div>
))

ThinkingPlaceholder.displayName = 'ThinkingPlaceholder'

/**
 * Thinking流管理器Props
 */
interface ThinkingStreamManagerProps {
  streams: Array<{
    streamId: StreamId
    show?: boolean
    dependencies?: any[]
    overrides?: Partial<ThinkingStreamConfig>
  }>
}

/**
 * Thinking流管理器组件
 * 统一管理多个thinking流的渲染
 */
export const ThinkingStreamManager = memo<ThinkingStreamManagerProps>(({ streams }) => {
  /** 按优先级排序，高优先级先渲染 */
  const sortedStreams = [...streams].sort((a, b) => {
    const priorityA = THINKING_STREAM_CONFIGS[a.streamId]?.priority || 'low'
    const priorityB = THINKING_STREAM_CONFIGS[b.streamId]?.priority || 'low'

    const priorityOrder = { high: 3, medium: 2, low: 1 }
    return priorityOrder[priorityB] - priorityOrder[priorityA]
  })

  return (
    <>
      {sortedStreams.map(({ streamId, show, dependencies, overrides }) => (
        <ConditionalThinkingStream
          key={ streamId }
          streamId={ streamId }
          show={ show }
          dependencies={ dependencies }
          overrides={ overrides }
        />
      ))}
    </>
  )
})

ThinkingStreamManager.displayName = 'ThinkingStreamManager'

/**
 * 预定义的常用组合
 */
export const CombinedThinkingStream = memo<{ show?: boolean }>(({ show = true }) => (
  <ConditionalThinkingStream
    streamId={ STREAM_IDS.COMBINED_THINKING1 }
    show={ show }
  />
))

export const PlanningThinkingStream = memo<{
  displayText?: string
  onComplete?: () => void
}>(({ displayText, onComplete }) => (
  <ConditionalThinkingStream
    streamId={ STREAM_IDS.PLANNING_THINKING1 }
    dependencies={ [displayText] }
    onComplete={ onComplete }
  />
))

export const OperationsThinkingStream1 = memo<{
  secondaryDisplayText?: string
  isStrategyApproved?: boolean
  onComplete?: () => void
}>(({ secondaryDisplayText, isStrategyApproved, onComplete }) => (
  <ConditionalThinkingStream
    streamId={ STREAM_IDS.OPERATIONS_THINKING1 }
    dependencies={ [secondaryDisplayText, isStrategyApproved] }
    onComplete={ onComplete }
  />
))

export const OperationsThinkingStream = memo<{
  operationsManagerDisplayText?: string
}>(({ operationsManagerDisplayText }) => (
  <ConditionalThinkingStream
    streamId={ STREAM_IDS.OPERATIONS_THINKING }
    dependencies={ [operationsManagerDisplayText] }
  />
))

export const TextLinkThinkingStream = memo<{
  show?: boolean
  onComplete?: () => void
}>(({ show = true, onComplete }) => (
  <ConditionalThinkingStream
    streamId={ STREAM_IDS.TEXT_LINK_THINKING }
    show={ show }
    onComplete={ onComplete }
  />
))

export const OriginalWorkThinkingStream = memo<{
  show?: boolean
  onComplete?: () => void
}>(({ show = true, onComplete }) => (
  <ConditionalThinkingStream
    streamId={ STREAM_IDS.ORIGINAL_WORK_THINKING }
    show={ show }
    onComplete={ onComplete }
  />
))

/**
 * 导出配置，供外部使用
 */
export { THINKING_STREAM_CONFIGS }
export type { ConditionalThinkingStreamProps, ThinkingStreamConfig }
