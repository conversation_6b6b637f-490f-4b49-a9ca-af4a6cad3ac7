/* eslint-disable style/multiline-ternary */
import cn from 'clsx'
import { AnimatePresence, motion } from 'framer-motion'
import { Send } from 'lucide-react'

import React, { useCallback, useEffect, useRef, useState } from 'react'
import { FileAPI } from '@/api/FileAPI'
import { DaRenListPreview } from '@/pages/Distribution/components/ReportComponents/DaRenListPreview'
import { PhonePreview } from '@/pages/Distribution/components/ReportComponents/PhonePreview'
import { request } from '@/utils'
import { ChatEventBus } from '../../ChatV2/constants'
import { getWorkflowExecutionParams } from '../stores/cozeStreamApi'

interface ImagePreviewCardProps {
  isOpen: boolean
  onClose: () => void
  data?: {
    title?: string
    content?: string
    image?: string
    tag?: string
    showPhoneFrame?: boolean // 添加标识，是否显示 PhoneFrame
    showDarenList?: boolean // 添加标识，是否显示达人列表
    darenListData?: any // 达人列表数据
    [key: string]: any
  }
  isLoading?: boolean // 添加 loading 状态
  loadingProgress?: Record<string, boolean> // 添加加载进度
}

export const ImagePreviewCard: React.FC<ImagePreviewCardProps> = ({
  isOpen,
  onClose,
  data,
  isLoading = false,
  loadingProgress = {},
}) => {
  const [selectedIndex, setSelectedIndex] = useState(0)
  const [isEditing, setIsEditing] = useState(false)
  const [editText, setEditText] = useState('')
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false)
  const [isUploading, setIsUploading] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isEditingImage, setIsEditingImage] = useState(false)
  const [hasAppliedPost, setHasAppliedPost] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  /** 使用 useRef 来跟踪上一次的数据，实现流式拼接 */
  const prevDataRef = useRef<{
    title?: string
    content?: string
    tag?: string
  }>({})

  /** 初始值 */
  const [tags, setTags] = useState<string[]>([])
  const [newTag, setNewTag] = useState('')
  const [title, setTitle] = useState('')
  const [bodyText, setBodyText] = useState('')
  /** 添加本地图片状态来管理从localStorage恢复的图片 */
  const [localImages, setLocalImages] = useState<string[]>([])

  /** 监听重置事件 */
  useEffect(() => {
    const handleReset = () => {
      /** 重置所有状态 */
      setTitle('')
      setBodyText('')
      setTags([])
      setLocalImages([])
      prevDataRef.current = {}
      setSelectedIndex(0)
      setIsSubmitting(false)
      setIsUploading(false)
      setIsEditingImage(false)
      setIsEditing(false)
      setHasAppliedPost(false)
      /** 清除永久禁用状态标记 */
      localStorage.removeItem('hasAppliedPost')
      /** 清除图片预览缓存 */
      localStorage.removeItem('imagePreviewData')
      console.log('[ImagePreviewCard] 已完全清除 imagePreviewData 缓存')
    }

    ChatEventBus.on('resetImagePreview', handleReset)

    return () => {
      ChatEventBus.off('resetImagePreview', handleReset)
    }
  }, [])

  /** 当卡片关闭时重置数据，当卡片打开时从 localStorage 恢复数据 */
  useEffect(() => {
    if (!isOpen) {
      /** 重置所有状态 */
      setTitle('')
      setBodyText('')
      setTags([])
      setLocalImages([])
      prevDataRef.current = {}
      setSelectedIndex(0)
      /** 重置按钮和编辑相关的状态 */
      setIsSubmitting(false)
      setIsUploading(false)
      setIsEditingImage(false)
      setIsEditing(false)
    }
    else {
      /** 卡片打开时，从缓存恢复数据 */
      const savedData = localStorage.getItem('imagePreviewData')
      if (savedData) {
        try {
          const parsed = JSON.parse(savedData)
          /** 如果传入的数据没有实质内容，或者是从其他卡片切换回来的，恢复完整数据 */
          const hasNoMeaningfulIncomingData = !data?.title && !data?.content && (!data?.images || data?.images?.length === 0)
          const isFromCardSwitch = data?.showPhoneFrame !== undefined || data?.showDarenList !== undefined
          if (hasNoMeaningfulIncomingData || isFromCardSwitch) {
            if (parsed.title && !title) {
              setTitle(parsed.title)
            }
            if (parsed.content && !bodyText) {
              setBodyText(parsed.content)
            }
            if (parsed.tag && tags.length === 0) {
              const restoredTags = parsed.tag.split(/\s+/).map((t: string) => t.trim()).filter((t: string) => t)
              setTags(restoredTags)
            }
            /** 恢复图片数据 */
            if (parsed.images && Array.isArray(parsed.images) && parsed.images.length > 0) {
              setLocalImages(parsed.images)
            }
            else if (parsed.image && typeof parsed.image === 'string' && parsed.image.includes('http')) {
              setLocalImages([parsed.image])
            }

            /** 恢复 prevDataRef，避免后续数据处理时的重复拼接 */
            prevDataRef.current = {
              title: parsed.title,
              content: parsed.content,
              tag: parsed.tag,
            }
          }
          else {
            console.log(`[ImagePreviewCard] ❌ 条件不满足，跳过数据恢复`)
          }
          /** 确保所有交互状态都被重置，但检查永久禁用状态 */
          setIsSubmitting(false)
          setIsUploading(false)
          setIsEditingImage(false)
          setIsEditing(false)
          /** 检查并恢复Apply Post永久禁用状态 */
          const appliedPostFlag = localStorage.getItem('hasAppliedPost')
          if (appliedPostFlag === 'true') {
            setHasAppliedPost(true)
          }
        }
        catch (e) {
          console.warn('[ImagePreviewCard] 恢复 localStorage 数据失败:', e)
        }
      }
    }
  }, [isOpen, data])

  /** 当 data 变化时进行流式拼接更新 */
  useEffect(() => {
    if (!isOpen)
      return // 如果卡片未打开，不处理数据

    // Title 流式拼接
    if (data?.title !== undefined && data.title !== '') {
      /** 如果是新的 title 数据，进行拼接 */
      if (prevDataRef.current.title !== data.title) {
        /** 如果之前没有 title，直接设置；否则检查是否需要拼接 */
        if (!prevDataRef.current.title) {
          setTitle(data.title)
        }
        else if (data.title.startsWith(prevDataRef.current.title)) {
          /** 新数据包含旧数据，直接替换（这是累积更新） */
          setTitle(data.title)
        }
        else {
          /** 新数据是增量，进行拼接 */
          setTitle(prev => prev + data.title)
        }
        prevDataRef.current.title = data.title
      }
    }

    // Content (Body copy) 流式拼接
    if (data?.content !== undefined && data.content !== '') {
      if (prevDataRef.current.content !== data.content) {
        if (!prevDataRef.current.content) {
          setBodyText(data.content)
        }
        else if (data.content.startsWith(prevDataRef.current.content)) {
          /** 新数据包含旧数据，直接替换（这是累积更新） */
          setBodyText(data.content)
        }
        else {
          /** 新数据是增量，进行拼接 */
          setBodyText(prev => prev + data.content)
        }
        prevDataRef.current.content = data.content
      }
    }

    // Tags 流式拼接
    if (data?.tag !== undefined && data.tag !== '') {
      if (prevDataRef.current.tag !== data.tag) {
        // Tags 可能是空格分隔或逗号分隔，智能解析
        let newTags: string[] = []

        /** 先尝试以 # 为分隔符（如果包含 #） */
        if (data.tag.includes('#')) {
          newTags = data.tag.split(/\s+/).filter(t => t.startsWith('#') && t.length > 1)
        }
        else if (data.tag.includes(',')) {
          /** 以逗号分隔 */
          newTags = data.tag.split(',').map((t: string) => t.trim()).filter(t => t)
        }
        else {
          /** 以空格分隔 */
          newTags = data.tag.split(/\s+/).map((t: string) => t.trim()).filter(t => t)
        }
        setTags(newTags)
        prevDataRef.current.tag = data.tag
      }
    }
  }, [data, isOpen])

  /** 数据持久化 - 当任何核心数据发生变化时保存到 localStorage */
  useEffect(() => {
    if (isOpen && (title || bodyText || tags.length > 0 || localImages.length > 0)) {
      const currentImages = (() => {
        /** 优先使用本地图片状态 */
        if (localImages && localImages.length > 0) {
          return localImages
        }
        if (data?.images && Array.isArray(data.images) && data.images.length > 0) {
          return data.images
        }
        if (data?.image) {
          if (Array.isArray(data.image) && data.image.length > 0) {
            return data.image
          }
          else if (typeof data.image === 'string' && data.image.includes('http')) {
            return [data.image]
          }
        }
        return []
      })()

      const dataToSave = {
        title,
        content: bodyText,
        tag: tags.join(' '),
        images: currentImages,
        image: currentImages.length > 0
          ? currentImages[0]
          : '',
        pic_url: currentImages.length > 0
          ? currentImages[0]
          : '',
        ...data, // 保留其他原有数据
        /** 明确排除按钮状态，避免持久化交互状态 */
        isSubmitting: undefined,
        isUploading: undefined,
        isEditingImage: undefined,
        isEditing: undefined,
      }

      localStorage.setItem('imagePreviewData', JSON.stringify(dataToSave))
    }
  }, [title, bodyText, tags, data, isOpen, localImages])

  /** 专门监听组件打开状态变化，确保交互状态正确重置 */
  useEffect(() => {
    if (isOpen) {
      /** 每次组件打开时都重置临时交互状态，但保持永久禁用状态 */
      setIsSubmitting(false)
      setIsUploading(false)
      setIsEditingImage(false)
      setIsEditing(false)

      /** 检查并恢复Apply Post永久禁用状态 */
      const appliedPostFlag = localStorage.getItem('hasAppliedPost')
      if (appliedPostFlag === 'true') {
        setHasAppliedPost(true)
      }
      else {
        setHasAppliedPost(false)
      }
    }
  }, [isOpen])

  /** 优先使用本地恢复的图片，其次使用传入的 images 数组，最后从 localStorage 实时恢复 */
  const images = (() => {
    /** 首先检查本地恢复的图片状态 */
    if (localImages && localImages.length > 0) {
      return localImages
    }

    /** 如果有传入的 images 数组，直接使用 */
    if (data?.images && Array.isArray(data.images) && data.images.length > 0) {
      return data.images
    }

    /** 如果有 image 字段，判断其格式 */
    if (data?.image) {
      /** 如果已经是数组，直接使用（来自cozeStreamApi解析后的数据） */
      if (Array.isArray(data.image) && data.image.length > 0) {
        return data.image
      }
      /** 如果是字符串且包含 http，直接使用 */
      else if (typeof data.image === 'string' && data.image.includes('http')) {
        return [data.image]
      }
      /** 如果是字符串但可能是JSON格式，尝试解析 */
      else if (typeof data.image === 'string') {
        try {
          /** 处理转义字符的JSON字符串 */
          let imageString = data.image

          /** 如果字符串包含转义的引号，先处理转义 */
          if (imageString.includes('\\"')) {
            /** 替换转义的引号为正常引号 */
            imageString = imageString.replace(/\\"/g, '"')
          }

          const parsedImages = JSON.parse(imageString)
          if (Array.isArray(parsedImages) && parsedImages.length > 0) {
            return parsedImages
          }
        }
        catch (e) {
          /** 如果解析失败，作为普通字符串处理 */
          console.warn('[ImagePreviewCard] JSON解析失败，作为普通字符串处理:', data.image, '错误:', e)
        }
        return [data.image]
      }
    }

    /** 如果上述都没有，尝试从 localStorage 恢复图片数据 */
    const savedData = localStorage.getItem('imagePreviewData')

    if (savedData) {
      try {
        const parsed = JSON.parse(savedData)

        if (parsed.images && Array.isArray(parsed.images) && parsed.images.length > 0) {
          return parsed.images
        }
        if (parsed.image && typeof parsed.image === 'string' && parsed.image.includes('http')) {
          return [parsed.image]
        }
      }
      catch (e) {
        console.warn(`[ImagePreviewCard] 从 localStorage 恢复图片数据失败:`, e)
      }
    }

    return []
  })()

  const handleDownload = useCallback(() => {
    const link = document.createElement('a')
    link.href = images[selectedIndex]
    link.download = `image-${selectedIndex + 1}.jpg`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }, [images, selectedIndex])

  const handleDelete = useCallback(() => {
    if (images.length > 0) {
      const updatedImages = images.filter((_, index) => index !== selectedIndex)

      /** 调整选中索引 */
      const newSelectedIndex = selectedIndex >= updatedImages.length
        ? Math.max(0, updatedImages.length - 1)
        : selectedIndex

      setSelectedIndex(newSelectedIndex)

      /** 通过事件总线更新图片数据 */
      ChatEventBus.emit('updateImagePreview', {
        ...data,
        images: updatedImages,
        image: updatedImages.length > 0
          ? updatedImages[0]
          : '',
      })
    }
    setShowDeleteConfirm(false)
  }, [selectedIndex, images, data])

  const handleSendEdit = useCallback(async () => {
    if (!editText.trim()) {
      console.warn('编辑内容为空')
      return
    }

    const currentImage = images[selectedIndex]
    if (!currentImage) {
      console.warn('没有选中的图片')
      return
    }

    try {
      setIsEditing(false) // 关闭编辑框
      setIsEditingImage(true) // 开始图片编辑loading

      /** 获取必要参数 */
      const taskInstanceId = localStorage.getItem('confirmed_taskInstanceId')
        || localStorage.getItem('taskInstanceId') || ''

      if (!taskInstanceId) {
        console.error('缺少 taskInstanceId')
        setIsEditingImage(false)
        return
      }

      /** 获取token - 使用与其他API相同的方式 */
      const getUserToken = async (): Promise<string> => {
        try {
          const { userStore } = await import('@/store/userStore')
          const token = userStore.token
          if (!token) {
            throw new Error('用户未登录，无法获取认证token')
          }
          return token
        }
        catch (error) {
          console.error('[ImageEdit] 获取用户token失败:', error)
          /** 降级到localStorage获取 */
          return localStorage.getItem('access_token') || ''
        }
      }
      const token = await getUserToken()

      if (!token) {
        console.error('[ImageEdit] 无法获取认证token')
        setIsEditingImage(false)
        setIsEditing(true)
        return
      }

      const apiUrl = import.meta.env.VITE_API_BASE_URL || '/api'
      const fullUrl = `${apiUrl}/app/market/stream/execute-image-modify`

      /** 构造请求参数 */
      const submitData = {
        taskInstanceId,
        workflowName: 'modify_pic',
        parameters: {
          fix_prompt: editText.trim(),
          pic_url: currentImage,
        },
        platform: 'rednote',
      }
      /** 使用流式响应处理 */
      const { fetchEventSource } = await import('@microsoft/fetch-event-source')

      /** 存储流式响应数据 */
      const responseData: Record<string, any> = {}
      const fragments: Record<string, Record<number, string>> = {}

      fetchEventSource(fullUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(submitData),
        async onopen(response) {
          if (!response.ok) {
            console.error('[ImageEdit] 请求失败，状态码:', response.status)
            if (response.status === 401) {
              console.error('[ImageEdit] 认证失败，检查token是否正确')
            }
          }
        },
        onmessage(ev) {
          if (!ev.data || ev.event === 'end')
            return

          try {
            const parsed = JSON.parse(ev.data)

            /** 处理 meta 事件 */
            if (ev.event === 'meta') {
              return
            }

            /** 处理 Message 事件 */
            if (ev.event === 'Message' && parsed.node_title) {
              const { content, node_title: nodeTitle, node_seq_id: seqId, node_is_finish: isFinish } = parsed

              /** 收集流式数据片段 */
              if (!fragments[nodeTitle]) {
                fragments[nodeTitle] = {}
              }
              fragments[nodeTitle][Number.parseInt(seqId)] = content

              /** 如果节点完成，组装完整内容 */
              if (isFinish) {
                const fragmentKeys = Object.keys(fragments[nodeTitle]).map(k => Number.parseInt(k)).sort((a, b) => a - b)
                const fullContent = fragmentKeys.map(key => fragments[nodeTitle][key]).join('')

                /** 特殊处理image节点 */
                let processedContent = fullContent
                if (nodeTitle === 'image' && fullContent) {
                  try {
                    /** 处理转义字符 */
                    let imageString = fullContent
                    if (imageString.includes('\\"')) {
                      imageString = imageString.replace(/\\"/g, '"')
                    }

                    /** 尝试解析JSON数组 */
                    const parsedImages = JSON.parse(imageString)
                    if (Array.isArray(parsedImages) && parsedImages.length > 0) {
                      processedContent = parsedImages

                      /** 更新图片数据 - 替换当前编辑的图片 */
                      const newImageUrl = parsedImages[0] // 使用第一张图片
                      const updatedImages = [...images]
                      updatedImages[selectedIndex] = newImageUrl

                      /** 通过事件总线更新图片数据 */
                      ChatEventBus.emit('updateImagePreview', {
                        ...data,
                        images: updatedImages,
                        image: updatedImages.length > 0
                          ? updatedImages[0]
                          : newImageUrl,
                      })

                      console.log('[ImageEdit] 图片替换成功:', newImageUrl)
                      setIsEditingImage(false) // 结束loading状态
                    }
                  }
                  catch (e) {
                    console.warn('[ImageEdit] image解析失败，使用原始内容:', fullContent)
                  }
                }

                responseData[nodeTitle] = processedContent
              }
            }
          }
          catch (error) {
            console.error('[ImageEdit] 解析SSE数据失败:', error)
          }
        },
        onerror(err) {
          console.error('[ImageEdit] SSE连接错误:', err)
          console.error('[ImageEdit] 错误详情:', {
            message: err.message,
            type: err.constructor.name,
            stack: err.stack,
          })
          setIsEditingImage(false)
          setIsEditing(true) // 重新打开编辑框让用户可以重试
        },
        onclose() {
          console.log('[ImageEdit] SSE连接已关闭')
          setIsEditingImage(false)
        },
      })

      /** 清空输入框 */
      setEditText('')
    }
    catch (error) {
      console.error('图片编辑请求失败:', error)
      setIsEditingImage(false)
      setIsEditing(true) // 重新打开编辑框让用户可以重试
    }
  }, [editText, images, selectedIndex, data])

  const handleAddTag = useCallback(() => {
    if (newTag.trim()) {
      setTags([...tags, newTag.trim()])
      setNewTag('')
    }
  }, [newTag, tags])

  const handleRemoveTag = useCallback((index: number) => {
    setTags(tags.filter((_, i) => i !== index))
  }, [tags])

  /** 处理图片上传 */
  const handleUpload = useCallback(async () => {
    if (!fileInputRef.current)
      return

    const file = fileInputRef.current.files?.[0]
    if (!file)
      return

    setIsUploading(true)
    try {
      /** 调用 oss/download-sign-url 接口上传图片 */
      const result = await FileAPI.upFileToUrl([file])
      if (result.downloadLoadFileDetails && result.downloadLoadFileDetails.length > 0) {
        const uploadedImageUrl = result.downloadLoadFileDetails[0].url

        /** 将上传的图片添加到images数组中 */
        const currentImages = images.length > 0
          ? images
          : []
        const updatedImages = [...currentImages, uploadedImageUrl]

        /** 更新本地图片状态 */
        setLocalImages(updatedImages)

        /** 通过事件总线更新图片数据 */
        ChatEventBus.emit('updateImagePreview', {
          ...data,
          images: updatedImages,
          image: uploadedImageUrl, // 同时更新单图字段
        })
      }
    }
    catch (error) {
      console.error('图片上传失败:', error)
      /** 这里可以添加错误提示 */
    }
    finally {
      setIsUploading(false)
      /** 清空文件输入 */
      if (fileInputRef.current) {
        fileInputRef.current.value = ''
      }
    }
  }, [images, data])

  /** 处理Apply Post按钮点击 */
  const handleApplyPost = useCallback(async () => {
    setIsSubmitting(true)

    try {
      /** 获取必要的参数 */
      let taskInstanceId = ''
      let executionId = ''

      /** 尝试从缓存中获取 taskInstanceId */
      taskInstanceId = localStorage.getItem('confirmed_taskInstanceId')
        || localStorage.getItem('taskInstanceId') || ''

      /** 优先从当前请求的工作流meta中获取executionId */
      /** 尝试从工作流执行参数中获取（这些参数是从工作流响应的type=meta中提取的） */
      const workflowParams = getWorkflowExecutionParams('original_work')
        || getWorkflowExecutionParams('duibiao_disassembly_text')

      console.error('workflowParams', workflowParams)

      if (workflowParams) {
        taskInstanceId = workflowParams.taskInstanceId || taskInstanceId
        executionId = workflowParams.executionId
      }

      if (!taskInstanceId) {
        console.error('缺少 taskInstanceId，无法提交')
        return
      }

      if (!executionId) {
        console.error('缺少 executionId，无法提交，请确保已完成工作流请求并获取到meta信息')
        return
      }

      /** 准备提交的数据 */
      const finalContent = {
        content: bodyText, // 拿Body copy输入框里面的内容
        title, // 拿Title输入框里面的内容
        tags: tags.filter(tag => tag.trim()), // 拿Tags的每个标签 数组包起来，过滤空标签
        image: images, // 拿图片坑位中的全部图片
      }

      const submitData = {
        taskInstanceId,
        executionId,
        finalContent,
        confirmReason: '', // 写死空字符串
      }

      console.log('准备提交数据:', submitData)

      /** 调用确认接口 */
      await request.post('/app/market/confirm-final-content', submitData)

      /** Apply Post 成功后，设置永久禁用状态 */
      setHasAppliedPost(true)
      localStorage.setItem('hasAppliedPost', 'true')

      /** Apply Post 成功后，更新缓存数据 */
      const updatedContent = {
        title: finalContent.title,
        content: finalContent.content,
        tag: finalContent.tags.join(' '),
        images: finalContent.image, // 这是用户确认提交的图片数组
        image: finalContent.image.length > 0
          ? finalContent.image[0]
          : '',
        pic_url: finalContent.image.length > 0
          ? finalContent.image[0]
          : '',
      }
      /** 更新缓存 */
      localStorage.setItem('imagePreviewData', JSON.stringify(updatedContent))
      /** Apply Post成功后，自动触发Post Content Review卡片的点击 */
      const phoneFrameData = {
        title: title || '',
        content: bodyText || '',
        tag: tags.join(' ') || '',
        showPhoneFrame: true,
        showDarenList: false, // 明确关闭达人列表模式
        image: images.length > 0
          ? images[0]
          : '',
        images, // 传递完整的图片数组
        pic_url: images.length > 0
          ? images[0]
          : '',
      }
      /** 在切换卡片前先重置提交状态，确保按钮能正常使用 */
      setIsSubmitting(false)
      ChatEventBus.emit('updateImagePreview', phoneFrameData)
    }
    catch (error) {
      console.error('Apply Post 失败:', error)
      setIsSubmitting(false) // 确保出错时也重置状态
    }
  }, [title, bodyText, tags, images, data?.image])

  const handlePrevImage = useCallback(() => {
    setSelectedIndex(prev => (prev > 0
      ? prev - 1
      : images.length - 1))
    /** 切换图片时退出编辑模式 */
    setIsEditing(false)
    setEditText('')
  }, [images.length])

  const handleNextImage = useCallback(() => {
    setSelectedIndex(prev => (prev < images.length - 1
      ? prev + 1
      : 0))
    /** 切换图片时退出编辑模式 */
    setIsEditing(false)
    setEditText('')
  }, [images.length])

  return (
    <AnimatePresence mode="wait">
      {isOpen && (
        <motion.div
          key="image-preview-card"
          initial={ { x: '100%' } }
          animate={ { x: 0 } }
          exit={ { x: '100%' } }
          transition={ { type: 'spring', damping: 25, stiffness: 300 } }
          className="h-full w-full flex flex-col overflow-hidden rounded-lg bg-white shadow-xl"
        >
          {/* Header */}
          <div className="flex items-center justify-between border-b bg-white px-5 py-4">
            <div className="flex items-center gap-4">
              <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={ 2 }
                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                />
              </svg>
              <h2 className="text-base font-semibold">
                {data?.showDarenList
                  ? 'Influencer Collaboration'
                  : data?.showPhoneFrame
                    ? 'Post Content Review'
                    : 'Post Content'}
              </h2>

              {/* 切换按钮 - 只在有数据且不是默认Post Content模式时显示 */}
              {(data?.showDarenList || data?.showPhoneFrame) && (
                <div className="ml-4 flex items-center gap-2">
                  <button
                    onClick={ () => {
                      /** 切换到 Post Content Review */

                      const phoneFrameData = {
                        title: title || data?.title || '',
                        content: bodyText || data?.content || '',
                        tag: tags.length > 0
                          ? tags.join(' ')
                          : data?.tag || '',
                        showPhoneFrame: true,
                        showDarenList: false, // 明确关闭达人列表模式
                        image: images.length > 0
                          ? images[0]
                          : data?.image || '',
                        images: images.length > 0
                          ? images
                          : data?.images || [], // 优先使用当前图片数组
                        pic_url: images.length > 0
                          ? images[0]
                          : data?.pic_url || '',
                        darenListData: data?.darenListData, // 保留达人数据以便切换回来
                      }
                      console.log('[ImagePreviewCard] 发送 phoneFrameData:', phoneFrameData)
                      ChatEventBus.emit('updateImagePreview', phoneFrameData)
                    } }
                    className={ cn(
                      'px-3 py-1 rounded-md text-sm font-medium transition-colors',
                      data?.showPhoneFrame && !data?.showDarenList
                        ? 'bg-blue-100 text-blue-700'
                        : 'bg-gray-100 text-gray-600 hover:bg-gray-200',
                    ) }
                  >
                    Post Content
                  </button>
                  <button
                    onClick={ () => {
                      /** 切换到 Influencer Collaboration */
                      console.log('[ImagePreviewCard] 切换到 Influencer Collaboration，当前数据:', data)
                      const darenData = {
                        showDarenList: true,
                        showPhoneFrame: false, // 明确关闭手机预览模式
                        darenListData: data?.darenListData || { KOC: [], KOL: [], suren: [] },
                        /** 保留其他数据以便切换回来 - 优先使用当前状态 */
                        title: title || data?.title,
                        content: bodyText || data?.content,
                        tag: tags.length > 0
                          ? tags.join(' ')
                          : data?.tag,
                        image: images.length > 0
                          ? images[0]
                          : data?.image,
                        images: images.length > 0
                          ? images
                          : data?.images || [], // 优先使用当前图片数组
                        pic_url: images.length > 0
                          ? images[0]
                          : data?.pic_url,
                      }
                      console.log('[ImagePreviewCard] 发送 darenData:', darenData)
                      ChatEventBus.emit('updateImagePreview', darenData)
                    } }
                    className={ cn(
                      'px-3 py-1 rounded-md text-sm font-medium transition-colors',
                      data?.showDarenList
                        ? 'bg-blue-100 text-blue-700'
                        : 'bg-gray-100 text-gray-600 hover:bg-gray-200',
                    ) }
                  >
                    Influencers
                  </button>
                </div>
              )}
            </div>
            <button
              onClick={ onClose }
              className="rounded-full p-1 transition-colors hover:bg-gray-100"
            >
              <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={ 2 } d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* Content - 横向布局 */}
          <div className="flex flex-1 overflow-hidden">
            {/* Left: Thumbnail List - 纵向排列 - 仅在非 PhoneFrame 和非达人列表模式下显示 */}
            {!data?.showPhoneFrame && !data?.showDarenList && (
              <div className="w-28 flex-shrink-0 overflow-y-auto border-r border-gray-200 p-4">
                <div className="space-y-2">
                  {images.length > 0 ? images.map((img, index) => (
                    <button
                      key={ index }
                      onClick={ () => {
                        setSelectedIndex(index)
                        /** 切换图片时退出编辑模式 */
                        setIsEditing(false)
                        setEditText('')
                      } }
                      className={ cn(
                        'relative w-full aspect-square rounded-lg overflow-hidden border-2 transition-all',
                        selectedIndex === index
                          ? 'border-blue-500 ring-2 ring-blue-200'
                          : 'border-gray-200 hover:border-gray-300',
                      ) }
                    >
                      <img src={ img } alt={ `Thumbnail ${index + 1}` } className="h-full w-full object-cover" />
                      {index === 0 && (
                        <div className="absolute left-1 top-1 rounded bg-white/90 px-1 text-[9px] text-gray-700 font-medium">
                          Cover
                        </div>
                      )}
                      {/* 缩略图编辑loading状态 */}
                      {isEditingImage && selectedIndex === index && (
                        <div className="absolute inset-0 flex items-center justify-center bg-black/40">
                          <div className="h-3 w-3 animate-spin border-2 border-white border-t-transparent rounded-full"></div>
                        </div>
                      )}
                    </button>
                  )) : (
                    <div className="aspect-square w-full flex items-center justify-center border-2 border-gray-200 rounded-lg">
                      <div className="text-center">
                        <svg className="mx-auto h-8 w-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={ 2 } d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                        <p className="mt-1 text-[9px] text-gray-500">等待生成</p>
                      </div>
                    </div>
                  )}

                  {/* Upload button */}
                  <div className="relative">
                    <input
                      ref={ fileInputRef }
                      type="file"
                      accept="image/*"
                      onChange={ handleUpload }
                      className="absolute inset-0 h-full w-full cursor-pointer opacity-0"
                    />
                    <button
                      className="aspect-square w-full flex flex-col items-center justify-center border-2 border-gray-300 rounded-lg border-dashed transition-all hover:border-gray-400 hover:bg-gray-100"
                      disabled={ isUploading }
                    >
                      {isUploading
                        ? (
                            <div className="h-4 w-4 animate-spin border-2 border-gray-400 border-t-transparent rounded-full"></div>
                          )
                        : (
                            <svg className="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={ 2 } d="M12 4v16m8-8H4" />
                            </svg>
                          )}
                      <span className="mt-1 text-[9px] text-gray-500">
                        {isUploading
                          ? 'Uploading...'
                          : 'Upload'}
                      </span>
                    </button>
                  </div>
                </div>
              </div>
            )}

            {/* Right: Main Content - White Background */}
            <div className="flex-1 overflow-y-auto bg-white">
              <div className="p-5 space-y-5">
                {/* Title Section - 根据模式显示不同内容 */}
                {!data?.showDarenList && (
                  <div>
                    <h3 className="mb-2 text-lg font-semibold">
                      {data?.showPhoneFrame
                        ? 'Post Content Review'
                        : 'Post Content Review'}
                    </h3>
                    <p className="text-sm text-gray-600">
                      {data?.showPhoneFrame
                        ? 'Preview how your content will appear on RedNote. Make final adjustments before publishing.'
                        : 'I\'ve created post content based on your brand strategy. Review and edit the title and copy to match your voice.'}
                    </p>
                  </div>
                )}

                {/* Main Content - 根据不同模式显示不同内容 */}
                {data?.showDarenList ? (
                  /* 显示达人列表 */
                  <div className="h-full">
                    <DaRenListPreview
                      data={ data.darenListData || { KOC: [], KOL: [], suren: [] } }
                      className="h-full"
                    />
                  </div>

                ) : data?.showPhoneFrame ? (
                  /* 显示 PhonePreview */
                  <div className="flex items-center justify-center py-8" style={ { minHeight: '700px' } }>
                    <PhonePreview
                      title={ title || data?.title || '👸 10秒自测! 别再买错粉底液' }
                      content={ bodyText || data?.content || '偷偷告诉你! 知名化妆师是不是每次粉底液都用的很快？💄 最近看到一百多粉二百？做挑战？ 热啊过法青春 泡泼绝对...' }
                      imageUrl={ images.length > 0
                        ? images
                        : [data?.image || ''] }
                      author="PhotoG"
                      authorAvatar="/src/assets/image/home/<USER>/avatar.png"
                      likes={ Math.floor(Math.random() * 1000) + 100 }
                      comments={ Math.floor(Math.random() * 100) + 10 }
                      scale={ 0.85 }
                    />
                  </div>
                ) : (
                  /* 原来的图片显示逻辑 */
                  <div className="relative overflow-hidden rounded-lg bg-white shadow-sm" style={ { height: '687px', margin: '0 auto' } }>
                    {isLoading && !loadingProgress.image
                      ? (
                          <div className="h-full w-full flex items-center justify-center bg-gray-50">
                            <div className="text-center">
                              <div className="mx-auto mb-4 h-12 w-12 animate-spin border-4 border-gray-300 border-t-blue-500 rounded-full"></div>
                              <p className="text-gray-500">正在生成图片...</p>
                            </div>
                          </div>
                        )
                      : images.length > 0
                        ? (
                            <div className="relative h-full w-full">
                              <img
                                src={ images[selectedIndex] }
                                alt="Main preview"
                                className="h-full w-full object-contain"
                              />
                              {/* 编辑loading遮罩 */}
                              {isEditingImage && (
                                <div className="absolute inset-0 flex items-center justify-center bg-black/20">
                                  <div className="rounded-lg bg-white p-4 text-center shadow-lg">
                                    <div className="mx-auto mb-3 h-8 w-8 animate-spin border-4 border-gray-300 border-t-blue-500 rounded-full"></div>
                                    <p className="text-sm text-gray-700 font-medium">正在修改图片...</p>
                                  </div>
                                </div>
                              )}
                            </div>
                          )
                        : (
                            <div className="h-full w-full flex items-center justify-center bg-gray-50">
                              <div className="text-center">
                                <svg className="mx-auto mb-4 h-16 w-16 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={ 2 } d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                </svg>
                                <p className="text-gray-500">等待图片生成...</p>
                              </div>
                            </div>
                          )}

                    {/* Image Counter - Top Right */}
                    <div className="absolute right-3 top-3 rounded-lg bg-black/60 px-3 py-1 text-sm text-white font-medium">
                      {selectedIndex + 1}
                      /
                      {images.length}
                    </div>

                    {/* Navigation Arrows */}
                    <button
                      onClick={ handlePrevImage }
                      className="absolute left-3 top-1/2 rounded-full bg-white/80 p-1.5 shadow-md transition-all -translate-y-1/2 hover:bg-white"
                    >
                      <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={ 2 } d="M15 19l-7-7 7-7" />
                      </svg>
                    </button>
                    <button
                      onClick={ handleNextImage }
                      className="absolute right-3 top-1/2 rounded-full bg-white/80 p-1.5 shadow-md transition-all -translate-y-1/2 hover:bg-white"
                    >
                      <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={ 2 } d="M9 5l7 7-7 7" />
                      </svg>
                    </button>

                    {/* Action Icons - Bottom Left - Vertical Layout - 36x36 with 16x16 icons */}
                    <div className="absolute bottom-3 left-3 flex flex-col gap-2">
                      {/* Edit Button */}
                      <button
                        onClick={ () => setIsEditing(true) }
                        disabled={ isEditingImage }
                        className="rounded-full bg-white/90 shadow-md transition-all disabled:cursor-not-allowed hover:bg-white disabled:opacity-50"
                        style={ { width: '36px', height: '36px', display: 'flex', alignItems: 'center', justifyContent: 'center' } }
                        title="Edit"
                      >
                        <svg style={ { width: '16px', height: '16px' } } className="text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={ 2 }
                            d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                          />
                        </svg>
                      </button>

                      {/* Download Button */}
                      <button
                        onClick={ handleDownload }
                        className="rounded-full bg-white/90 shadow-md transition-all hover:bg-white"
                        style={ { width: '36px', height: '36px', display: 'flex', alignItems: 'center', justifyContent: 'center' } }
                        title="Download"
                      >
                        <svg style={ { width: '16px', height: '16px' } } className="text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={ 2 }
                            d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"
                          />
                        </svg>
                      </button>

                      {/* Delete Button - Red Color */}
                      <button
                        onClick={ () => setShowDeleteConfirm(true) }
                        className="rounded-full bg-white/90 shadow-md transition-all hover:bg-white"
                        style={ { width: '36px', height: '36px', display: 'flex', alignItems: 'center', justifyContent: 'center' } }
                        title="Delete"
                      >
                        <svg style={ { width: '16px', height: '16px' } } className="text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={ 2 }
                            d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                          />
                        </svg>
                      </button>
                    </div>

                    {/* Edit Input - Centered Below Image */}
                    {isEditing && (
                      <motion.div
                        initial={ { opacity: 0, y: 20 } }
                        animate={ { opacity: 1, y: 0 } }
                        className="absolute bottom-3 left-0 right-0 flex justify-center"
                      >
                        <div
                          className="flex items-center gap-3 shadow-xl"
                          style={ {
                            width: '521px',
                            height: '69px',
                            padding: '2px',
                            background: 'linear-gradient(90deg, #DD9DFF 0%, #36D3FF 100%)',
                            borderRadius: '16px',
                          } }
                        >
                          <div className="h-full w-full flex items-center gap-3 bg-white px-6" style={ { borderRadius: '14px' } }>
                            <input
                              type="text"
                              value={ editText }
                              onChange={ e => setEditText(e.target.value) }
                              placeholder="Ask what's on your mind"
                              className="flex-1 bg-transparent text-center text-base outline-none placeholder-gray-400"
                              autoFocus
                              onKeyDown={ (e) => {
                                if (e.key === 'Escape') {
                                  setIsEditing(false)
                                  setEditText('')
                                }
                                else if (e.key === 'Enter') {
                                  handleSendEdit()
                                }
                              } }
                            />
                            <button
                              onClick={ handleSendEdit }
                              className="rounded-full p-2 transition-colors hover:bg-gray-100"
                            >
                              <Send size={ 20 } className="text-gray-500" />
                            </button>
                          </div>
                        </div>
                      </motion.div>
                    )}
                  </div>
                )}

                {/* Post Content Form - 只在非 PhoneFrame 和非达人列表模式下显示 */}
                {!data?.showPhoneFrame && !data?.showDarenList && (
                  <div className="rounded-lg bg-white p-5 shadow-sm space-y-4">
                    <h4 className="text-gray-900" style={ { fontWeight: 700, fontSize: '16px' } }>Post content</h4>

                    {/* Title */}
                    <div>
                      <label className="mb-1 block text-gray-700" style={ { fontWeight: 700, fontSize: '16px' } }>Title</label>
                      {isLoading && !loadingProgress.title
                        ? (
                            <>
                              <div className="h-10 animate-pulse rounded-lg bg-gray-200"></div>
                              <div className="mt-1 text-right text-xs text-gray-400">0 / 100</div>
                            </>
                          )
                        : (
                            <>
                              <input
                                type="text"
                                className="w-full border border-gray-200 rounded-lg bg-gray-50 p-2.5 text-sm focus:border-transparent focus:outline-none focus:ring-2 focus:ring-blue-500"
                                value={ title }
                                onChange={ (e) => {
                                  if (e.target.value.length <= 100) {
                                    setTitle(e.target.value)
                                  }
                                } }
                                maxLength={ 100 }
                              />
                              <div className="mt-1 text-right text-xs text-gray-400">
                                {title.length}
                                {' '}
                                / 100
                              </div>
                            </>
                          )}
                    </div>

                    {/* Body copy */}
                    <div>
                      <label className="mb-1 block text-gray-700" style={ { fontWeight: 700, fontSize: '16px' } }>Body copy</label>
                      {isLoading && !loadingProgress.content
                        ? (
                            <>
                              <div className="h-24 animate-pulse rounded-lg bg-gray-200"></div>
                              <div className="mt-1 text-right text-xs text-gray-400">0 / 100</div>
                            </>
                          )
                        : (
                            <>
                              <textarea
                                className="w-full resize-none border border-gray-200 rounded-lg bg-gray-50 p-2.5 text-sm focus:border-transparent focus:outline-none focus:ring-2 focus:ring-blue-500"
                                rows={ 5 }
                                value={ bodyText }
                                onChange={ e => setBodyText(e.target.value) }
                              />
                              <div className="mt-1 text-right text-xs text-gray-400">
                                {bodyText.length}
                                {' '}
                                / 100
                              </div>
                            </>
                          )}
                    </div>

                    {/* Tags */}
                    <div>
                      <label className="mb-2 block text-gray-700" style={ { fontWeight: 700, fontSize: '16px' } }>Tags</label>
                      {isLoading && !loadingProgress.tag
                        ? (
                            <div className="mb-3">
                              <div className="flex gap-2">
                                <div className="h-7 w-20 animate-pulse rounded-full bg-gray-200"></div>
                                <div className="h-7 w-24 animate-pulse rounded-full bg-gray-200"></div>
                                <div className="h-7 w-20 animate-pulse rounded-full bg-gray-200"></div>
                              </div>
                            </div>
                          )
                        : (
                            <div className="mb-3 flex flex-wrap gap-2">
                              {tags.map((tag, index) => (
                                <span key={ index } className="inline-flex items-center gap-1 rounded-full bg-blue-50 px-3 py-1 text-xs text-blue-600">
                                  <span>{tag}</span>
                                  <button
                                    onClick={ () => {
                                      setTags(tags.filter((_, i) => i !== index))
                                    } }
                                    className="hover:text-blue-800"
                                  >
                                    <svg className="h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={ 2 } d="M6 18L18 6M6 6l12 12" />
                                    </svg>
                                  </button>
                                </span>
                              ))}
                            </div>
                          )}
                      {!isLoading && (
                        <div className="flex gap-2" style={ { width: '400px' } }>
                          <input
                            type="text"
                            maxlength="8"
                            placeholder="Enter tag name (without #)"
                            value={ newTag }
                            onChange={ e => setNewTag(e.target.value) }
                            onKeyDown={ (e) => {
                              if (e.key === 'Enter' && newTag.trim()) {
                                setTags([...tags, newTag.trim()])
                                setNewTag('')
                              }
                            } }
                            className="flex-1 border border-gray-200 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                          />
                          <button
                            onClick={ () => {
                              if (newTag.trim()) {
                                setTags([...tags, newTag.trim()])
                                setNewTag('')
                              }
                            } }
                            className="border border-gray-300 rounded-lg px-4 py-2 text-sm text-gray-700 transition-colors hover:bg-gray-50"
                          >
                            Add
                          </button>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* Apply Button - Bottom Right Corner - 只在非 PhoneFrame 和非达人列表模式下显示 */}
                {!data?.showPhoneFrame && !data?.showDarenList && (
                  <div className="flex justify-end">
                    <button
                      onClick={ handleApplyPost }
                      disabled={ isSubmitting || hasAppliedPost }
                      className="rounded-full px-8 py-2.5 font-medium font-semibold transition-all disabled:cursor-not-allowed disabled:opacity-50 hover:opacity-90"
                      style={ {
                        background: (isSubmitting || hasAppliedPost)
                          ? 'linear-gradient(white, white) padding-box, linear-gradient(90deg, #ccc 0%, #ccc 100%) border-box'
                          : 'linear-gradient(white, white) padding-box, linear-gradient(90deg, #DD9DFF 0%, #36D3FF 100%) border-box',
                        border: '2px solid transparent',
                        color: '#333',
                      } }
                    >
                      {isSubmitting
                        ? (
                            <div className="flex items-center gap-2">
                              <div className="h-4 w-4 animate-spin border-2 border-gray-400 border-t-transparent rounded-full"></div>
                              Submitting...
                            </div>
                          )
                        : hasAppliedPost
                          ? 'Applied'
                          : 'Apply Post'}
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Delete Confirmation Modal */}
          <AnimatePresence>
            {showDeleteConfirm && (
              <motion.div
                initial={ { opacity: 0 } }
                animate={ { opacity: 1 } }
                exit={ { opacity: 0 } }
                className="absolute inset-0 z-50 flex items-center justify-center bg-black/30"
                onClick={ () => setShowDeleteConfirm(false) }
              >
                <motion.div
                  initial={ { scale: 0.9 } }
                  animate={ { scale: 1 } }
                  exit={ { scale: 0.9 } }
                  className="mx-4 max-w-sm rounded-xl bg-white p-6 shadow-xl"
                  onClick={ e => e.stopPropagation() }
                >
                  <div className="mb-4 flex items-start justify-between">
                    <h3 className="text-lg font-semibold">Delete this image?</h3>
                    {/* <button
                      onClick={ () => setShowDeleteConfirm(false) }
                      className="text-gray-400 hover:text-gray-600"
                    >
                      <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={ 2 } d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button> */}
                  </div>
                  <p className="mb-6 text-gray-600">
                    Once deleted, this image cannot be recovered.
                  </p>
                  <div className="flex gap-3">
                    <button
                      onClick={ handleDelete }
                      className="flex-1 rounded-lg bg-red-500 px-4 py-2 text-white transition-colors hover:bg-red-600"
                    >
                      Delete
                    </button>
                    <button
                      onClick={ () => setShowDeleteConfirm(false) }
                      className="flex-1 rounded-lg bg-gray-100 px-4 py-2 text-gray-700 transition-colors hover:bg-gray-200"
                    >
                      Cancel
                    </button>
                  </div>
                </motion.div>
              </motion.div>
            )}
          </AnimatePresence>
        </motion.div>
      )}
    </AnimatePresence>
  )
}

export default ImagePreviewCard
