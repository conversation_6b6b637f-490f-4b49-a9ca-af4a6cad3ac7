import { fetchEventSource } from '@microsoft/fetch-event-source'
import { Button, Checkbox, Select, Skeleton } from 'antd'
import classNames from 'clsx'
import { ChevronDown, Eye, Heart, Link, MessageCircle } from 'lucide-react'
import { useEffect, useMemo, useRef, useState } from 'react'
import { callDistillDarenListAPI, getWorkflowOutput } from '@/pages/Trend/stores/cozeStreamApi'
import { userStore } from '@/store/userStore'

type ThumbnailPost = {
  id: string
  image: string
  title: string
  user: { name: string }
  stats: { likes: string, comm: string, read: string }
  noteLink: string
  desc: string
}
interface Props {
  handleSelectNote: (noteId: string, checked: boolean) => void
  selectedNoteId?: string // 外部控制的选中ID
  useDynamicData?: boolean // 是否使用动态数据
  taskInstanceId?: string // 动态传入的 taskInstanceId
  shouldFetch?: boolean // 控制是否触发数据加载
  storeInstance?: any // 传入 store 实例
}
const RednoteReferenceList: React.FC<Props> = ({
  handleSelectNote = () => { },
  selectedNoteId: externalSelectedNoteId = '', // 接收外部传入的选中ID
  useDynamicData = false,
  taskInstanceId: propTaskInstanceId,
  shouldFetch = true,
  storeInstance,
}) => {
  const [selectedCategory, setSelectedCategory] = useState<'All' | 'Regulars' | 'KOL' | 'KOC'>('All')
  const [currentImageIndex, setCurrentImageIndex] = useState(0)

  /** 优先使用 store 中的数据，如果没有则使用本地 state */
  const [localNoteList, setLocalNoteList] = useState<{
    KOC: ThumbnailPost[]
    KOL: ThumbnailPost[]
    Regulars: ThumbnailPost[]
    All: ThumbnailPost[]
  }>({
    KOC: [],
    KOL: [],
    Regulars: [],
    All: [],
  })

  /** 从 store 获取数据或使用本地数据 */
  const [noteList, setNoteListState] = useState(() => {
    if (storeInstance?.stateStore?.distillDarenListData) {
      return {
        KOC: storeInstance.stateStore.distillDarenListData.KOC || [],
        KOL: storeInstance.stateStore.distillDarenListData.KOL || [],
        Regulars: storeInstance.stateStore.distillDarenListData.Regulars || [],
        All: storeInstance.stateStore.distillDarenListData.All || [],
      }
    }
    return localNoteList
  })

  /** 监听 store 数据变化并更新本地状态 */
  useEffect(() => {
    if (storeInstance?.stateStore?.distillDarenListData) {
      const storeData = storeInstance.stateStore.distillDarenListData
      setNoteListState({
        KOC: storeData.KOC || [],
        KOL: storeData.KOL || [],
        Regulars: storeData.Regulars || [],
        All: storeData.All || [],
      })
    }
  }, [storeInstance?.stateStore?.distillDarenListData?.KOC?.length, storeInstance?.stateStore?.distillDarenListData?.KOL?.length, storeInstance?.stateStore?.distillDarenListData?.Regulars?.length])

  /** 调试日志 */
  useEffect(() => {
    console.log('[RednoteReferenceList] noteList 数据变化:', {
      KOC: noteList.KOC.length,
      KOL: noteList.KOL.length,
      Regulars: noteList.Regulars.length,
      All: noteList.All.length,
      hasStore: !!storeInstance?.stateStore?.distillDarenListData,
      storeData: storeInstance?.stateStore?.distillDarenListData,
    })

    /** 如果有数据，自动设置loading为false */
    if (noteList.All.length > 0 && storeInstance?.stateStore?.distillDarenListData) {
      storeInstance.stateStore.distillDarenListData.loading = false
    }
  }, [noteList, storeInstance?.stateStore?.distillDarenListData])

  const setNoteList = (data: any) => {
    if (storeInstance?.stateStore?.distillDarenListData) {
      /** 更新 store 中的数据 */
      storeInstance.stateStore.distillDarenListData.KOC = data.KOC || []
      storeInstance.stateStore.distillDarenListData.KOL = data.KOL || []
      storeInstance.stateStore.distillDarenListData.Regulars = data.Regulars || []
      storeInstance.stateStore.distillDarenListData.All = data.All || []
    }
    /** 同时更新本地状态以触发重新渲染 */
    setNoteListState(data)
  }

  /** 使用本地状态管理 previewId，确保点击时能立即响应 */
  const [localPreviewId, setLocalPreviewId] = useState<string>(
    storeInstance?.stateStore?.distillDarenListData?.previewId || '',
  )
  const previewId = localPreviewId
  const setPreviewId = (id: string) => {
    console.log('[RednoteReferenceList] 设置预览ID:', id)
    setLocalPreviewId(id)
    if (storeInstance?.stateStore?.distillDarenListData) {
      storeInstance.stateStore.distillDarenListData.previewId = id
    }
  }

  /** 使用外部传入的选中ID，如果没有则使用store中的 */
  const selectNoteId = externalSelectedNoteId || storeInstance?.stateStore?.distillDarenListData?.selectNoteId || ''
  const setSelectNoteId = (id: string) => {
    /** 不再直接修改store，而是通过回调通知父组件 */
    // if (storeInstance?.stateStore?.distillDarenListData) {
    //   storeInstance.stateStore.distillDarenListData.selectNoteId = id
    // }
  }

  /** 使用本地状态管理 loading，因为 store 可能没有正确初始化 */
  const [localLoading, setLocalLoading] = useState(true)
  const loading = storeInstance?.stateStore?.distillDarenListData?.loading ?? localLoading
  const setLoading = (isLoading: boolean) => {
    setLocalLoading(isLoading)
    if (storeInstance?.stateStore?.distillDarenListData) {
      storeInstance.stateStore.distillDarenListData.loading = isLoading
    }
  }

  const [hasFetched, setHasFetched] = useState<boolean>(false) // 防止重复请求的标志
  const token = userStore.token

  const formatNumber = (num: string | number) => {
    const n = Number(num)
    if (n >= 10000)
      return `${(n / 10000).toFixed(1)}w`
    if (n >= 1000)
      return `${(n / 1000).toFixed(1)}k`
    return String(n)
  }

  /** 将数据累积变量提升到组件级别，避免作用域问题 */
  const dataAccumulatorRef = useRef<{
    KOL: ThumbnailPost[]
    KOC: ThumbnailPost[]
    Regulars: ThumbnailPost[]
  }>({
    KOL: [],
    KOC: [],
    Regulars: [],
  })

  const fetchNoteList = () => {
    console.log('[RednoteReferenceList] fetchNoteList 被调用, useDynamicData:', useDynamicData, 'hasFetched:', hasFetched)

    /** 如果已经请求过，直接返回 */
    if (hasFetched) {
      console.log('[RednoteReferenceList] 已经请求过数据，跳过重复请求')
      return
    }

    /** 重置累积数据 */
    dataAccumulatorRef.current = {
      KOL: [],
      KOC: [],
      Regulars: [],
    }

    /** 如果使用动态数据，调用新的 API */
    if (useDynamicData) {
      console.log('[RednoteReferenceList] 使用动态数据模式，调用 callDistillDarenListAPI')
      setLoading(true)
      setHasFetched(true) // 标记已请求

      callDistillDarenListAPI(
        (data) => {
          /** 处理流式数据 */
          if (!data || !data.content)
            return

          console.log('[RednoteReferenceList] 接收到数据:', {
            type: data.type,
            nodeTitle: data.nodeTitle,
            hasContent: !!data.content,
          })

          if (data.nodeTitle === 'kol') {
            const kol = JSON.parse(data.content)
            dataAccumulatorRef.current.KOL = kol.map((item: any, index: number) => {
              const { desc, title, imageList = [], imgList = [], nick, like, comm, read } = item
              // KOL数据使用imageList字段
              return {
                id: `KOL-${index}`,
                image: imageList.length > 0
                  ? imageList
                  : imgList,
                title,
                user: { name: nick },
                stats: { likes: formatNumber(like), comm: formatNumber(comm), read: formatNumber(read) },
                noteLink: item.noteLink,
                desc,
              }
            })
            console.log('[RednoteReferenceList] KOL 数据已处理:', dataAccumulatorRef.current.KOL.length, '条')
          }
          if (data.nodeTitle === 'koc') {
            const koc = JSON.parse(data.content)
            dataAccumulatorRef.current.KOC = koc.map((item: any, index: number) => {
              const { desc, title, imgList = [], imageList = [], nick, like, comm, read } = item
              // KOC数据使用imgList字段
              return {
                id: `KOC-${index}`,
                image: imgList.length > 0
                  ? imgList
                  : imageList,
                title,
                user: { name: nick },
                stats: { likes: formatNumber(like), comm: formatNumber(comm), read: formatNumber(read) },
                noteLink: item.noteLink,
                desc,
              }
            })
            console.log('[RednoteReferenceList] KOC 数据已处理:', dataAccumulatorRef.current.KOC.length, '条')
          }
          if (data.nodeTitle === 'suren') {
            const suren = JSON.parse(data.content)
            dataAccumulatorRef.current.Regulars = suren.map((item: any, index: number) => {
              const { desc, title, imgList = [], imageList = [], nick, like, comm, read } = item
              // suren数据使用imgList字段
              return {
                id: `Regulars-${index}`,
                image: imgList.length > 0
                  ? imgList
                  : imageList,
                title,
                user: { name: nick },
                stats: { likes: formatNumber(like), comm: formatNumber(comm), read: formatNumber(read) },
                noteLink: item.noteLink,
                desc,
              }
            })
            console.log('[RednoteReferenceList] Regulars 数据已处理:', dataAccumulatorRef.current.Regulars.length, '条')
          }
        },
        (error) => {
          console.error('[RednoteReferenceList] 错误:', error)
          setLoading(false)
        },
        () => {
          /** 完成回调 */
          const { KOC, KOL, Regulars } = dataAccumulatorRef.current
          console.log('[RednoteReferenceList] distill_daren_list 完成，收集到的数据:', {
            KOC: KOC.length,
            KOL: KOL.length,
            Regulars: Regulars.length,
          })

          const allData = [...KOC, ...KOL, ...Regulars]

          setNoteList({
            KOC,
            KOL,
            Regulars,
            All: allData,
          })

          /** 根据当前选择的分类设置预览 */
          const currentCategoryData = selectedCategory === 'All'
            ? allData
            : selectedCategory === 'KOC'
              ? KOC
              : selectedCategory === 'KOL'
                ? KOL
                : Regulars

          if (currentCategoryData.length > 0) {
            const firstId = currentCategoryData[0].id
            setPreviewId(firstId)
            console.log('[RednoteReferenceList] 设置预览ID:', firstId, '分类:', selectedCategory)
          }

          setLoading(false)
        },
        propTaskInstanceId, // 传递 taskInstanceId
      )

      return
    }

    /** 原有的静态数据逻辑（用于测试） */
    setHasFetched(true) // 标记已请求

    /** 重置累积数据 */
    dataAccumulatorRef.current = {
      KOL: [],
      KOC: [],
      Regulars: [],
    }

    const apiUrl = import.meta.env.VITE_API_BASE_URL || '/api'
    const taskInstanceId = propTaskInstanceId || localStorage.getItem('confirmed_taskInstanceId') || localStorage.getItem('taskInstanceId') || '1958473114904039424'

    /** 获取动态参数 */
    const dataReportData = getWorkflowOutput('data_report')
    const competitorReportData = getWorkflowOutput('competitor_report')
    const dataReportParams = JSON.parse(localStorage.getItem('dataReportParams') || '{}')

    const parameters = {
      brand_report: dataReportData.brand_report || '',
      biji_data: dataReportData.biji_data || '',
      daren_data: dataReportData.daren_data || '',
      product_data: dataReportData.product_data || '',
      industry_name: dataReportParams.industry_name || competitorReportData.industry_name || '',
    }

    console.log('[RednoteReferenceList] 使用参数:', parameters)

    fetchEventSource(`${apiUrl}/app/market/stream/execute-main`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
      body: JSON.stringify({
        taskInstanceId,
        platform: 'rednote',
        workflowName: 'distill_daren_list',
        parameters,
      }),
      onmessage(ev) {
        if (!ev.data || ev.event === 'end')
          return

        const parsed = JSON.parse(ev.data)
        console.log(parsed)

        // if (parsed.node_title === "biji_list") {
        //   const { koc, kol, suren } = JSON.parse(parsed.content)[0]

        //   const KOC = koc.map((item, index: number) => {
        //     const { desc, title, imageList=[], nick, like, comm, read } = item.info
        //     return {
        //       id: `KOC-${index}`,
        //       image: imageList,
        //       title,
        //       user: { name: nick },
        //       stats: { likes: formatNumber(like), comm, read },
        //       noteLink: item.noteLink,
        //       desc
        //     }
        //   })
        //   const KOL = kol.map((item, index: number) => {
        //     const { desc, title, imageList=[], nick, like, comm, read } = item.info
        //     return {
        //       id: `KOL-${index}`,
        //       image: imageList,
        //       title,
        //       user: { name: nick },
        //       stats: { likes: formatNumber(like), comm, read },
        //       noteLink: item.noteLink,
        //       desc
        //     }
        //   })
        //   const Regulars = suren.map((item, index: number) => {
        //     const { desc, title, imageList=[], nick, like, comm, read } = item.info
        //     return {
        //       id: `Regulars-${index}`,
        //       image: imageList,
        //       title,
        //       user: { name: nick },
        //       stats: { likes: formatNumber(like), comm, read },
        //       noteLink: item.noteLink,
        //       desc
        //     }
        //   })
        //   setNoteList({
        //     KOC,
        //     KOL,
        //     Regulars,
        //     All: [...KOC, ...KOL, ...Regulars]
        //   })
        // }

        if (parsed.node_title === 'kol') {
          const kol = JSON.parse(parsed.content)
          dataAccumulatorRef.current.KOL = kol.map((item, index: number) => {
            const { desc, title, imageList = [], imgList = [], nick, like, comm, read } = item
            // KOL数据使用imageList字段
            return {
              id: `KOL-${index}`,
              image: imageList.length > 0
                ? imageList
                : imgList,
              title,
              user: { name: nick },
              stats: { likes: formatNumber(like), comm: formatNumber(comm), read: formatNumber(read) },
              noteLink: item.noteLink,
              desc,
            }
          })
          console.log('[RednoteReferenceList] 静态 KOL 数据已处理:', dataAccumulatorRef.current.KOL.length, '条')
        }
        if (parsed.node_title === 'koc') {
          const koc = JSON.parse(parsed.content)
          dataAccumulatorRef.current.KOC = koc.map((item, index: number) => {
            const { desc, title, imgList = [], imageList = [], nick, like, comm, read } = item
            // KOC数据使用imgList字段
            return {
              id: `KOC-${index}`,
              image: imgList.length > 0
                ? imgList
                : imageList,
              title,
              user: { name: nick },
              stats: { likes: formatNumber(like), comm: formatNumber(comm), read: formatNumber(read) },
              noteLink: item.noteLink,
              desc,
            }
          })
          console.log('[RednoteReferenceList] 静态 KOC 数据已处理:', dataAccumulatorRef.current.KOC.length, '条')
        }
        if (parsed.node_title === 'suren') {
          const suren = JSON.parse(parsed.content)
          dataAccumulatorRef.current.Regulars = suren.map((item, index: number) => {
            const { desc, title, imgList = [], imageList = [], nick, like, comm, read } = item
            // suren数据使用imgList字段
            return {
              id: `Regulars-${index}`,
              image: imgList.length > 0
                ? imgList
                : imageList,
              title,
              user: { name: nick },
              stats: { likes: formatNumber(like), comm: formatNumber(comm), read: formatNumber(read) },
              noteLink: item.noteLink,
              desc,
            }
          })
          console.log('[RednoteReferenceList] 静态 Regulars 数据已处理:', dataAccumulatorRef.current.Regulars.length, '条')
        }
      },
      onopen: async () => {
        setLoading(true)
      },
      onclose: async () => {
        /** 响应完成 */
        const { KOC, KOL, Regulars } = dataAccumulatorRef.current
        console.log('[RednoteReferenceList] 静态数据加载完成，收集到的数据:', {
          KOC: KOC.length,
          KOL: KOL.length,
          Regulars: Regulars.length,
        })

        const allData = [...KOC, ...KOL, ...Regulars]

        setNoteList({
          KOC,
          KOL,
          Regulars,
          All: allData,
        })

        /** 根据当前选择的分类设置预览 */
        const currentCategoryData = selectedCategory === 'All'
          ? allData
          : selectedCategory === 'KOC'
            ? KOC
            : selectedCategory === 'KOL'
              ? KOL
              : Regulars

        if (currentCategoryData.length > 0) {
          const firstId = currentCategoryData[0].id
          setPreviewId(firstId)
          console.log('[RednoteReferenceList] 静态数据设置预览ID:', firstId, '分类:', selectedCategory)
        }

        setLoading(false)
      },
      onerror() {
        alert('错误')
        // setLoading(false)
      },
    })
  }

  const handlePreviewPost = (postId: string) => {
    console.log('[RednoteReferenceList] 点击缩略图，postId:', postId)
    console.log('[RednoteReferenceList] 当前分类:', selectedCategory)
    console.log('[RednoteReferenceList] 当前分类数据:', noteList[selectedCategory])
    setPreviewId(postId)
    setCurrentImageIndex(0) // 重置图片索引
  }

  useEffect(() => {
    /** 检查是否应该从 store 获取数据 */
    const checkAndLoadData = async () => {
      console.log('[RednoteReferenceList] checkAndLoadData 开始执行', {
        hasStoreInstance: !!storeInstance,
        hasDistillData: !!storeInstance?.stateStore?.distillDarenListData,
        shouldFetch,
        hasFetched,
        useDynamicData,
      })

      /** 如果有 storeInstance 并且有 distillDarenListData，直接使用 */
      if (storeInstance?.stateStore?.distillDarenListData) {
        const storeData = storeInstance.stateStore.distillDarenListData
        console.log('[RednoteReferenceList] 从 store 获取数据:', {
          KOC: storeData.KOC?.length || 0,
          KOL: storeData.KOL?.length || 0,
          Regulars: storeData.Regulars?.length || 0,
          All: storeData.All?.length || 0,
        })

        /** 如果 store 中有数据，使用它 */
        if (storeData.KOC?.length > 0 || storeData.KOL?.length > 0 || storeData.Regulars?.length > 0) {
          console.log('[RednoteReferenceList] Store 中有数据，更新组件状态')
          setNoteListState({
            KOC: storeData.KOC || [],
            KOL: storeData.KOL || [],
            Regulars: storeData.Regulars || [],
            All: storeData.All || [],
          })

          /** 设置默认预览 */
          const allData = storeData.All || []
          if (allData.length > 0 && !localPreviewId) {
            const firstId = allData[0].id
            setPreviewId(firstId)
            console.log('[RednoteReferenceList] 从 store 数据设置预览ID:', firstId)
          }

          setLoading(false)
          setHasFetched(true)
          return
        }
        else {
          console.log('[RednoteReferenceList] Store 中没有数据，继续等待或调用 API')
        }
      }

      /** 如果没有 store 数据，检查是否需要调用 API */
      try {
        /** 尝试获取全局 store */
        const { trendAg } = await import('@/pages/Trend/stores')
        if (trendAg?.stateStore?.hasTriggeredDistillDarenList) {
          console.log('[RednoteReferenceList] distill_daren_list 已在全局触发，设置定时器等待数据...')

          let checkCount = 0
          /** 设置一个定时器来检查数据是否已经到达 */
          const checkInterval = setInterval(() => {
            checkCount++
            console.log(`[RednoteReferenceList] 第 ${checkCount} 次检查 store 数据...`)

            if (storeInstance?.stateStore?.distillDarenListData) {
              const storeData = storeInstance.stateStore.distillDarenListData
              console.log(`[RednoteReferenceList] 检查结果:`, {
                KOC: storeData.KOC?.length || 0,
                KOL: storeData.KOL?.length || 0,
                Regulars: storeData.Regulars?.length || 0,
              })

              if (storeData.KOC?.length > 0 || storeData.KOL?.length > 0 || storeData.Regulars?.length > 0) {
                console.log('[RednoteReferenceList] 检测到 store 数据已更新，更新组件状态')
                clearInterval(checkInterval)
                setNoteListState({
                  KOC: storeData.KOC || [],
                  KOL: storeData.KOL || [],
                  Regulars: storeData.Regulars || [],
                  All: storeData.All || [],
                })
                setLoading(false)
                setHasFetched(true)
              }
            }

            /** 超过20次检查（10秒）后停止 */
            if (checkCount >= 20) {
              console.warn('[RednoteReferenceList] 等待超时，停止检查')
              clearInterval(checkInterval)
              setLoading(false) // 即使没有数据也要停止 loading
            }
          }, 500) // 每500ms检查一次

          return
        }
        else {
          console.log('[RednoteReferenceList] distill_daren_list 未触发')
        }
      }
      catch (e) {
        console.log('[RednoteReferenceList] 无法访问全局状态，使用本地逻辑:', e)
      }

      /** 原有逻辑 */
      if (shouldFetch && !hasFetched) {
        console.log('[RednoteReferenceList] shouldFetch=true, 开始加载数据')
        fetchNoteList()
      }
      else {
        console.log('[RednoteReferenceList] 不满足加载条件', { shouldFetch, hasFetched })
        /** 如果不满足加载条件，也要停止 loading */
        setLoading(false)
      }
    }

    checkAndLoadData()
  }, [useDynamicData, propTaskInstanceId, shouldFetch, hasFetched, localPreviewId])

  const preview = useMemo(() => {
    /** 首先在当前分类中查找 */
    let found = noteList[selectedCategory].find(item => item.id === previewId)
    /** 如果当前分类没找到，在All中查找 */
    if (!found) {
      found = noteList.All.find(item => item.id === previewId)
    }
    return found
  }, [previewId, selectedCategory, noteList])

  /** 添加调试信息 */
  console.log('[RednoteReferenceList] 渲染组件，当前状态:', {
    loading,
    noteListLength: noteList.All.length,
    hasData: noteList.All.length > 0,
  })

  return (
    <div className="mx-auto max-w-4xl rounded-xl bg-white p-6 shadow-lg">
      {/* Header */}
      <div className="mb-6">
        <h2 className="mb-2 text-2xl text-gray-900 font-semibold">Rednote Reference List</h2>
        <p className="text-sm text-gray-600 leading-relaxed">
          Choose your template Select a reference from your Rednote list to use as your starting point
        </p>
      </div>

      {loading ? <Skeleton /> : noteList.All.length === 0 ? (
        <div className="py-8 text-center text-gray-500">
          <p>Waiting for distill_daren_list workflow to complete...</p>
          <p className="mt-2 text-xs">数据加载中，请稍候</p>
        </div>
      ) : (<div style={ { overflow: 'auto', maxHeight: 'calc(100vh - 200px)' } }>
        {/* Category Filter */}
        <div className="mb-6 flex items-center border-b border-gray-200 pb-4">
          <span className="mr-4 text-sm text-gray-700 font-medium">Post Categories:</span>
          <Select
            value={ selectedCategory }
            onChange={ (e) => {
              setSelectedCategory(e)
              setCurrentImageIndex(0) // 重置图片索引
              /** 默认显示第一个 */
              const firstItem = noteList[e]?.[0]
              if (firstItem) {
                setPreviewId(firstItem.id)
              }
            } }
            className="!h-8 !min-w-[90px] !flex !items-center !rounded-full !border-none !bg-[#f5f5f5] !px-3 !py-1 !shadow-none"
            bordered={ false }
            dropdownStyle={ { minWidth: 120, whiteSpace: 'normal' } }
            optionLabelProp="children"
            suffixIcon={
              <span className="ml-1 text-xs text-gray-400">
                <ChevronDown size={ 13 } />
              </span>
            }
          >
            <Select.Option value="All" className="!text-gray-700 !font-medium">All</Select.Option>
            <Select.Option value="KOC" className="!text-gray-700 !font-medium">KOC</Select.Option>
            <Select.Option value="KOL" className="!text-gray-700 !font-medium">KOL</Select.Option>
            <Select.Option value="Regulars" className="!text-gray-700 !font-medium">Regulars</Select.Option>
          </Select>
        </div>

        {/* Main Content Card */}
        <div
          className={ classNames(`mb-4 flex overflow-hidden border-2 border-blue-100 rounded-xl`) }
          style={ {
            background: preview?.id === selectNoteId
              ? 'linear-gradient(90deg, rgba(221, 157, 255, 0.10) 0%, rgba(54, 211, 255, 0.10) 100%)'
              : 'transparent',
          } }>
          {/* Image Section */}
          <div className="relative flex-1">
            <div className="relative h-full w-full">
              {preview?.image && preview.image.length > 0 ? (
                <>
                  <img
                    src={ preview.image[Math.min(currentImageIndex, preview.image.length - 1)] }
                    alt="Main post"
                    className="h-full w-full object-cover"
                    onError={ (e) => {
                      console.error('[RednoteReferenceList] 图片加载失败:', e.currentTarget.src)
                      e.currentTarget.src = '/src/assets/image/placeholder.png' // 使用占位图
                    } }
                  />
                  <div className="absolute right-4 top-4 rounded-xl bg-black bg-opacity-60 px-2 py-1 text-xs text-white">
                    {Math.min(currentImageIndex + 1, preview.image.length)}
                    /
                    {preview.image.length}
                  </div>
                  {preview.image.length > 1 && (
                    <>
                      <button
                        className="absolute left-3 top-1/2 h-8 w-8 flex items-center justify-center rounded-full bg-black bg-opacity-60 text-lg text-[#fff] transition-colors -translate-y-1/2 hover:bg-opacity-80"
                        onClick={ () => setCurrentImageIndex(prev => prev === 0
                          ? preview.image.length - 1
                          : prev - 1) }
                      >
                        ‹
                      </button>
                      <button
                        className="absolute right-3 top-1/2 h-8 w-8 flex items-center justify-center rounded-full bg-black bg-opacity-60 text-lg text-[#fff] transition-colors -translate-y-1/2 hover:bg-opacity-80"
                        onClick={ () => setCurrentImageIndex(prev => prev === preview.image.length - 1
                          ? 0
                          : prev + 1) }
                      >
                        ›
                      </button>
                    </>
                  )}
                </>
              ) : (
                <div className="h-full w-full flex items-center justify-center bg-gray-100">
                  <span className="text-gray-500">No image available</span>
                </div>
              )}
            </div>
          </div>

          {/* Content Section */}
          <div className="flex flex-1 flex-col p-6">
            {/* User Info */}
            <div className="mb-4 flex items-center justify-between">
              <div className="flex items-center">
                <img
                  src="/src/assets/image/home/<USER>/1.webp"
                  alt="User avatar"
                  className="mr-2 h-8 w-8 rounded-full"
                />
                <span className="mr-2 text-gray-700 font-medium">{preview?.user.name}</span>
              </div>
              <span className="text-[rgb(104 104 104)] rounded-xl bg-[#f5f5f5] px-2 py-1 text-xs">
                {preview?.id.split('-')[0]}
              </span>
            </div>

            {/* Post Content */}
            <div className="mb-4 flex-1">
              <h3 className="mb-3 text-base text-gray-700 font-semibold leading-tight">
                {preview?.title}
              </h3>
              <p className="text-sm text-gray-600 leading-relaxed">
                {preview?.desc}
              </p>
            </div>

            {/* Stats and Actions */}
            <div className="mb-4 flex items-center justify-between border-t border-gray-200 pt-4">
              <div className="flex items-center gap-4">
                <span className="flex items-center gap-1 text-sm text-gray-600">
                  <Heart size={ 16 } />
                  {preview?.stats.likes}
                </span>
                <span className="flex items-center gap-1 text-sm text-gray-600">
                  <MessageCircle size={ 16 } />
                  {preview?.stats.comm}
                </span>
                <span className="flex items-center gap-1 text-sm text-gray-600">
                  <Eye size={ 16 } />
                  {preview?.stats.read}
                </span>
              </div>
              <Button
                type="link"
                className="flex items-center gap-1 text-sm !p-0 !text-blue-600"
                onClick={ () => window.open(preview?.noteLink) }
              >
                View Post
                <Link size={ 13 } />
              </Button>
            </div>

            {/* Select Reference Checkbox */}
            <div className="flex items-center justify-end gap-2">
              <Checkbox
                onChange={ (e) => {
                  const checked = e.target.checked
                  // setSelectNoteId(previewId) // 不再直接设置，由父组件控制
                  handleSelectNote(preview?.id || previewId, checked) // 通知父组件
                } }
                checked={ preview?.id === selectNoteId }>
                Select as my reference
              </Checkbox>
            </div>
          </div>
        </div>

        {/* Thumbnail Carousel */}
        <div className="relative flex items-center gap-3">
          <button
            className="h-8 w-8 flex flex-shrink-0 items-center justify-center border border-gray-300 rounded-full bg-black bg-opacity-60 text-base text-[#fff]"
            onClick={ () => {
              const container = document.querySelector('.scrollbar-hide')
              if (container) {
                container.scrollLeft -= 220 // 滚动一个缩略图的宽度 + 间距
              }
            } }
          >
            ‹
          </button>
          <div className="scrollbar-hide flex flex-1 gap-4 overflow-x-auto py-2">
            {noteList[selectedCategory]
              .map(post => (
                <div
                  key={ post.id }
                  onClick={ () => handlePreviewPost(post.id) }
                  className={ classNames(
                    'relative min-w-[150px] cursor-pointer overflow-hidden rounded-lg bg-white shadow-md transition-transform hover:-translate-y-1',
                    {
                      'border-2 border-blue-100': preview?.id === post.id,
                    },
                  ) }
                >
                  {post.id === selectNoteId && (
                    <div className="absolute right-[12px] top-[12px] items-center rounded-full bg-gray-100 px-2 py-0.5 text-sm text-gray-700">
                      Selected
                    </div>
                  )}
                  <img
                    src={ Array.isArray(post.image)
                      ? post.image[0]
                      : post.image }
                    alt="Thumbnail"
                    className="h-[200px] w-full object-cover"
                    onError={ (e) => {
                      console.error('[RednoteReferenceList] 缩略图加载失败:', e.currentTarget.src)
                      e.currentTarget.src = '/src/assets/image/placeholder.png' // 使用占位图
                    } }
                  />
                  <div className="p-3">
                    <p className="line-clamp-2 mb-2 h-[30px] text-xs text-gray-700 leading-tight">
                      {post.title}
                    </p>
                    <div className="flex items-center gap-1.5">
                      <img
                        src="/src/assets/image/home/<USER>/1.webp"
                        alt="User"
                        className="h-4 w-4 rounded-full"
                      />
                      <span className="flex-1 text-xs text-gray-600">{post.user.name}</span>
                      <span className="flex items-center gap-1 text-xs text-gray-400">
                        <Heart size={ 12 } />
                        {post.stats.likes}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
          </div>
          <button
            className="h-8 w-8 flex flex-shrink-0 items-center justify-center border border-gray-300 rounded-full bg-black bg-opacity-60 text-base text-[#fff]"
            onClick={ () => {
              const container = document.querySelector('.scrollbar-hide')
              if (container) {
                container.scrollLeft += 220 // 滚动一个缩略图的宽度 + 间距
              }
            } }
          >
            ›
          </button>
        </div>
      </div>)}

    </div>
  )
}

export default RednoteReferenceList
