/* eslint-disable prefer-const */
import type { WorkflowStep } from '../../ChatV2/components/ChatWorkflow'
import type { MdToCodePreviewType, MessageStoreType, ReportStoreType, StateStoreType, StepStateType, TaskStoreType } from '../stores'
import type { ParsedStreamData } from '../stores/cozeStreamApi'

import type { MarketStep1Params } from '@/api/MarketApi'
import type { TrendStep1Params } from '@/api/TrendApi'
import classNames from 'clsx'
import { motion } from 'framer-motion'
import { ChevronDown, Sparkles } from 'lucide-react'
import { memo, useCallback, useEffect, useInsertionEffect, useMemo, useRef, useState } from 'react'
import { FileAPI } from '@/api/FileAPI'
import { AnimateShow } from '@/components/Animate/AnimateShow'
import { SelectableGradientCard } from '@/components/Card/SelectableGradientCard'
import { LoadingIcon } from '@/components/Loading/LoadingIcon'

import { useBindWinEvent } from '@/hooks'
import { cn, request } from '@/utils'
import TopBar from '../../ChatV2/components/TopBar'
import { ChatEventBus } from '../../ChatV2/constants'
import TrendSelectionPage from '../../TrendSelection/components/TrendSelectionPage'
import { DistributionEvent, eventBus } from '../constants'

import { trendAg } from '../stores'
import { addReportItem, createCardMessage, removeMessage } from '../stores/create'
import { ChatHistory } from './ChatHistory'
import ChatWorkflow from './ChatWorkflow'
import { MessageItem } from './MessageItem'
import {
  CombinedThinkingStream,
  OperationsThinkingStream,
  OperationsThinkingStream1,
  PlanningThinkingStream as OptimizedPlanningThinkingStream,
  OriginalWorkThinkingStream,
} from './OptimizedThinkingStream'
import { ReportPreview } from './ReportComponents/ReportPreview'
import { STREAM_IDS, streamingDataManager } from './StreamingDataManager'
import { TextDisplayWithPagination } from './TextDisplayWithPagination'

/** Strategy卡片渲染组件 - 专门渲染Strategy卡片 */
const StrategyCardRenderer = memo<{
  messageStore: any
  stateStore: any
}>(({ messageStore, stateStore }) => {
  const { messages } = messageStore.useAndDispose()

  /** 找到Strategy卡片 */
  const strategyCard = useMemo(() => {
    return messages.find((msg: any) => msg.meta?.isStrategyCard && msg.type === 'card')
  }, [messages])

  if (!strategyCard) {
    return null
  }

  return (
    <motion.div
      initial={ { opacity: 0, y: 20 } }
      animate={ { opacity: 1, y: 0 } }
      transition={ { duration: 0.5 } }
      className="mt-4"
    >
      <MessageItem
        message={ strategyCard }
        onDelete={ () => {} }
        className="w-full"
        stateStore={ stateStore }
      />
    </motion.div>
  )
})

StrategyCardRenderer.displayName = 'StrategyCardRenderer'

/** Implementation卡片渲染组件 - 专门渲染Implementation卡片 */
const ImplementationCardRenderer = memo<{
  messageStore: any
  stateStore: any
  className?: string
}>(({ messageStore, stateStore, className }) => {
  const { messages } = messageStore.useAndDispose()

  /** 找到Implementation卡片 */
  const implementationCard = useMemo(() => {
    return messages.find((msg: any) => msg.meta?.isImplementationCard && msg.type === 'card')
  }, [messages])

  if (!implementationCard) {
    return null
  }

  return (
    <motion.div
      initial={ { opacity: 0, y: 20 } }
      animate={ { opacity: 1, y: 0 } }
      transition={ { duration: 0.5 } }
      className={ cn('mt-4', className) }
    >
      <MessageItem
        message={ implementationCard }
        onDelete={ () => {} }
        className="w-full"
        stateStore={ stateStore }
      />
    </motion.div>
  )
})

ImplementationCardRenderer.displayName = 'ImplementationCardRenderer'

/** OriginalWork卡片渲染组件 - 使用 SelectableGradientCard 组件 */
const OriginalWorkCardRenderer = memo<{
  messageStore: any
  stateStore: any
  className?: string
}>(({ messageStore, stateStore, className }) => {
  const { messages } = messageStore.useAndDispose()

  /** 创建一个虚拟的卡片数据用于渲染（不添加到 messages 中） */
  const originalWorkCard = useMemo(() => {
    /** 先查找已存在的卡片 */
    const existingCard = messages.find((msg: any) => msg.meta?.cardId === 'original-work-report' && msg.type === 'card')
    if (existingCard) {
      return existingCard
    }

    /** 如果没有，创建一个虚拟卡片用于渲染 */
    return {
      id: 'original-work-virtual-card',
      type: 'card',
      meta: {
        cardId: 'original-work-report',
        isVirtual: true,
      },
      originalWorkData: stateStore.originalWorkData || {},
    }
  }, [messages, stateStore.originalWorkData])

  /** 处理卡片点击事件 - 必须在所有条件判断之前定义 */
  const handleCardClick = useCallback(() => {
    /** 点击卡片时，先将卡片中的数据同步到 stateStore */
    if (originalWorkCard && originalWorkCard.originalWorkData) {
      stateStore.originalWorkData = { ...originalWorkCard.originalWorkData }
    }
    /** 显示 ImagePreviewCard */
    ChatEventBus.emit('showImagePreview', { isUserAction: true })
  }, [originalWorkCard, stateStore])

  /** 获取图标组件 */
  const getIconComponent = (iconName: string) => {
    if (iconName === 'creativeDirector') {
      return (
        <img
          src={ new URL('@/assets/image/home/<USER>', import.meta.url).href }
          alt="Creative Director"
          className="h-10 w-10 object-contain"
        />
      )
    }
    if (iconName === 'rightArrow') {
      return (
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
          <path d="M9 18L15 12L9 6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
        </svg>
      )
    }
    return null
  }
  <motion.div
    initial={ { opacity: 0, y: 20 } }
    animate={ { opacity: 1, y: 0 } }
    transition={ { duration: 0.5 } }
    className={ className }
  >
    <SelectableGradientCard
      selected={ false }
      onSelectedChange={ handleCardClick }
      onClick={ handleCardClick }
      cardConfig={ {
        leftIcon: {
          show: true,
          icon: getIconComponent('creativeDirector'),
          size: 'lg',
        },
        rightIcon: {
          show: true,
          icon: getIconComponent('rightArrow'),
          size: 'md',
          className: 'text-gray-400',
        },
        content: {
          title: originalWorkCard.card?.title || 'The Trend Report',
          description: originalWorkCard.card?.description || 'Content creation based on your selection',
        },
        /** 不显示按钮 */
        layout: 'simple',
      } }
      className="cursor-pointer transition-transform hover:scale-[1.02]"
    />
  </motion.div>
})

OriginalWorkCardRenderer.displayName = 'OriginalWorkCardRenderer'

/** OperationsManager卡片渲染组件 - 渲染静态OperationsManager卡片 */
const OperationsManagerCardRenderer = memo<{
  stateStore: any
  className?: string
}>(({ stateStore, className }) => {
  /** 创建静态的OperationsManager卡片数据，不添加到消息流中 */

  const currentLeftIcon = 'creativeDirector' // 使用 Research Analyst 图标

  const operationsManagerCard = useMemo(() => {
    return {
      id: 'operations-manager-static-card',
      type: 'card' as const,
      timestamp: Date.now(),
      sender: 'assistant' as const,
      content: '',
      meta: {
        cardId: 'operations-manager-card',
        isOperationsManagerCard: true,
        step: 'step1' as const,
      },
      card: {
        title: 'Operations Manager',
        description: '正在生成运营管理详情，包含策略执行、资源协调和进度监控...',
        variant: 'success' as const,
        onClick: () => {
          /** 显示相关页面 */
          stateStore.showTrendSelection = true
          stateStore.isReportOpen = true
        },
        cardConfig: {
          leftIcon: {
            show: true,
            icon: currentLeftIcon,
            size: 'lg' as const,
          },
          rightIcon: {
            show: true,
            icon: 'card-right-icon3', // 使用自定义 SVG 图标
            size: 'md' as const,
          },
          content: {
            title: 'The Trend Report',
            description: 'Review the trend report based on your choice',
          },
          socialPost: {
            image: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop',
            description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit...',
            author: {
              name: 'Milla',
              avatar: 'https://sns-avatar-qc.xhscdn.com/avatar/1040g2jo31aje2ouc6q0049042ub7u510c0c06r0?imageView2/2/w/120/format/webp|imageMogr2/strip',
            },
          },
          layout: 'social' as const,
          button: {
            text: 'View Operations',
            variant: 'gradient-border' as const,
            position: 'bottom-left' as const,
            disabled: false,
            onClick: () => {
              /** 显示相关页面 */
              stateStore.showTrendSelection = true
              stateStore.isReportOpen = true
            },
          },
        },
      },
    }
  }, [stateStore])

  return (
    <motion.div
      initial={ { opacity: 0, y: 20 } }
      animate={ { opacity: 1, y: 0 } }
      transition={ { duration: 0.5 } }
      className={ cn('mt-4', className) }
    >
      <MessageItem
        message={ operationsManagerCard }
        onDelete={ () => {} }
        className="w-full"
        stateStore={ stateStore }
      />
    </motion.div>
  )
})

OperationsManagerCardRenderer.displayName = 'OperationsManagerCardRenderer'

/** Planning Scheme专用的ThinkingStream组件 */
const PlanningThinkingStream = memo<{
  content: string
  isActive: boolean
}>(({ content, isActive }) => {
  const [thinkingExpanded, setThinkingExpanded] = useState(true)
  const [displayedText, setDisplayedText] = useState('')
  const [isTyping, setIsTyping] = useState(false)

  useEffect(() => {
    if (!content || !isActive)
      return

    setIsTyping(true)
    setDisplayedText('')

    let currentIndex = 0
    const typeInterval = setInterval(() => {
      if (currentIndex < content.length) {
        setDisplayedText(content.slice(0, currentIndex + 1))
        currentIndex++
      }
      else {
        setIsTyping(false)
        clearInterval(typeInterval)
      }
    }, 20)

    return () => clearInterval(typeInterval)
  }, [content, isActive])

  if (!isActive || !content)
    return null

  return (
    <motion.div
      initial={ { opacity: 0, y: 10 } }
      animate={ { opacity: 1, y: 0 } }
      className="border border-gray-200 rounded-lg bg-white p-4 shadow-sm"
    >
      <div
        className="flex cursor-pointer items-center justify-between"
        onClick={ () => setThinkingExpanded(!thinkingExpanded) }
      >
        <div className="flex items-center gap-2">
          <Sparkles className="h-4 w-4 text-blue-500" />
          <span className="text-sm text-gray-700 font-medium">Planning Analysis</span>
          {isTyping && <LoadingIcon className="h-3 w-3" />}
        </div>
        <ChevronDown
          className={ `h-4 w-4 text-gray-400 transition-transform ${
            thinkingExpanded
              ? 'rotate-180'
              : ''
          }` }
        />
      </div>

      <AnimateShow show={ thinkingExpanded }>
        <div className="mt-3 whitespace-pre-wrap text-sm text-gray-600">
          {displayedText}
          {isTyping && <span className="animate-pulse">|</span>}
        </div>
      </AnimateShow>
    </motion.div>
  )
})

PlanningThinkingStream.displayName = 'PlanningThinkingStream'

export const ChatPage = memo<ChatPageProps>((
  {
    style,
    className,
    taskStore,
    stateStore,
    messageStore,
    mdToCodePreview,
    resetDistributionStore: _resetDistributionStore,
    reportStore,
    stepState,

    // TopBar 相关 props
    showTopBar = false,
    topBarAgents = [],
    onTopBarAgentClick,
    topBarDropdownExpanded = false,
    onTopBarDropdownToggle,
    onTopBarNewProject,

    // ChatV2 流程相关 props
    onStartAIAnalysis,
  },
) => {
  // @ts-ignore - 添加 collectedFormData 和 thinkingContent 到监听列表
  const { formData, isReportOpen, chatV2FlowMode, userDescription, uploadedImage, collectedFormData, thinkingContent } = stateStore.use()
  taskStore.use() // 保持响应式连接

  const chatHistoryRef = useRef<{ scrollToBottom: () => void }>(null)
  const stateStoreRef = useRef(stateStore)
  const taskStoreRef = useRef(taskStore)

  /** 更新 refs */
  stateStoreRef.current = stateStore
  taskStoreRef.current = taskStore

  // ChatV2 流程状态管理
  const [isThinking, setIsThinking] = useState(false)
  const [showForm, setShowForm] = useState(false)
  const [showWorkflow, setShowWorkflow] = useState(false)
  const [thinkingExpanded, setThinkingExpanded] = useState(true)
  const [isAnalysisStarted, setIsAnalysisStarted] = useState(false) // 控制是否已开始分析
  const [isButtonLoading, setIsButtonLoading] = useState(false) // 控制Continue to Strategy按钮的loading状态
  const [isButtonDisabled, setIsButtonDisabled] = useState(false) // 控制Continue to Strategy按钮的disabled状态
  const [cardsCreated, setCardsCreated] = useState(false) // 防重复创建标志
  const [competitiveReportItemId, setCompetitiveReportItemId] = useState<string>('') // 存储竞争对手分析报告项ID
  const [thinkingData, setThinkingData] = useState<string>('') // 存储从insight_report获取的thinking数据
  const [isThinkingDataReady, setIsThinkingDataReady] = useState(false) // 控制按钮是否可用（基于thinking数据是否准备好）
  const [showStrategyCard, setShowStrategyCard] = useState(false) // 控制Strategy卡片的显示

  /** 工作流完成状态追踪 */
  const [dataReportCompleted, setDataReportCompleted] = useState(false) // data_report 工作流完成状态
  const [insightReportCompleted, setInsightReportCompleted] = useState(false) // insight_report 工作流完成状态
  const [competitorReportCompleted, setCompetitorReportCompleted] = useState(false) // competitor_report 工作流完成状态
  const [planningSchemeCompleted, setPlanningSchemeCompleted] = useState(false) // planning_scheme 工作流完成状态
  const [hasApprovedStrategy, setHasApprovedStrategy] = useState(false) // 是否已经点击过 Approve Strategy
  const [strategyReportItemId, setStrategyReportItemId] = useState<string>('') // 存储Strategy报告项ID
  const [isStrategyApproved, setIsStrategyApproved] = useState(false) // 控制Strategy是否已批准
  const [displayText, setDisplayText] = useState<string>('') // 存储要在文本显示组件中展示的内容
  const [secondaryDisplayText, setSecondaryDisplayText] = useState<string>('') // 存储第二个文本显示组件的内容
  const [planningThinkingData, setPlanningThinkingData] = useState<string>('') // 存储Planning Scheme的thinking数据
  const [showPlanningThinking, setShowPlanningThinking] = useState(false) // 控制Planning Scheme ThinkingStream的显示
  const [isContentCreationDisabled, setIsContentCreationDisabled] = useState(false) // 控制 Start content creation 按钮的禁用状态
  const [isContentCreationLoading, setIsContentCreationLoading] = useState(false) // 控制 Start content creation 按钮的加载状态
  const [showImplementationCard, setShowImplementationCard] = useState(false) // 控制Implementation卡片的显示
  const [showTrendReportAfterClick, setShowTrendReportAfterClick] = useState(false) // 控制 The Trend Report 卡片只在点击后显示
  const [showPostLaunchCard, setShowPostLaunchCard] = useState(false) // 控制 RedNote Post Launch Plan 卡片的显示
  const [isPostingDisabled, setIsPostingDisabled] = useState(false) // 控制 Continue to content posting 按钮的禁用状态
  const [showContinueToPosting, setShowContinueToPosting] = useState(false) // 控制 Continue to content posting 按钮的显示
  const [showSecondPostLaunchCard, setShowSecondPostLaunchCard] = useState(false) // 控制第二个 RedNote Post Launch Plan 卡片的显示
  const [showOriginalWorkThinking, setShowOriginalWorkThinking] = useState(false) // 控制第五个thinking组件的显示
  const [showAnalysisResults, setShowAnalysisResults] = useState(false) // 新增：控制是否显示分析结果
  const [thinkingMessages, setThinkingMessages] = useState<Array<{ id: string, text: string }>>([]) // 存储thinking消息
  const [hasThinkingCompleted, setHasThinkingCompleted] = useState(false) // 跟踪thinking是否已完成
  const [workflowSteps, setWorkflowSteps] = useState<WorkflowStep[]>([]) // 存储工作流步骤
  const [isLoadingSteps, setIsLoadingSteps] = useState(false) // 工作流步骤加载状态
  const [hasCalledReportSync, setHasCalledReportSync] = useState(false) // 防止重复调用 updateBothCardsReportSync
  const [showAiReportThinking, setShowAiReportThinking] = useState(false)
  const industryReportItemRef = useRef<any>()
  const competitiveReportItemRef = useRef<any>()
  /** OperationsManager 相关状态变量 */
  const [operationsManagerDisplayText, setOperationsManagerDisplayText] = useState<string>('') // OperationsManager文本显示内容
  const [showOperationsManagerComponents, setShowOperationsManagerComponents] = useState(false) // 控制OperationsManager组件显示
  const [showOperationsManagerCard, setShowOperationsManagerCard] = useState(false) // 控制OperationsManager卡片显示
  const [operationsManagerThinkingData, setOperationsManagerThinkingData] = useState<string>('') // OperationsManager专用thinking数据
  const [isOperationsManagerActive, setIsOperationsManagerActive] = useState(false) // OperationsManager激活状态

  /** 防止 original_work 重复触发的 ref */

  /** 防止各个工作流重复触发的 refs */
  const hasTriggeredInsightReport = useRef<boolean>(false)
  const hasTriggeredCompetitorReport = useRef<boolean>(false)
  const hasTriggeredPlanningScheme = useRef<boolean>(false)
  const hasTriggeredHotpotsRef = useRef<boolean>(false)
  const hasTriggeredDistillRef = useRef<boolean>(false)

  /** hotpots_analysis 完成状态 */
  const [isHotpotsAnalysisComplete, setIsHotpotsAnalysisComplete] = useState(false)

  /** 监听 hotpots_analysis 数据变化来判断完成状态 */
  useEffect(() => {
    if (stateStore.hotpotsTopics && Object.keys(stateStore.hotpotsTopics).length > 0) {
      /** 检查是否有至少一个 topic 数据 */
      const hasTopicData = ['topic1', 'topic2', 'topic3', 'topic4', 'topic5'].some(
        key => stateStore.hotpotsTopics[key],
      )
      if (hasTopicData) {
        setIsHotpotsAnalysisComplete(true)
      }
    }
  }, [stateStore.hotpotsTopics])

  /** 表单状态管理 */
  const [formDataLocal, setFormDataLocal] = useState<Partial<MarketStep1Params>>({
    brand: '',
    product_name: '',
    industry: '',
    competitor: '',
    product: '', // 独立的表单字段，不使用 userDescription
    pic: '', // 独立的表单字段，不使用 uploadedImage
    industry_id: 83, // 默认值
    ip: '', // 默认值
    role: '', // 默认值
  })
  const [formErrors, setFormErrors] = useState<Record<string, string>>({})
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isUploadingImage, setIsUploadingImage] = useState(false) // 图片上传状态

  /** 监听ButtonStateManager的状态变化，同步按钮状态 */
  useEffect(() => {
    const handleButtonStateChange = (event: CustomEvent) => {
      const { loading, disabled } = event.detail
      setIsButtonLoading(loading)
      setIsButtonDisabled(disabled)
    }

    window.addEventListener('chatWorkflowButtonStateChange', handleButtonStateChange as EventListener)

    return () => {
      window.removeEventListener('chatWorkflowButtonStateChange', handleButtonStateChange as EventListener)
    }
  }, [])

  /** 监听表单数据变化，确保数据更新 */
  useEffect(() => {
    if (formDataLocal.brand || formDataLocal.product_name || formDataLocal.industry) {
    }
  }, [formDataLocal])

  /** 监听 planning_scheme 完成状态，动态更新 Approve Strategy 按钮 */
  useEffect(() => {
    if (planningSchemeCompleted) {
      /** 查找 planning-scheme-report 卡片 */
      const planningCard = trendAg.messageStore.messages.find(msg => msg.meta?.cardId === 'planning-scheme-report')

      if (planningCard && planningCard.card && planningCard.card.cardConfig?.button) {
        /** 更新按钮文本和状态 */
        planningCard.card.cardConfig.button.text = 'Approve Strategy'
        planningCard.card.cardConfig.button.variant = 'gradient-border' as const
        planningCard.card.cardConfig.button.disabled = hasApprovedStrategy // 只有在已经点击过时才禁用

        /** 强制触发重新渲染 */
        trendAg.messageStore.messages = [...trendAg.messageStore.messages]
      }
      else {
      }
    }
  }, [planningSchemeCompleted, hasApprovedStrategy])

  /** 监听 hasApprovedStrategy 状态，更新按钮状态 */
  useEffect(() => {
    if (hasApprovedStrategy) {
      /** 查找 planning-scheme-report 卡片 */
      const planningCard = trendAg.messageStore.messages.find(msg => msg.meta?.cardId === 'planning-scheme-report')

      if (planningCard && planningCard.card) {
        /** 卡片点击仍然显示 planning_scheme 报告（不改变） */

        /** 更新按钮状态为已批准 */
        if (planningCard.card.cardConfig?.button) {
          planningCard.card.cardConfig.button.text = 'Approved'
          planningCard.card.cardConfig.button.variant = 'success' as const
          planningCard.card.cardConfig.button.disabled = true
        }

        /** 强制触发重新渲染 */
        trendAg.messageStore.messages = [...trendAg.messageStore.messages]
      }
    }
  }, [hasApprovedStrategy, stateStore])

  /** 处理 thinking 渲染完成 */
  const handleThinkingComplete = useCallback(() => {
    /** 避免重复触发 */
    if (hasThinkingCompleted) {
      return
    }

    setHasThinkingCompleted(true)

    /** 关闭 loading 状态 */
    if (isThinking) {
      setIsThinking(false)
    }

    /** 收起 thinking 内容 */
    setThinkingExpanded(false)

    /** 短暂延迟后显示表单 */
    setTimeout(() => {
      /** 确保数据已经回填 */
      // @ts-ignore
      if (stateStore.collectedFormData) {
      }

      setShowForm(true)
      stateStore.chatV2FlowMode = 'form'

      /** 清除收集的数据，避免重复回填 */
      setTimeout(() => {
        // @ts-ignore
        if (stateStore.collectedFormData) {
          // @ts-ignore
          stateStore.collectedFormData = null
        }
      }, 1000) // 表单显示1秒后清除数据
    }, 1000) // 1秒延迟，让用户看到渲染完成
  }, [isThinking, stateStore, hasThinkingCompleted])

  /** 监听 store 中的 thinking 内容并更新 */
  useEffect(() => {
    // @ts-ignore
    const thinkingData = stateStore?.thinkingContent || thinkingContent

    if (thinkingData) {
      /** 将 thinking 内容设置为消息 */
      setThinkingMessages([{
        id: 'thinking-content',
        text: thinkingData,
      }])

      /** 不再立即解除 loading，等待渲染完成回调 */
      if (chatV2FlowMode === 'thinking') {
      }
    }
    // @ts-ignore
    else if (stateStore?.sseStreamMessages) {
      /** 如果没有 thinking 内容，使用 SSE 消息作为备份 */
      const sseMessages = stateStore.sseStreamMessages
      if (sseMessages && Array.isArray(sseMessages) && sseMessages.length > 0) {
        const messages = sseMessages.map((msg, index) => ({
          id: `msg-${index}`,
          text: msg.content || msg.text || 'Processing...',
        }))
        setThinkingMessages(messages)
      }
    }
    else if (chatV2FlowMode === 'thinking') {
      /** 保持loading状态 */
    }
  }, [stateStore, thinkingContent, isThinking, chatV2FlowMode])

  /** 监听 store 中的收集数据并更新表单 */
  useEffect(() => {
    if (collectedFormData && typeof collectedFormData === 'object') {
      /** 直接更新表单数据 */
      setFormDataLocal((prev) => {
        const updated = {
          ...prev,
          brand: collectedFormData.brand_name || prev.brand,
          product_name: collectedFormData.product_name || prev.product_name,
          industry: collectedFormData.industry_name || prev.industry,
          industry_id: collectedFormData.industry_id || prev.industry_id, // 新增：行业ID映射
          competitor: collectedFormData.competitor_name || prev.competitor,
          product: collectedFormData.product_key_features || prev.product, // 新增：产品描述映射
          account_link: collectedFormData.account_link || prev.account_link, // 新增：账户链接映射
        }
        return updated
      })

      /** 不要立即清除数据，等待表单显示后再清除 */
      /** 移除了 setTimeout 清除数据的逻辑 */
    }
  }, [collectedFormData, stateStore])

  /** 监听 userDescription 和 uploadedImage 并更新到表单的 product 和 pic 字段 */
  useEffect(() => {
    if (userDescription || uploadedImage) {
      setFormDataLocal((prev) => {
        const updated = {
          ...prev,
          product: userDescription || prev.product, // 回填到 Product description 字段
          pic: uploadedImage || prev.pic, // 回填到 Upload 字段
        }

        /** 同时更新 userSubmitPic */
        if (updated.pic) {
          stateStore.userSubmitPic = updated.pic
        }

        return updated
      })
    }
  }, [userDescription, uploadedImage])

  /** 监听 startOriginalWork 事件 */
  const handleStartOriginalWork = useCallback(async () => {
    /** 多重防重检查 - 检查请求状态和已触发标记 */
    if (stateStore.originalWorkRequesting || stateStore.hasTriggeredOriginalWork) {
      /** 只触发显示预览事件（非用户主动点击） */
      ChatEventBus.emit('showImagePreview', { isUserAction: false })
      return
    }

    /** 多重防重检查 - 检查请求状态和已触发标记 */
    if (stateStore.originalWorkRequesting || stateStore.hasTriggeredOriginalWork) {
      /** 只触发显示预览事件（非用户主动点击） */
      ChatEventBus.emit('showImagePreview', { isUserAction: false })
      return
    }

    /** 触发预览事件并设置 loading 状态（非用户主动点击） */
    ChatEventBus.emit('showImagePreview', { isUserAction: false })

    /** 设置 loading 开始 */
    stateStore.originalWorkLoading = true
    stateStore.originalWorkProgress = {} // 重置进度

    /** 调用 original_work 工作流 */
    try {
      /** 导入 callOriginalWorkAPI 函数 */
      const { callOriginalWorkAPI } = await import('../stores/cozeStreamApi')

      /** 获取动态数据 */
      const planningReport = stateStore.planningReportContent || ''

      /** 合并 hotpots_analysis 的 topics 数据 */
      const hotpotsTopics = stateStore.hotpotsTopics

      const anyTopic = [
        hotpotsTopics?.topic1,
        hotpotsTopics?.topic2,
        hotpotsTopics?.topic3,
        hotpotsTopics?.topic4,
        hotpotsTopics?.topic5,
        hotpotsTopics?.topic1_detail,
        hotpotsTopics?.topic2_detail,
        hotpotsTopics?.topic3_detail,
        hotpotsTopics?.topic4_detail,
        hotpotsTopics?.topic5_detail,
      ].filter(t => t).join('\n\n') || ''

      const parameters = {
        planning_report: planningReport,
        any_topic: anyTopic,
        user_submit_pic: stateStore.userSubmitPic || '',
      }

      /** 存储响应数据 */
      const originalWorkData: Record<string, any> = {}

      /** 调用 original_work 工作流，传递正确的参数 */
      await callOriginalWorkAPI(
        (data) => {
          /** 处理流式数据 - 现在每个节点返回完整内容 */
          if (data.nodeTitle && data.content) {
            /** 标记该节点已接收到数据 */
            stateStore.originalWorkProgress[data.nodeTitle] = true

            /** 直接存储完整内容，不再需要累积 */
            originalWorkData[data.nodeTitle] = data.content

            /** 存储到 state store */
            if (!stateStore.originalWorkData) {
              stateStore.originalWorkData = {}
            }
            stateStore.originalWorkData[data.nodeTitle] = data.content

            const contentInfo = Array.isArray(data.content)
              ? `数组(${data.content.length}项): ${JSON.stringify(data.content).substring(0, 100)}...`
              : `字符串(${data.content.length}字符): ${data.content.substring(0, 100)}...`
          }
        },
        () => {
          stateStore.originalWorkLoading = false
        },
        () => {
          /** 设置持久化完成标记 */
          localStorage.setItem('original_work_completed', 'true')

          /** 设置 loading 结束 */
          stateStore.originalWorkLoading = false

          /** 存储完整数据到 stateStore */
          stateStore.originalWorkData = originalWorkData
        },
        parameters,
      )
    }
    catch (error) {
      stateStore.originalWorkLoading = false
    }
  }, [stateStore]) // useCallback 的依赖

  useEffect(() => {
    /** 添加事件监听 */
    ChatEventBus.on('startOriginalWork', handleStartOriginalWork)

    /** 添加页面可见性变化监听 */
    const handleVisibilityChange = () => {
      if (!document.hidden && document.visibilityState === 'visible') {
        /** 如果工作流之前被触发过但请求状态为false（可能因页面切换中断），不重复触发 */
        if (stateStore.hasTriggeredOriginalWork && !stateStore.originalWorkRequesting) {
          /** 如果有数据，确保显示预览 */
          if (stateStore.originalWorkData) {
            ChatEventBus.emit('showImagePreview', { isUserAction: false })
          }
        }
      }
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)

    /** 清理函数 */
    return () => {
      ChatEventBus.off('startOriginalWork', handleStartOriginalWork)
      document.removeEventListener('visibilitychange', handleVisibilityChange)
    }
  }, [handleStartOriginalWork, stateStore]) // 添加 stateStore 依赖

  /** 处理 ChatV2 流程状态变化 */
  useEffect(() => {
    if (chatV2FlowMode === 'thinking') {
      /** 启动 Thinking 流程 */
      setIsThinking(true)
      // setShowForm(true) // 确保表单隐藏
      setHasThinkingCompleted(false) // 重置完成状态

      /** 检查是否已经有thinking数据 */
      // @ts-ignore
    }
    else if (chatV2FlowMode === 'workflow') {
      // setShowForm(true)
      setShowWorkflow(true)
    }
  }, [chatV2FlowMode, stateStore, thinkingContent])

  /** 单独处理 text_link_disassemble 工作流 */
  useEffect(() => {
    if (chatV2FlowMode === 'text_link_disassemble') {
      /** 处理 text_link_disassemble 工作流模式 */
      setIsThinking(true)
      setThinkingExpanded(true) // 展开 thinking 内容
      // setShowForm(true)
      setIsLoadingSteps(false)

      /** 直接执行 text_link_disassemble 工作流 */
      handleTextLinkDisassembleWorkflow()
    }
  }, [chatV2FlowMode]) // 只依赖于 chatV2FlowMode

  /** 当分析开始时，自动滚动到底部 */
  useEffect(() => {
    if (isAnalysisStarted) {
      /** 使用 requestAnimationFrame 确保 DOM 已更新 */
      requestAnimationFrame(() => {
        /** 第一次滚动：确保容器可见 */
        const container = document.querySelector('.ChatPageContainer .overflow-auto')
        if (container) {
          container.scrollTo({
            top: container.scrollHeight,
            behavior: 'smooth',
          })
        }

        /** 延迟后再次滚动：确保内容加载完成 */
        setTimeout(() => {
          if (container) {
            container.scrollTo({
              top: container.scrollHeight,
              behavior: 'smooth',
            })
          }
          /** ChatHistory 内部滚动 */
          chatHistoryRef.current?.scrollToBottom?.()
        }, 300)
      })
    }
  }, [isAnalysisStarted])

  /** 表单处理函数 */
  const handleFormChange = (field: keyof MarketStep1Params, value: string) => {
    setFormDataLocal(prev => ({ ...prev, [field]: value }))
    if (formErrors[field]) {
      setFormErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  /** 处理 text_link_disassemble 工作流 */
  const handleTextLinkDisassembleWorkflow = async () => {
    try {
      const taskInstanceId = stateStore.taskInstanceId || localStorage.getItem('taskInstanceId')
      const userMessage = stateStore.userDescription || localStorage.getItem('userMessage')

      if (!taskInstanceId || !userMessage) {
        setIsThinking(false)
        return
      }

      /** 保存 taskInstanceId 到 localStorage，供后续 confirm-form 使用 */
      localStorage.setItem('text_link_taskInstanceId', taskInstanceId)

      /** 准备 SSE 请求参数 */
      const sseParams = {
        taskInstanceId,
        platform: 'rednote',
        workflowName: 'text_link_disassemble',
        parameters: {
          user_submit: userMessage,
        },
      }

      /** 调用 SSE 流式接口 */
      const { userStore } = await import('@/store/userStore')
      const sseResponse = await fetch(`${import.meta.env.VITE_API_BASE_URL}/app/market/stream/execute-main`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${userStore.token}`,
        },
        body: JSON.stringify(sseParams),
      })

      if (!sseResponse.ok) {
        throw new Error(`SSE request failed: ${sseResponse.status} ${sseResponse.statusText}`)
      }

      /** 处理 SSE 流式数据 */
      const reader = sseResponse.body?.getReader()
      const decoder = new TextDecoder()

      if (reader) {
        let buffer = ''
        let thinkingContent = ''
        let formData: any = {}
        let chunkCount = 0

        while (true) {
          const { done, value } = await reader.read()
          if (done) {
            break
          }

          const chunk = decoder.decode(value, { stream: true })
          chunkCount++

          buffer += chunk
          const lines = buffer.split('\n')
          buffer = lines.pop() || ''

          for (const line of lines) {
            if (line.trim() === '')
              continue

            if (line.startsWith('data:')) {
              const data = line.slice(5).trim()

              if (data === '[DONE]') {
                break
              }

              try {
                const jsonData = JSON.parse(data)

                /** 收集 thinking 内容并实时更新 */
                if (jsonData.node_title === 'thinking' && jsonData.content) {
                  thinkingContent += jsonData.content

                  /** 推送数据到TEXT_LINK_THINKING流 */
                  streamingDataManager.pushData(STREAM_IDS.TEXT_LINK_THINKING, thinkingContent, true)

                  /** 实时更新 thinking 消息，触发流式渲染（保持兼容性） */
                  setThinkingMessages([{
                    id: 'thinking-content',
                    text: thinkingContent,
                  }])

                  /** 确保 isThinking 状态为 true */
                  setIsThinking(true)
                  // setShowForm(true)
                }

                /** 收集表单数据 - 根据实际的 node_title 处理 */
                if (jsonData.node_title && jsonData.content !== undefined && jsonData.content !== '') {
                  /** 直接根据 node_title 映射到表单字段 */
                  switch (jsonData.node_title) {
                    case 'brand_name':
                      formData.brand = jsonData.content
                      break
                    case 'product_name':
                      formData.product_name = jsonData.content
                      break
                    case 'industry_name':
                      formData.industry = jsonData.content
                      break
                    case 'competitor_name':
                      formData.competitor = jsonData.content
                      break
                    case 'industry_id':
                      formData.industry_id = Number.parseInt(jsonData.content) || 0
                      break
                    case 'platform':
                      formData.platform = jsonData.content
                      break
                    case 'inputText':
                      formData.product = jsonData.content
                      break
                    case 'product':
                      formData.product = jsonData.content
                      break
                    default:
                      /** 其他节点不处理 */
                      break
                  }
                }
              }
              catch (e) {
              }
            }
          }
        }

        /** 标记TEXT_LINK_THINKING数据流完成 */
        streamingDataManager.completeStream(STREAM_IDS.TEXT_LINK_THINKING)

        /** 更新表单数据 */
        if (Object.keys(formData).length > 0) {
          /** 如果没有 product_name，使用默认值或从用户输入中提取 */
          const userMessage = stateStore.userDescription || localStorage.getItem('userMessage') || ''
          const extractedProductName = formData.product_name // 默认值

          /** 直接使用收集到的数据，已经在上面映射过了 */
          const normalizedFormData = {
            brand: formData.brand || '',
            product_name: extractedProductName,
            industry: formData.industry || '',
            competitor: formData.competitor || '',
            product: formData.product || userMessage || '好用', // 使用用户描述或默认值
            pic: formData.pic || stateStore?.uploadedImage || '',
          }

          /** 延迟更新，确保组件已经渲染 */
          setTimeout(() => {
            setFormDataLocal((prev) => {
              const newData = {
                ...prev,
                ...normalizedFormData,
              }
              return newData
            })
          }, 500) // 稍微延迟，等待 thinking 渲染完成
        }
        else {
        }

        /** 不要立即关闭 thinking，让 TypewriterTextWithScroll 组件完成渲染 */
        // onThinkingComplete 回调会在渲染完成后自动调用
      }
    }
    catch (error) {
      setIsThinking(false)
    }
  }

  const handleFormSubmit = async () => {
    if (isSubmitting)
      return

    /** 如果图片正在上传，阻止提交 */
    if (isUploadingImage) {
      return
    }

    setFormErrors({})
    const errors: Record<string, string> = {}

    /** 验证所有必填字段 */
    const requiredFields = [
      { key: 'brand', label: 'Please enter the brand name to continue.' },
      { key: 'product_name', label: 'Please enter the product name to continue.' },
      { key: 'industry', label: 'Please enter the industry to continue.' },
      { key: 'competitor', label: 'Please enter the competitor information to continue.' },
      { key: 'product', label: 'Please enter the product description to continue.' },
      { key: 'pic', label: 'Please upload an image to continue.' },
    ]

    requiredFields.forEach(({ key, label }) => {
      const value = formDataLocal[key as keyof typeof formDataLocal]
      if (!value || (typeof value === 'string' && !value.trim())) {
        errors[key] = label
      }

      /** 特殊检查：确保图片是 OSS URL 而不是 blob URL */
      if (key === 'pic' && value && typeof value === 'string' && value.startsWith('blob:')) {
        errors[key] = 'The image is being uploaded. Please submit again later.'
      }
    })

    if (Object.keys(errors).length > 0) {
      setFormErrors(errors)
      return
    }

    setIsSubmitting(true)

    /** 提交表单时关闭 thinking loading */
    setIsThinking(false)

    try {
      /** 获取 taskInstanceId - 优先从 text_link_disassemble 响应中获取，否则使用初始的 */
      const textLinkTaskId = localStorage.getItem('text_link_taskInstanceId')
      const initialTaskId = localStorage.getItem('taskInstanceId')
      const taskInstanceId = textLinkTaskId || initialTaskId || ''

      if (!taskInstanceId) {
        setIsSubmitting(false)
        return
      }

      /** 准备接口请求参数 */
      const requestPayload = {
        taskInstanceId,
        formData: {
          brand_name: formDataLocal.brand || '',
          product_name: formDataLocal.product_name || '',
          industry_name: formDataLocal.industry || '',
          competitor_name: formDataLocal.competitor || '',
          inputText: formDataLocal.product || '',
          pic: formDataLocal.pic || '',
        },
      }

      /** 保存用户提交的图片到 store */
      if (requestPayload.formData.pic) {
        stateStore.userSubmitPic = requestPayload.formData.pic
      }

      /** 调用确认表单接口 - request.post 已经返回解析后的数据 */
      const response = await request.post('/app/market/confirm-form', requestPayload)

      /** 检查响应状态 - 注意：response 已经是 data 对象 */
      /** 如果 request 工具已经处理了错误，这里只需要检查业务逻辑 */
      if (!response) {
        throw new Error('No response from server')
      }

      /** 如果返回了新的 taskInstanceId，使用新的 */
      let finalTaskInstanceId = taskInstanceId
      /** 检查两种可能的响应格式 */
      const responseData = response.data || response
      const newTaskId = responseData?.taskInstanceId
      if (newTaskId) {
        /** 更新 taskInstanceId 为新的值 */
        finalTaskInstanceId = newTaskId
        localStorage.setItem('confirmed_taskInstanceId', finalTaskInstanceId)
      }

      /** 准备完整的表单数据用于后续流程 */
      const completeFormData = {
        industry: formDataLocal.industry || '',
        industry_id: 83,
        product_name: formDataLocal.product_name || '',
        ip: '专研彩妆',
        brand: formDataLocal.brand || '',
        role: '中国时尚彩妆领导品牌',
        product: formDataLocal.product || '',
        pic: formDataLocal.pic || '',
        competitor: formDataLocal.competitor || '',
        company: formDataLocal.brand || '',
        marketing_strategy: '好用',
        product_market: '',
        competitor_info: '女性彩妆',
      }

      /** 保存到 stateStore 并切换到工作流模式 */
      stateStore.cacheFormData = completeFormData
      stateStore.chatV2FlowMode = 'workflow'
      setIsLoadingSteps(true) // 开始加载工作流步骤

      /** 保存表单数据供 data_report 使用 */
      const dataReportParams = {
        brand_name: formDataLocal.brand || '',
        competitor_name: formDataLocal.competitor || '',
        industry_id: '83', // 转为字符串格式
        industry_name: formDataLocal.industry || '',
        product_name: formDataLocal.product_name || '',
        platform: 'rednote',
      }
      localStorage.setItem('dataReportParams', JSON.stringify(dataReportParams))

      /** 只有在确认成功时才执行 command_pot */
      /** 根据你提供的响应格式，response 本身就是完整的响应对象 */
      const confirmStatus = responseData?.confirmStatus
      if (confirmStatus === 'SUCCESS') {
        /** 执行 command_pot 工作流 */
        const executeCommandPot = async () => {
          try {
            const { userStore } = await import('@/store/userStore')
            const token = userStore.token
            /** 从 localStorage 和 stateStore 获取最新的 detectedIntent */
            const latestIntent = stateStore.detectedIntent || localStorage.getItem('detectedIntent') || 'create_post'

            const executeParams = {
              taskInstanceId: finalTaskInstanceId, // 使用新的或原有的 taskInstanceId
              platform: 'rednote',
              workflowName: 'command_pot',
              parameters: {
                user_submit: stateStore.userDescription || '',
                intent_result: latestIntent, // 使用最新的 intent 值（可能来自 dialogue 工作流的 code 节点）
              },
            }

            const sseResponse = await fetch(`${import.meta.env.VITE_API_BASE_URL}/app/market/stream/execute-main`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`,
              },
              body: JSON.stringify(executeParams),
            })

            if (!sseResponse.ok) {
              throw new Error(`Failed to execute command_pot: ${sseResponse.statusText}`)
            }

            /** 处理 SSE 流响应 */
            const reader = sseResponse.body?.getReader()
            const decoder = new TextDecoder()

            if (reader) {
              let buffer = ''

              while (true) {
                const { done, value } = await reader.read()
                if (done) {
                  break
                }

                const chunk = decoder.decode(value, { stream: true })
                buffer += chunk
                const lines = buffer.split('\n')
                buffer = lines.pop() || ''

                for (const line of lines) {
                  if (line.trim() === '')
                    continue

                  if (line.startsWith('data:')) {
                    const data = line.slice(5).trim()
                    if (data === '[DONE]') {
                      continue
                    }

                    try {
                      const jsonData = JSON.parse(data)

                      /** 解析步骤数据 */
                      if (jsonData.content && jsonData.node_title === '输出') {
                        try {
                          const content = jsonData.content
                          const parsedSteps: WorkflowStep[] = []

                          /** 按行分割内容 */
                          const lines = content.split('\n').filter((line: string) => line.trim())

                          lines.forEach((line: string, index: number) => {
                            /** 解析格式："第X步. 部门名称：描述" */
                            const chineseMatch = line.match(/第(.+?)步\.\s*([^：:]+)[：:](.+)/)

                            if (chineseMatch) {
                              const stepNumber = index + 1 // 使用索引+1作为步骤号
                              const agent = `${chineseMatch[2].trim()}`
                              const description = chineseMatch[3].trim()

                              parsedSteps.push({
                                step: stepNumber,
                                agent,
                                description,
                                borderColor: stepNumber === 1
                                  ? '#DD9DFF'
                                  : '#36D3FF',
                              })
                            }
                          })

                          if (parsedSteps.length > 0) {
                            setWorkflowSteps(parsedSteps)
                            setIsLoadingSteps(false)
                          }
                        }
                        catch (parseError) {
                        }
                      }
                    }
                    catch (e) {
                    }
                  }
                }
              }
            }
          }
          catch (error) {
          }
        }

        /** 异步执行 command_pot，不阻塞UI */
        executeCommandPot()
      }
    }
    catch (error) {
    }
    finally {
      setIsSubmitting(false)
    }
  }

  /** 转换 MarketStep1Params 到 TrendStep1Params */
  const convertToTrendParams = (marketParams: MarketStep1Params): TrendStep1Params => {
    return {
      brand: marketParams.brand,
      product_name: marketParams.product_name,
      industry: marketParams.industry,
      industry_id: marketParams.industry_id,
      competitor: marketParams.competitor || '',
      product: marketParams.product,
      role: marketParams.role,
      company: marketParams.company || marketParams.brand,
      pic: marketParams.pic,
      ip: marketParams.ip,
      marketing_strategy: marketParams.marketing_strategy || '',
      product_market: marketParams.product_market || '',
      competitor_info: marketParams.competitor_info || '',
    }
  }

  /** 为两个卡片同步调用流式API更新报告内容 - 修复重复调用问题 */
  const updateBothCardsReportSync = useCallback(async (insightReportItemId: string, competitorReportItemId: string) => {
    try {
      /** 导入流式API函数 */
      const { callCozeStreamAPI, callCompetitorCozeStreamAPI } = await import('../stores/cozeStreamApi')

      /** 第一步：执行 insight_report 流式接口 - 添加防重复检查 */
      if (!hasTriggeredInsightReport.current) {
        hasTriggeredInsightReport.current = true
        let isFirstContent = true
        let dataReportCompleted = false

        /** 开始接收数据时立即打开报告面板，以便实时看到渲染 */
        stateStoreRef.current.isReportOpen = true
        eventBus.emit(DistributionEvent.SetActiveTab, insightReportItemId)

        await callCozeStreamAPI(
          (data: ParsedStreamData) => {
            const { reportStore, messageStore } = trendAg
            const targetItem = reportStore.items.find(item => item.id === insightReportItemId)

            if (data.type === 'report_title' && data.title && targetItem) {
              targetItem.title = data.title

              /** 第一个卡片：初始显示Loading，数据响应后更新标题 */
              const firstCard = createCardMessage(
                {
                  title: data.title, // 初始通用标题
                  description: 'Analyzing industry trends and market dynamics...',
                  variant: 'success',
                  onClick: () => {
                    /** 直接显示对应的报告内容，不需要tab切换 */
                    stateStoreRef.current.isReportOpen = true
                    stateStoreRef.current.showTrendSelection = false // 隐藏 TrendSelection
                    taskStoreRef.current.currentStep = 'step0'
                    eventBus.emit(DistributionEvent.SetActiveTab, industryReportItemRef.current.id)
                    /** 关闭图片预览卡片 */
                    ChatEventBus.emit('hideImagePreview')
                  },
                  cardConfig: {
                    leftIcon: {
                      show: true,
                      icon: 'researchAnalyst',
                      size: 'lg' as const,
                    },
                    rightIcon: {
                      show: true,
                      icon: 'card-right-icon1',
                      size: 'md' as const,
                    },
                    content: {
                      title: data.title, // 初始通用标题
                      description: 'Analyzing industry trends and market dynamics...',
                    },
                    layout: 'simple' as const,
                  },
                },
                {
                  meta: {
                    step: 'step0',
                    cardId: 'interior-design-report',
                  },
                },
                trendAg,
              )
              firstCard.timestamp = Date.now() // 第一个卡片
              /** 更新第一个卡片的标题 */
            }
            else if (data.type === 'report_display' && data.content && targetItem) {
              if (isFirstContent) {
                targetItem.content = data.content
                isFirstContent = false
                /** 首次接收内容时，确保报告面板显示正确的 tab */
                eventBus.emit(DistributionEvent.SetActiveTab, insightReportItemId)
              }
              else {
                targetItem.content += data.content
              }

              /** 触发 store 更新以确保界面重新渲染 */
              reportStore.items = [...reportStore.items]
            }
            else if (data.type === 'thinking_stream' && data.content) {
            /** 将insight_report的thinking数据传递给流式ThinkingStream组件 */

              setThinkingData(data.content)

              /** 推送数据到Insight阶段的流式thinking组件 */
              streamingDataManager.pushData(STREAM_IDS.INSIGHT_THINKING, data.content, true)

              /** 保持原有的stateStore更新以兼容其他组件 */
              stateStore.thinkingContent = data.content
              stateStore.isThinkingActive = true
            }
            else if (data.type === 'workflow_param' && data.nodeTitle === 'brand_reasoning_content') {
              /** 2. data_report工作流完成后：开始显示thinking的流式打字机输出效果 */
              if (!dataReportCompleted) {
                dataReportCompleted = true
                setDataReportCompleted(true)
              }
            }
            else if (data.type === 'complete') {
              setInsightReportCompleted(true) // 设置 insight_report 完成状态
            }
          },
          (error: Error) => {
            const { reportStore } = trendAg
            const targetItem = reportStore.items.find(item => item.id === insightReportItemId)
            if (targetItem) {
              targetItem.content = `行业洞察分析失败：${error.message}\n\n请检查网络连接后重试。`
            }
          },
          () => {
            setInsightReportCompleted(true) // 确保在完成回调中也设置状态
          },
        )
      }
      else {
        setInsightReportCompleted(true) // 如果已经触发过，直接设置为完成
      }

      /** 第二步：调用 competitor_report 流式接口 - 添加防重复检查 */
      if (!hasTriggeredCompetitorReport.current) {
        hasTriggeredCompetitorReport.current = true
        let isFirstContentCompetitor = true

        /** 切换到 competitor_report 的 tab，以便实时看到渲染 */
        setTimeout(() => {
          eventBus.emit(DistributionEvent.SetActiveTab, competitorReportItemId)
        }, 100)

        await callCompetitorCozeStreamAPI(
          (data: ParsedStreamData) => {
            const { reportStore, messageStore } = trendAg
            const targetItem = reportStore.items.find(item => item.id === competitorReportItemId)

            if (data.type === 'report_title' && data.title && targetItem) {
              targetItem.title = data.title
              /** 第二个卡片：初始显示Loading，数据响应后更新标题 */
              const secondCard = createCardMessage(
                {
                  title: data.title, // 初始通用标题
                  description: 'Analyzing competitor strategies and positioning opportunities...',
                  variant: 'success',
                  onClick: () => {
                    /** 直接显示对应的报告内容，不需要tab切换 */
                    stateStoreRef.current.isReportOpen = true
                    stateStoreRef.current.showTrendSelection = false // 隐藏 TrendSelection
                    taskStoreRef.current.currentStep = 'step0'
                    eventBus.emit(DistributionEvent.SetActiveTab, competitiveReportItemRef.current.id)
                    /** 关闭图片预览卡片 */
                    ChatEventBus.emit('hideImagePreview')
                  },
                  cardConfig: {
                    leftIcon: {
                      show: true,
                      icon: 'researchAnalyst',
                      size: 'lg' as const,
                    },
                    rightIcon: {
                      show: true,
                      icon: 'card-right-icon2',
                      size: 'md' as const,
                    },
                    content: {
                      title: data.title, // 初始通用标题
                      description: 'Analyzing competitor strategies and positioning opportunities...',
                    },
                    layout: 'simple' as const,
                  },
                },
                {
                  meta: {
                    step: 'step0',
                    cardId: 'competitive-analysis',
                  },
                },
                trendAg,
              )
              secondCard.timestamp = Date.now() // 第二个卡片
            }
            else if (data.type === 'report_display' && data.content && targetItem) {
              if (isFirstContentCompetitor) {
                targetItem.content = data.content
                isFirstContentCompetitor = false
                /** 首次接收内容时，切换到 competitor tab */
                eventBus.emit(DistributionEvent.SetActiveTab, competitorReportItemId)
              }
              else {
                targetItem.content += data.content
              }

              /** 触发 store 更新以确保界面重新渲染 */
              reportStore.items = [...reportStore.items]
            }
            else if (data.type === 'complete') {
              setCompetitorReportCompleted(true) // 设置 competitor_report 完成状态
            }
          },
          (error: Error) => {
            const { reportStore } = trendAg
            const targetItem = reportStore.items.find(item => item.id === competitorReportItemId)
            if (targetItem) {
              targetItem.content = `竞争对手分析失败：${error.message}\n\n请检查网络连接后重试。`
            }
          },
          () => {
            setCompetitorReportCompleted(true) // 确保在完成回调中也设置状态
          },
        )
      }
      else {
        setCompetitorReportCompleted(true) // 如果已经触发过，直接设置为完成
      }
    }
    catch (error) {

    }
  }, [stateStore, setThinkingData, setInsightReportCompleted, setCompetitorReportCompleted, hasTriggeredInsightReport, hasTriggeredCompetitorReport])

  /** 独立的planning_scheme请求方法 - 为Continue to Strategy按钮专用 */
  /** 转换 distill_daren_list 数据格式 */
  const transformDistillDarenListData = (listData: any) => {
    const result: any = {
      KOL: [],
      KOC: [],
      Regulars: [],
      All: [],
    }

    /** 处理原始数据格式 */
    const data = Array.isArray(listData)
      ? listData[0]
      : listData

    /** 处理 KOL 数据 */
    if (data.kol && Array.isArray(data.kol)) {
      result.KOL = data.kol.map((item: any, index: number) => {
        const info = item.info || item
        return {
          id: `KOL-${index}`,
          image: info.imageList || [],
          title: info.title || '',
          user: { name: info.nick || '' },
          stats: {
            likes: info.like
              ? (info.like >= 10000
                  ? `${(info.like / 10000).toFixed(1)}w`
                  : info.like >= 1000
                    ? `${(info.like / 1000).toFixed(1)}k`
                    : String(info.like))
              : '0',
            comm: String(info.comm || 0),
            read: String(info.read || 0),
          },
          noteLink: item.noteLink || '',
          desc: info.desc || '',
        }
      })
    }

    /** 处理 KOC 数据 */
    if (data.koc && Array.isArray(data.koc)) {
      result.KOC = data.koc.map((item: any, index: number) => {
        const info = item.info || item
        return {
          id: `KOC-${index}`,
          image: info.imageList || [],
          title: info.title || '',
          user: { name: info.nick || '' },
          stats: {
            likes: info.like
              ? (info.like >= 10000
                  ? `${(info.like / 10000).toFixed(1)}w`
                  : info.like >= 1000
                    ? `${(info.like / 1000).toFixed(1)}k`
                    : String(info.like))
              : '0',
            comm: String(info.comm || 0),
            read: String(info.read || 0),
          },
          noteLink: item.noteLink || '',
          desc: info.desc || '',
        }
      })
    }

    /** 处理素人数据 */
    if (data.suren && Array.isArray(data.suren)) {
      result.Regulars = data.suren.map((item: any, index: number) => {
        const info = item.info || item
        return {
          id: `Regulars-${index}`,
          image: info.imageList || [],
          title: info.title || '',
          user: { name: info.nick || '' },
          stats: {
            likes: info.like
              ? (info.like >= 10000
                  ? `${(info.like / 10000).toFixed(1)}w`
                  : info.like >= 1000
                    ? `${(info.like / 1000).toFixed(1)}k`
                    : String(info.like))
              : '0',
            comm: String(info.comm || 0),
            read: String(info.read || 0),
          },
          noteLink: item.noteLink || '',
          desc: info.desc || '',
        }
      })
    }

    /** 合并所有数据到 All */
    result.All = [...result.KOL, ...result.KOC, ...result.Regulars]

    return result
  }

  /** 自动触发 hotpots_analysis 和 distill_daren_list 工作流 */
  const triggerWorkflowsAfterPlanningScheme = useCallback(async () => {
    const currentTaskInstanceId = stateStore?.taskInstanceId || localStorage.getItem('taskInstanceId')
    if (!currentTaskInstanceId) {
      return
    }

    try {
      /** 1. 触发 hotpots_analysis 工作流 */
      if (!hasTriggeredHotpotsRef.current && !stateStore.hasTriggeredHotpotsAnalysis) {
        hasTriggeredHotpotsRef.current = true
        stateStore.hasTriggeredHotpotsAnalysis = true

        const { callHotpotsAnalysisAPI } = await import('../stores/cozeStreamApi')

        callHotpotsAnalysisAPI(
          (data) => {
            const topicTitleList = ['topic1', 'topic2', 'topic3', 'topic4', 'topic5']
            const topicDetailNodeTitle = ['topic1_detail', 'topic2_detail', 'topic3_detail', 'topic4_detail', 'topic5_detail']

            if (data.nodeTitle && data.content) {
              if (topicTitleList.includes(data.nodeTitle) || topicDetailNodeTitle.includes(data.nodeTitle)) {
                if (!stateStore.hotpotsTopics) {
                  stateStore.hotpotsTopics = {}
                }
                stateStore.hotpotsTopics[data.nodeTitle] = data.content
              }
            }
          },
          (error) => {
          },
          () => {
            stateStore.isHotpotsAnalysisComplete = true
          },
          {
            planning_report: stateStore.planningReportContent || '',
          },
          currentTaskInstanceId as string,
        )
      }

      /** 2. 触发 distill_daren_list 工作流 */
      if (!hasTriggeredDistillRef.current && !stateStore.hasTriggeredDistillDarenList) {
        hasTriggeredDistillRef.current = true
        stateStore.hasTriggeredDistillDarenList = true

        if (!stateStore.distillDarenListData) {
          stateStore.distillDarenListData = {
            loading: true,
            KOC: [],
            KOL: [],
            Regulars: [],
            All: [],
          }
        }

        if (stateStore.trendSelectionState) {
          stateStore.trendSelectionState.showRednoteList = true
        }

        const { callDistillDarenListAPI } = await import('../stores/cozeStreamApi')

        callDistillDarenListAPI(
          (data) => {
            if (data.nodeTitle && data.content && stateStore.distillDarenListData) {
              try {
                if (data.nodeTitle === 'biji_list' || data.node_title === 'biji_list') {
                  const parsedData = JSON.parse(data.content)
                  const listData = Array.isArray(parsedData)
                    ? parsedData
                    : parsedData.list || parsedData.data || parsedData

                  if (Array.isArray(listData)) {
                    const transformedData = transformDistillDarenListData(listData)
                    Object.assign(stateStore.distillDarenListData, transformedData)
                  }
                }
              }
              catch (error) {

              }
            }
          },
          (error) => {

          },
          () => {
            if (stateStore.distillDarenListData) {
              stateStore.distillDarenListData.loading = false
            }
          },
          currentTaskInstanceId as string,
        )
      }

      /** 3. 显示 TrendSelectionPage - 在触发工作流后显示 */
      setTimeout(() => {
        stateStore.showTrendSelection = true
      }, 500)
    }
    catch (error) {

    }
  }, [stateStore])

  const executePlanningSchemeOnly = useCallback(async (planningReportItemId: string) => {
    /** 检查是否已经触发过 */
    if (hasTriggeredPlanningScheme.current) {
      setPlanningSchemeCompleted(true) // 如果已经触发过，直接设置为完成
      return
    }

    /** 标记已触发，防止重复 */
    hasTriggeredPlanningScheme.current = true

    /** 重置 planning_scheme 完成状态 */
    setPlanningSchemeCompleted(false)

    /** 获取 taskInstanceId */
    const currentTaskInstanceId = stateStore.taskInstanceId || localStorage.getItem('confirmed_taskInstanceId') || localStorage.getItem('taskInstanceId') || ''

    if (!currentTaskInstanceId) {

    }

    try {
      /** 导入planning_scheme专用的流式API函数 */
      const { callPlanningSchemeCozeStreamAPI } = await import('../stores/cozeStreamApi')

      /** 执行 planning_scheme 流式接口 */
      let isFirstContent = true
      let collectedPlanningReport = '' // 收集完整的 planning_report 内容

      await callPlanningSchemeCozeStreamAPI(
        (data: ParsedStreamData) => {
          const { reportStore } = trendAg
          const targetItem = reportStore.items.find(item => item.id === planningReportItemId)

          if (data.type === 'report_title' && data.title && targetItem) {
            targetItem.title = data.title
          }
          else if (data.type === 'report_display' && data.content && targetItem) {
            if (isFirstContent) {
              targetItem.content = data.content
              isFirstContent = false
            }
            else {
              targetItem.content += data.content
            }

            /** 收集 planning_report 内容 - 注意：planning_report1 是用于显示的，planning_report 是实际的数据 */
            if (data.nodeTitle === 'planning_report1') {
              collectedPlanningReport += data.content
            }
          }
          else if (data.type === 'workflow_param' && data.nodeTitle === 'planning_report') {
            /** 收集 workflow_param 类型的 planning_report - 这是实际的完整数据 */
            collectedPlanningReport += data.content
          }
          else if (data.type === 'thinking_stream' && data.content) {
            /** 将thinking数据传递给流式ThinkingStream组件 */
            setThinkingData(data.content)

            /** 推送数据到Planning阶段的流式thinking组件 */
            streamingDataManager.pushData(STREAM_IDS.PLANNING_THINKING, data.content, true)

            /** 保持原有的stateStore更新以兼容其他组件 */
            stateStore.thinkingContent = data.content
            stateStore.isThinkingActive = true
          }
          else if (data.type === 'complete') {
            /** 立即更新按钮状态 */
            const planningCard = trendAg.messageStore.messages.find(
              msg => msg.meta?.cardId === 'planning-scheme-report',
            )

            if (planningCard && planningCard.card && planningCard.card.cardConfig?.button) {
              planningCard.card.cardConfig.button.text = 'Approve Strategy'
              planningCard.card.cardConfig.button.variant = 'gradient-border'
              planningCard.card.cardConfig.button.disabled = false

              /** 触发重新渲染 */
              trendAg.messageStore.messages = [...trendAg.messageStore.messages]
            }

            /** 当数据接收完成时，只更新 planning_report 内容，不自动显示 TrendSelection */
            if (collectedPlanningReport) {
              /** 确保 taskInstanceId 也被设置 */
              if (!stateStore.taskInstanceId && currentTaskInstanceId) {
                stateStore.taskInstanceId = currentTaskInstanceId
              }

              stateStore.planningReportContent = collectedPlanningReport
              /** 不在这里触发工作流，等待 onComplete 回调 */
            }
          }
        },
        (error: Error) => {
          const { reportStore } = trendAg
          const targetItem = reportStore.items.find(item => item.id === planningReportItemId)
          if (targetItem) {
            targetItem.content = `营销策划方案生成失败：${error.message}\n\n请检查网络连接后重试。`
          }
        },
        async () => {
          /** 确保在完成时也设置数据 */
          if (collectedPlanningReport && !stateStore.planningReportContent) {
            /** 确保 taskInstanceId 也被设置 */
            if (!stateStore.taskInstanceId && currentTaskInstanceId) {
              stateStore.taskInstanceId = currentTaskInstanceId
            }
            stateStore.planningReportContent = collectedPlanningReport
          }
        },
      )
    }
    catch (error) {

    }
  }, [stateStore, setThinkingData, setPlanningSchemeCompleted])

  /** 创建自定义的两个策略卡片 */
  const createCustomStrategyCards = useCallback(() => {
    /** 防重复创建检查 */
    if (cardsCreated) {
      return
    }
    /** 1. 第二个thinking组件预先显示 - 在开始任何API请求之前 */
    setShowAiReportThinking(true)

    const curStep = 'step0' as any
    /** 创建第一个报告项 - 初始标题为Loading，等待数据响应后更新 */
    industryReportItemRef.current = addReportItem({
      type: 'markdown',
      title: 'Loading...', // 初始标题为Loading
      content: 'Analyzing industry trends and market dynamics...',
      meta: {
        step: curStep,
        canTransformCode: true,
      },
    }, true, true, trendAg)

    /** 创建第二个报告项 - 初始标题为Loading，等待数据响应后更新 */
    competitiveReportItemRef.current = addReportItem({
      type: 'markdown',
      title: 'Loading...', // 初始标题为Loading
      content: 'Analyzing competitor strategies and positioning opportunities...',
      meta: {
        step: curStep,
        canTransformCode: true,
      },
    }, true, false, trendAg)

    /** 保存竞争对手分析报告项ID */
    setCompetitiveReportItemId(competitiveReportItemRef.current.id)

    /** 手动设置基础时间戳，确保卡片显示在正确位置 */
    const baseTimestamp = Date.now() - 10000 // 减去10秒，确保卡片显示在其他消息之前

    /** 手动调整时间戳，确保卡片按正确顺序显示在其他消息之前 */

    /** 使用修复后的同步流式API调用，确保ReportPreview正确显示 */
    if (!hasCalledReportSync) {
      setTimeout(() => {
        setHasCalledReportSync(true) // 标记已调用
        /** 重置工作流完成状态 */
        setDataReportCompleted(false)
        setInsightReportCompleted(false)
        setCompetitorReportCompleted(false)
        setPlanningSchemeCompleted(false)
        setHasApprovedStrategy(false)
        /** 不重置 hasTriggeredTrendWorkflows，防止重复触发 */
        // stateStore.hasTriggeredTrendWorkflows = false  // 注释掉，保持状态
        stateStore.showTrendSelection = false
        updateBothCardsReportSync(industryReportItemRef.current.id, competitiveReportItemRef.current.id)
      }, 200) // 短暂延迟确保卡片创建完成
    }

    /** 立即打开报告面板并显示第一个卡片的tab，以便实时看到渲染 */
    stateStoreRef.current.isReportOpen = true
    setTimeout(() => {
      eventBus.emit(DistributionEvent.SetActiveTab, industryReportItemRef.current.id)
    }, 100) // 短暂延迟确保报告项已创建

    /** 设置创建完成标志，防止重复创建 */
    setCardsCreated(true)
  }, [cardsCreated, updateBothCardsReportSync, hasCalledReportSync]) // 添加依赖

  /**
   * 注意：原 updateStrategyReport 函数已移除
   * Strategy相关的API调用现在通过其他方式处理
   */

  /** Planning Scheme流式API更新函数 - 创建专门的报告项 */
  const updatePlanningSchemeReport = useCallback(async () => {
    try {
      const { addReportItem } = await import('../stores/create')

      const planningSchemeReportItem = addReportItem({
        type: 'markdown',
        title: 'Planning Scheme Report',
        content: '正在生成营销策划方案...',
        meta: {
          step: 'step0',
          canTransformCode: true,
        },
      }, true, false, trendAg)

      /** 导入Planning Scheme专用的流式API函数 */
      const { callPlanningSchemeCozeStreamAPI } = await import('../stores/cozeStreamApi')

      /** 调用Planning Scheme流式接口 */
      await callPlanningSchemeCozeStreamAPI(
        (data) => {
          if (data.type === 'thinking_stream' && data.content) {
            /** 处理Planning Scheme专用的thinking数据 */
            setPlanningThinkingData(data.content)
            setShowPlanningThinking(true)

            /** 同时更新原有的thinking数据以保持兼容性 */
            setThinkingData(data.content)
            setIsThinkingDataReady(true)
            stateStore.thinkingContent = data.content
            stateStore.isThinkingActive = true
          }
          else if (data.type === 'report_title' && data.title) {
            const { reportStore } = trendAg
            const planningItem = reportStore.items.find(item => item.id === planningSchemeReportItem.id)
            if (planningItem) {
              planningItem.title = data.title
            }
          }
          else if (data.type === 'report_display' && data.content) {
            const { reportStore } = trendAg
            const planningItem = reportStore.items.find(item => item.id === planningSchemeReportItem.id)
            if (planningItem) {
              if (data.content.startsWith('# ') || data.content.startsWith('## ')) {
                planningItem.content = data.content
              }
              else {
                planningItem.content += data.content
              }
            }
          }
        },
        (error: Error) => {
          const { reportStore } = trendAg
          const planningItem = reportStore.items.find(item => item.id === planningSchemeReportItem.id)

          if (planningItem) {
            planningItem.content = `Planning Scheme报告生成失败：${error.message}\n\n请检查网络连接后重试。`
          }
        },
        () => {
          /** 流式完成后，自动打开报告面板并更新卡片点击事件 */
          setTimeout(() => {
            stateStore.isReportOpen = true
            eventBus.emit(DistributionEvent.SetActiveTab, planningSchemeReportItem.id)

            /** 更新Planning Scheme卡片的点击事件 */
            const planningSchemeCard = trendAg.messageStore.messages.find(
              msg => msg.meta?.isPlanningSchemeCard === true,
            )
            if (planningSchemeCard && planningSchemeCard.card) {
              planningSchemeCard.card.onClick = () => {
                stateStore.isReportOpen = true
                stateStore.showTrendSelection = false // 隐藏 TrendSelection
                eventBus.emit(DistributionEvent.SetActiveTab, planningSchemeReportItem.id)
              }
            }
          }, 500)
        },
      )
    }
    catch (error) {
    }
  }, [stateStore])

  /** 创建Planning Scheme后续卡片 - 在Approve Strategy后显示 */
  const createPlanningFollowUpCard = useCallback(() => {
    const curStep = 'step1' as any
    const currentLeftIcon = 'brandStrategist'

    const followUpCard = createCardMessage(
      {
        title: 'The Trend Report and Reference Posts',
        description: 'Reference posts is curated to match your brand positioning.',
        variant: 'success',
        onClick: () => {
          /** 关闭图片预览 */
          ChatEventBus.emit('hideImagePreview')

          /** 显示 TrendSelectionPage（里面包含 hotpots_analysis 和 distill_daren_list 的结果） */
          stateStore.showTrendSelection = true
          stateStore.isReportOpen = true
        },
        cardConfig: {
          leftIcon: {
            show: true,
            icon: currentLeftIcon,
            size: 'lg' as const,
          },
          rightIcon: {
            show: true,
            icon: 'card-right-icon3', // 使用自定义 SVG 图标
            size: 'md' as const,
          },
          content: {
            title: 'The Trend Report',
            description: 'Review the trend report based on your choice',
          },
          socialPost: {
            image: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop',
            description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit...',
            author: {
              name: 'Milla',
              avatar: 'https://sns-avatar-qc.xhscdn.com/avatar/1040g2jo31aje2ouc6q0049042ub7u510c0c06r0?imageView2/2/w/120/format/webp|imageMogr2/strip',
            },
          },
          layout: 'social' as const,
        },
      },
      {
        meta: {
          step: curStep,
          cardId: 'implementation-plan-card',
          isImplementationCard: true,
        },
      },
      trendAg,
    )

    /** 设置时间戳确保显示在正确位置 */
    const futureTimestamp = Date.now() + 120000 // 比Planning Scheme卡片晚60秒
    followUpCard.timestamp = futureTimestamp

    return { followUpCard }
  }, [stateStore])

  /** 创建Planning Scheme独立卡片 - 创建独立的DOM节点和卡片 */
  const createPlanningSchemeCard = useCallback(() => {
    const curStep = 'step0' as any
    const currentLeftIcon = 'creativeDirector' // 使用 Research Analyst 图标

    /** 先检查是否已存在报告项，避免重复创建 */
    let planningReportItem = trendAg.reportStore.items.find(
      item => item.title === 'Planning Scheme Report',
    )

    if (!planningReportItem) {
      /** 首次创建Planning Scheme报告项 */
      planningReportItem = addReportItem({
        type: 'markdown',
        title: 'Planning Scheme Report',
        content: '正在生成营销策划方案...',
        meta: {
          step: curStep,
          canTransformCode: true,
        },
      }, true, false, trendAg)
    }
    /** 创建Planning Scheme专用的卡片消息，标记为独立的Strategy卡片 */
    const planningSchemeCard = createCardMessage(
      {
        title: 'Planning Scheme Report',
        description: '正在生成营销策划方案，包含完整的实施路线图和关键指标...',
        variant: 'success',
        onClick: () => {
          /** 点击卡片（非按钮）始终显示 planning_scheme 报告 */
          /** 使用保存的报告项 ID 直接打开 */
          if (planningReportItem && planningReportItem.id) {
            /** 关闭图片预览卡片 */
            ChatEventBus.emit('hideImagePreview')

            stateStore.isReportOpen = true
            stateStore.showTrendSelection = false // 隐藏 TrendSelection
            eventBus.emit(DistributionEvent.SetActiveTab, planningReportItem.id)
          }
          else {
            /** 如果没有报告项ID，尝试通过标题查找 */
            const planningReport = trendAg.reportStore.items.find(item => item.title === 'Planning Scheme Report')
            if (planningReport) {
              ChatEventBus.emit('hideImagePreview')
              stateStore.isReportOpen = true
              stateStore.showTrendSelection = false // 隐藏 TrendSelection
              eventBus.emit(DistributionEvent.SetActiveTab, planningReport.id)
            }
          }
        },
        cardConfig: {
          leftIcon: {
            show: true,
            icon: currentLeftIcon,
            size: 'lg' as const,
          },
          rightIcon: {
            show: true,
            icon: 'card-right-icon1',
            size: 'md' as const,
          },
          content: {
            title: 'Planning Scheme Report',
            description: '营销策划方案生成中，包含实施路线图、关键指标和预算分配等详细内容。',
          },
          layout: 'simple' as const,
          button: {
            text: isStrategyApproved
              ? 'Approved'
              : 'Approve Strategy', // 直接显示 Approve Strategy
            variant: isStrategyApproved
              ? 'success' as const
              : 'gradient-border' as const, // 直接使用 gradient-border 样式
            position: 'bottom-left' as const,
            disabled: isStrategyApproved || hasApprovedStrategy, // 移除 planningSchemeCompleted 检查
            onClick: async () => {
              if (isStrategyApproved || hasApprovedStrategy)
                return

              /** 如果 planning_scheme 还没完成，先检查并设置必要的数据 */
              if (!planningSchemeCompleted) {
                if (!stateStore.taskInstanceId) {
                  /** 尝试从 localStorage 获取 */
                  const taskId = localStorage.getItem('confirmed_taskInstanceId') || localStorage.getItem('taskInstanceId')
                  if (taskId) {
                    stateStore.taskInstanceId = taskId
                  }
                }
                if (!stateStore.planningReportContent) {
                  stateStore.planningReportContent = '' // 设置空字符串，允许继续
                }
              }

              /** 防止重复点击 */
              setHasApprovedStrategy(true)
              setIsStrategyApproved(true)
              /** 保持报告预览打开，这样用户可以切换 */
              stateStoreRef.current.isReportOpen = true
              taskStoreRef.current.currentStep = 'step0' as any
              setSecondaryDisplayText(`Approve Strategy`)

              try {
                /** 检查工作流是否已经触发 */
                if (!hasTriggeredHotpotsRef.current || !hasTriggeredDistillRef.current) {
                  /** 触发工作流 */
                  await triggerWorkflowsAfterPlanningScheme()
                }
                else {
                  /** 如果工作流已经触发，直接显示 TrendSelectionPage */
                  /** 设置显示小红书列表 */
                  if (stateStore.trendSelectionState) {
                    stateStore.trendSelectionState.showRednoteList = true
                  }

                  /** 设置 showTrendSelection 为 true，显示 TrendSelectionPage */
                  stateStore.showTrendSelection = true
                }
              }
              catch (error) {

              }

              /** 创建Planning Scheme后续卡片，但不立即显示 The Trend Report 卡片 */
              setTimeout(() => {
                createPlanningFollowUpCard()
                setShowImplementationCard(true) // 显示 Start content creation 按钮
                /** 注意：setShowTrendReportAfterClick(true) 移动到第四个thinking完成回调中 */
              }, 1000)
            },
          },
        },
      },
      {
        meta: {
          step: curStep,
          cardId: 'planning-scheme-report',
          isStrategyCard: true, // 标记为Strategy卡片，让StrategyCardRenderer能找到它
          isPlanningSchemeCard: true, // 额外标记为Planning Scheme卡片
        },
      },
      trendAg,
    )

    /** 设置特殊的时间戳，确保卡片显示在ThinkingStream下方 */
    const futureTimestamp = Date.now() + 60000
    planningSchemeCard.timestamp = futureTimestamp

    return { planningSchemeCard, planningReportItemId: planningReportItem.id }
  }, [stateStore, isStrategyApproved, createPlanningFollowUpCard, planningSchemeCompleted, hasApprovedStrategy])

  /** 处理开始分析按钮点击 */
  const handleStartAnalysis = async () => {
    /** 临时移除thinking数据依赖，提升用户体验 */
    if (isButtonLoading || isButtonDisabled)
      return

    /**
     * 只在用户首次点击或者明确需要重新开始时重置工作流标记
     * 如果 hotpots 和 distill 已经触发过，不要重置它们
     */
    const isFirstRun = !hasTriggeredPlanningScheme.current
      && !hasTriggeredHotpotsRef.current
      && !hasTriggeredDistillRef.current

    if (isFirstRun) {
      hasTriggeredPlanningScheme.current = false
      hasTriggeredHotpotsRef.current = false
      hasTriggeredDistillRef.current = false
      stateStore.hasTriggeredHotpotsAnalysis = false
      stateStore.hasTriggeredDistillDarenList = false
    }
    else {
      hasTriggeredPlanningScheme.current = false
    }

    /** 标记分析已开始，显示分析结果区域 */
    setIsAnalysisStarted(true)
    setIsButtonLoading(true)

    /** 激活ThinkingStream，优先使用从API接收到的thinking数据 */
    stateStore.isThinkingActive = true

    /** 设置ThinkingStream内容 */
    if (thinkingData && thinkingData.trim() && !thinkingData.includes('正在分析市场趋势')) {
      stateStore.thinkingContent = thinkingData
    }
    else if (stateStore.thinkingContent && stateStore.thinkingContent.trim() && !stateStore.thinkingContent.includes('正在思考') && !stateStore.thinkingContent.includes('正在分析市场趋势')) {
      /** 保持现有内容 */
    }
    else {
      stateStore.thinkingContent = '正在思考...'
    }

    /** 创建Planning Scheme卡片并获取报告项ID */
    let planningReportItemId = ''
    setTimeout(() => {
      const planningCard = createPlanningSchemeCard()
      if (planningCard.planningReportItemId) {
        planningReportItemId = planningCard.planningReportItemId
      }
    }, 300)

    /** 1. 第三个thinking组件显示时机：在开始执行planning_scheme工作流时立即显示 */
    setDisplayText(`Continue to Strategy`)

    /** 注意：setShowStrategyCard(true) 移动到thinking完成回调中 */

    /** 开始分析后持续监听消息变化并滚动 */
    const scrollInterval = setInterval(() => {
      const container = document.querySelector('.ChatPageContainer .overflow-auto')
      if (container) {
        const isNearBottom = container.scrollHeight - container.scrollTop - container.clientHeight < 100
        /** 只有在接近底部时才自动滚动，避免干扰用户手动滚动 */
        if (isNearBottom) {
          container.scrollTo({
            top: container.scrollHeight,
            behavior: 'smooth',
          })
        }
      }
    }, 500)

    /** 使用独立的planning_scheme请求方法 */
    try {
      /** 等待卡片创建完成 */
      await new Promise(resolve => setTimeout(resolve, 500))

      /** 如果没有获取到报告项ID，尝试从reportStore中查找 */
      if (!planningReportItemId) {
        const { reportStore } = trendAg
        const planningItem = reportStore.items.find(item =>
          item.title?.includes('Planning Scheme') || item.title?.includes('营销策划方案'),
        )
        if (planningItem) {
          planningReportItemId = planningItem.id
        }
      }

      if (planningReportItemId) {
        await executePlanningSchemeOnly(planningReportItemId)
      }

      await new Promise(resolve => setTimeout(resolve, 2000))

      setTimeout(() => clearInterval(scrollInterval), 2000)
      setIsButtonDisabled(true)
    }
    catch (error) {
      setIsAnalysisStarted(false)
      clearInterval(scrollInterval)
    }
    finally {
      setIsButtonLoading(false)
    }
  }

  useBindWinEvent('resize', () => {
    stateStore.isReportOpen = false
    stateStore.isAgentCollapsed = true
  })

  /** 当页面加载且有表单数据时，创建一个开始按钮让用户手动启动 */
  useEffect(() => {
    if (!formData) {
      return
    }

    /** 创建一个初始任务，让用户点击开始 */
    if (taskStore.agentTasks.length === 0) {
      taskStore.agentTasks.push({
        id: 'start',
        title: 'Marketing Team Ready',
        description: '点击下方按钮开始执行营销方案生成',
        status: 'waiting',
        actions: [{ label: 'Continue to Strategy', type: 'primary' }],
        step: 'step0' as any,
      })
    }
  }, [formData, taskStore.agentTasks])

  /** 监听 showWorkflow 变化，在表单提交后立即创建策略卡片 */
  useEffect(() => {
    if (showWorkflow && !cardsCreated && !isAnalysisStarted) {
      /** 只有在表单提交后（非Continue to Strategy按钮点击后）才创建卡片 */
      setTimeout(() => {
        /** 调用自定义的策略卡片创建方法 */
        createCustomStrategyCards()
      }, 100) // 短暂延迟确保状态更新完成
    }
    else if (!showWorkflow && cardsCreated) {
      /** 当 showWorkflow 变为 false 时，重置创建标志，允许下次重新创建 */
      setCardsCreated(false)
    }
  }, [showWorkflow, cardsCreated, isAnalysisStarted, createCustomStrategyCards])

  /** 监听工作流完成状态，按顺序显示组件 */
  useEffect(() => {
    /** 5. 所有工作流完成后：将"Continue to Strategy"按钮状态改为可点击 */
    if (dataReportCompleted && insightReportCompleted && competitorReportCompleted) {
      setIsThinkingDataReady(true)
      setIsButtonDisabled(false)

      /** 完成聚合thinking流 */
      streamingDataManager.completeThinking1Stream()
    }
  }, [dataReportCompleted, insightReportCompleted, competitorReportCompleted])

  /** 处理第三个thinking组件完成后的卡片显示 */
  const handlePlanningThinkingComplete = useCallback(() => {
    /** 2. 卡片显示延迟：策略卡片必须等待thinking的流式打字机效果完全完成后才能显示 */
    setShowStrategyCard(true)
  }, [])

  /** 处理第四个thinking组件完成后的卡片显示 */
  const handleOperationsThinkingComplete = useCallback(() => {
    /** 第四个thinking完成后显示The Trend Report卡片 */
    setShowTrendReportAfterClick(true)
  }, [])

  /** 处理第五个thinking组件完成后的卡片和按钮显示 */
  const handleOriginalWorkThinkingComplete = useCallback(() => {
    /** 第五个thinking完成后显示RedNote Post Launch Plan卡片和Continue to content posting按钮 */
    setShowPostLaunchCard(true)
    setShowContinueToPosting(true)
  }, [])

  /** 处理Continue to content posting按钮点击 - 将原Apply Post功能迁移到这里 */
  const handleContinueToContentPosting = useCallback(async () => {
    setIsPostingDisabled(true)

    try {
      /** 获取必要的参数 */
      let taskInstanceId = ''
      let executionId = ''

      /** 尝试从缓存中获取 taskInstanceId */
      taskInstanceId = localStorage.getItem('confirmed_taskInstanceId')
        || localStorage.getItem('taskInstanceId') || ''

      /** 导入工作流参数获取函数 */
      const { getWorkflowExecutionParams } = await import('../stores/cozeStreamApi')

      /** 优先从当前请求的工作流meta中获取executionId */
      const workflowParams = getWorkflowExecutionParams('original_work')
        || getWorkflowExecutionParams('duibiao_disassembly_text')

      if (workflowParams) {
        taskInstanceId = workflowParams.taskInstanceId || taskInstanceId
        executionId = workflowParams.executionId
      }

      if (!taskInstanceId) {
        console.error('缺少 taskInstanceId，无法提交')
        setIsPostingDisabled(false)
        return
      }

      if (!executionId) {
        console.error('缺少 executionId，无法提交，请确保已完成工作流请求并获取到meta信息')
        setIsPostingDisabled(false)
        return
      }

      /** 从 localStorage 获取图片预览数据 */
      const imagePreviewData = localStorage.getItem('imagePreviewData')
      let title = ''; let content = ''; let tags: string[] = []; let images: string[] = []

      if (imagePreviewData) {
        try {
          const parsedData = JSON.parse(imagePreviewData)
          title = parsedData.title || ''
          content = parsedData.content || ''
          tags = parsedData.tag
            ? parsedData.tag.split(' ').filter((t: string) => t.trim())
            : []
          images = parsedData.images || (parsedData.image
            ? [parsedData.image]
            : [])
        }
        catch (e) {
          console.warn('解析图片预览数据失败:', e)
        }
      }

      /** 准备提交的数据 */
      const finalContent = {
        content, // Body copy内容
        title, // Title内容
        tags: tags.filter(tag => tag.trim()), // Tags数组，过滤空标签
        image: images, // 图片数组
      }

      const submitData = {
        taskInstanceId,
        executionId,
        finalContent,
        confirmReason: '', // 写死空字符串
      }

      console.log('准备提交数据:', submitData)

      /** 调用确认接口 */
      await request.post('/app/market/confirm-final-content', submitData)

      /** 成功后，设置永久禁用状态 */
      localStorage.setItem('hasAppliedPost', 'true')

      /** 更新缓存数据 */
      const updatedContent = {
        title: finalContent.title,
        content: finalContent.content,
        tag: finalContent.tags.join(' '),
        images: finalContent.image,
        image: finalContent.image.length > 0
          ? finalContent.image[0]
          : '',
        pic_url: finalContent.image.length > 0
          ? finalContent.image[0]
          : '',
      }
      localStorage.setItem('imagePreviewData', JSON.stringify(updatedContent))

      /** 成功后切换到Post Content Review模式 */
      const phoneFrameData = {
        title: title || '',
        content: content || '',
        tag: tags.join(' ') || '',
        showPhoneFrame: true,
        showDarenList: false,
        image: images.length > 0
          ? images[0]
          : '',
        images,
        pic_url: images.length > 0
          ? images[0]
          : '',
      }
      ChatEventBus.emit('updateImagePreview', phoneFrameData)
      ChatEventBus.emit('showImagePreview', { isUserAction: true })

      /** 显示第二个 RedNote Post Launch Plan 卡片 */
      setShowSecondPostLaunchCard(true)

      /** 成功后重置loading状态 */
      setIsPostingDisabled(false)
    }
    catch (error) {
      console.error('Continue to content posting 失败:', error)
      setIsPostingDisabled(false) // 确保出错时重置状态
    }
  }, [])

  useInsertionEffect(() => {
    const overflow = document.body.style.overflow
    document.body.style.overflow = 'hidden'
    return () => {
      document.body.style.overflow = overflow
    }
  }, [])

  return <div
    className={ cn(
      'ChatPageContainer flex flex-row h-full overflow-hidden p-4 gap-4',
      className,
    ) }
    style={ style }
  >
    {/* 主内容区 */}
    <div className="flex flex-1 flex-col overflow-hidden rounded-2xl bg-white">
      {/* TopBar - 只在左侧聊天区域上方显示 */}
      {showTopBar && (
        <div className="flex-shrink-0 border-b border-gray-100">
          <TopBar
            agents={ topBarAgents }
            showAgents={ !!localStorage.getItem('dataReportParams') } // 只在data_report工作流开始后显示AI代理图标
            onAgentClick={ onTopBarAgentClick }
            dropdownExpanded={ topBarDropdownExpanded }
            onDropdownToggle={ onTopBarDropdownToggle }
            onNewProject={ onTopBarNewProject }
            containerClassName="relative"
          />
        </div>
      )}

      {/* 聊天内容区域 */}
      <div className="flex-1 overflow-hidden">
        {chatV2FlowMode
          ? (
            // ChatV2 流程界面 - 使用可滚动容器
              <div className="relative h-full flex flex-col">
                <div className="flex-1 overflow-auto pb-28">
                  {' '}
                  {/* 为底部输入框预留空间 */}
                  <div className="mx-auto max-w-4xl">
                    <ChatWorkflow
                      showContentDisplay
                      content={ userDescription }
                      uploadedImage={ uploadedImage }
                      showThinking
                      isThinking={ isThinking }
                      thinkingExpanded={ thinkingExpanded }
                      onThinkingToggle={ setThinkingExpanded }
                      onThinkingComplete={ handleThinkingComplete }
                      thinkingMessages={ thinkingMessages.length > 0
                        ? thinkingMessages
                        : undefined }
                      useOptimizedThinking={ chatV2FlowMode === 'text_link_disassemble' }
                      showWorkflow={ showWorkflow }
                      workflowSteps={ workflowSteps }
                      isLoadingWorkflowSteps={ isLoadingSteps }
                      showForm={ showForm && !showWorkflow }
                      formData={ formDataLocal }
                      formErrors={ formErrors }
                      formUploadedImage={ formDataLocal.pic }
                      isSubmitting={ isSubmitting || isUploadingImage } // 图片上传时也禁用按钮
                      isUploadingImage={ isUploadingImage } // 传递图片上传状态
                      onFormChange={ handleFormChange }
                      isHotpotsAnalysisComplete={ isHotpotsAnalysisComplete }
                      isOriginalWorkLoading={ stateStore.originalWorkLoading }
                      onPreviewImages={ () => {
                        /** 仅触发显示预览事件 */
                        ChatEventBus.emit('showImagePreview', { isUserAction: true })
                      } }
                      onStartContentCreation={ async () => {
                        /** 移除自动触发 original_work 的代码 */
                        // original_work 应该只在用户点击 "Start content creation" 按钮时触发
                      } }
                      onFormSubmit={ handleFormSubmit }
                      onImageUpload={ async (file) => {
                        try {
                          setIsUploadingImage(true) // 开始上传

                          /** 先创建本地预览 URL */
                          const localPreviewUrl = URL.createObjectURL(file)
                          handleFormChange('pic', localPreviewUrl)

                          /** 上传到 OSS 获取 URL */
                          const uploadResult = await FileAPI.upFileToUrl([file])
                          if (uploadResult?.downloadLoadFileDetails?.[0]?.url) {
                            const ossUrl = uploadResult.downloadLoadFileDetails[0].url

                            /** 清理本地预览 URL */
                            URL.revokeObjectURL(localPreviewUrl)

                            /** 更新为 OSS URL */
                            handleFormChange('pic', ossUrl)
                            /** 清除图片相关错误 */
                            setFormErrors((prev) => {
                              const newErrors = { ...prev }
                              delete newErrors.pic
                              return newErrors
                            })
                          }
                          else {
                            handleFormChange('pic', '')
                            /** 添加错误提示 */
                            setFormErrors(prev => ({ ...prev, pic: '图片上传失败，请重试' }))
                          }
                        }
                        catch (error) {
                          handleFormChange('pic', '')
                          /** 添加错误提示 */
                          setFormErrors(prev => ({ ...prev, pic: '图片上传失败，请重试' }))
                        }
                        finally {
                          setIsUploadingImage(false) // 结束上传
                        }
                      } }
                      onImageRemove={ () => handleFormChange('pic', '') }
                      workflowTitle="Your AI Agent Team‘s Action plan"
                      workflowDescription="Perfect! Your brand profile is ready. Now I'm bringing together my AI agent team to create amazing trend analysis for you. Here's how we'll work together:"
                      onStartAnalysis={ handleStartAnalysis }
                      showAskInput={ false } // 禁用内置的 Ask 输入框
                      askInputPlaceholder=""
                      askInputValue=""
                      askInputDisabled
                      onAskInputSubmit={ () => {} }
                      onStepRendered={ () => setShowAiReportThinking(true) }
                    />

                    {/* 分析结果区域 - 表单提交后立即显示，包含策略卡片和Continue to Strategy按钮 */}
                    {showWorkflow && (
                      <motion.div
                        className="border-gray-200"
                        initial={ { opacity: 0, y: 20 } }
                        animate={ { opacity: 1, y: 0 } }
                        transition={ { duration: 0.5 } }
                      >
                        <CombinedThinkingStream show={ showAiReportThinking } />
                        {/* 策略卡片显示区域 - 在motion组件最顶部 */}
                        <div className="mb-6">
                          <ChatHistory
                            taskStore={ taskStore }
                            messageStore={ messageStore }
                            ref={ chatHistoryRef }
                            className="min-h-0 w-full"
                            onDeleteMessage={ removeMessage }
                            stateStore={ stateStore }
                          />
                        </div>

                        {/* Continue to Strategy 按钮 - 在卡片下方 */}
                        <div className="mb-6 flex justify-start">
                          <button
                            className={ cn(
                              'px-8 py-3 rounded-full text-sm font-medium transition-colors',
                              (!insightReportCompleted || !competitorReportCompleted)
                                ? 'bg-gray-400 text-gray-200 cursor-not-allowed'
                                : 'bg-black text-white hover:bg-gray-800',
                              'disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-black',
                              isButtonLoading && 'cursor-wait',
                            ) }
                            disabled={ isButtonLoading || isButtonDisabled || !insightReportCompleted || !competitorReportCompleted }
                            onClick={ handleStartAnalysis }
                          >
                            <div className="flex items-center gap-2">
                              {isButtonLoading && (
                                <div className="h-4 w-4 animate-spin border-2 border-white border-t-transparent rounded-full" />
                              )}
                              {(!insightReportCompleted || !competitorReportCompleted)
                                ? 'Waiting for reports...'
                                : 'Continue to Strategy'}
                            </div>
                          </button>
                        </div>

                        {/* 文本显示组件 - 在按钮下方，ThinkingStream上方 */}
                        {displayText && (
                          <TextDisplayWithPagination
                            content={ displayText }
                            charsPerPage={ 600 }
                            onCopySuccess={ () => {
                              /** 这里可以添加toast提示 */
                            } }
                            onCopyError={ (error) => {
                              /** 这里可以添加错误提示 */
                            } }
                          />
                        )}
                        {/* Planning阶段 - 流式Thinking显示组件 */}
                        <OptimizedPlanningThinkingStream
                          displayText={ displayText }
                          onComplete={ handlePlanningThinkingComplete }
                        />

                        {/* Strategy Implementation Plan 卡片容器 - 在ThinkingStream下方 */}
                        {showStrategyCard && (
                          <StrategyCardRenderer
                            messageStore={ trendAg.messageStore }
                            stateStore={ stateStore }
                          />
                        )}

                        {/* 第二个文本显示组件 - 在Strategy卡片下方，显示实施路线图 */}
                        {secondaryDisplayText && isStrategyApproved && (
                          <TextDisplayWithPagination
                            content={ secondaryDisplayText }
                            charsPerPage={ 500 }
                            className="mt-4"
                            onCopySuccess={ () => {
                              /** 这里可以添加toast提示 */
                            } }
                            onCopyError={ (error) => {

                              /** 这里可以添加错误提示 */
                            } }
                          />
                        )}
                        {/* distill_daren_list - 流式Thinking显示组件 */}
                        <OperationsThinkingStream1
                          secondaryDisplayText={ secondaryDisplayText }
                          isStrategyApproved={ isStrategyApproved }
                          onComplete={ handleOperationsThinkingComplete }
                        />
                        {/* The Trend Report 卡片 - 在 Approve Strategy 后显示 */}
                        {showTrendReportAfterClick && (
                          <div className="mt-4">
                            {/* The Trend Report 卡片 - 使用 SelectableGradientCard 组件 */}
                            <div className="w-106.5">
                              <SelectableGradientCard
                                className="cursor-pointer"
                                borderWidth={ 1.5 }
                                borderGradient="linear-gradient(90deg, #DD9DFF 0%, #36D3FF 100%)"
                                hoverBackground="linear-gradient(90deg, rgba(221, 157, 255, 0.10) 0%, rgba(54, 211, 255, 0.10) 100%)"
                                selectedBackground="linear-gradient(90deg, rgba(221, 157, 255, 0.10) 0%, rgba(54, 211, 255, 0.10) 100%)"
                                cardConfig={ {
                                  leftIcon: {
                                    show: true,
                                    icon: currentLeftIcon,
                                    size: 'lg' as const,
                                  },
                                  rightIcon: {
                                    show: true,
                                    icon: (
                                      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M9 18L15 12L9 6" stroke="#999" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                                      </svg>
                                    ),
                                    size: 'md' as const,
                                  },
                                  content: {
                                    title: 'The Trend Report',
                                    description: 'Review the trend report based on your choice',
                                  },
                                  socialPost: {
                                    image: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop',
                                    description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit...',
                                    author: {
                                      name: 'Milla',
                                      avatar: 'https://sns-avatar-qc.xhscdn.com/avatar/1040g2jo31aje2ouc6q0049042ub7u510c0c06r0?imageView2/2/w/120/format/webp|imageMogr2/strip',
                                    },
                                    stats: {
                                      likes: 3.5,
                                    },
                                  },
                                  layout: 'social' as const,
                                } }
                                onClick={ () => {
                                  /** 显示TrendSelectionPage */
                                  stateStore.showTrendSelection = true
                                  stateStore.isReportOpen = true
                                  /** 关闭图片预览 */
                                  ChatEventBus.emit('hideImagePreview')
                                } }
                              />
                            </div>

                            {/* Start content creation 按钮 - 在 The Trend Report 卡片下方 */}
                            <div className="mb-6 mt-5 flex justify-start">
                              <button
                                className={ cn(
                                  'px-8 py-3 rounded-full text-sm font-medium transition-colors',
                                  (isContentCreationDisabled || isContentCreationLoading)
                                    ? 'bg-gray-400 text-gray-200 cursor-not-allowed'
                                    : 'bg-black text-white hover:bg-gray-800',
                                  'disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-black',
                                  isContentCreationLoading && 'cursor-wait',
                                ) }
                                disabled={ isContentCreationDisabled || isContentCreationLoading }
                                onClick={ async () => {
                                  /** 清除图片缓存和Apply Post标记，开始新的内容创建流程 */
                                  localStorage.setItem('imagePreviewData', JSON.stringify({}))
                                  localStorage.removeItem('hasAppliedPost')

                                  /** 通知 ImagePreviewCard 组件重置状态 */
                                  try {
                                    ChatEventBus.emit('resetImagePreview')
                                  }
                                  catch (error) {

                                  }

                                  /** 设置按钮为禁用和加载状态 */
                                  setIsContentCreationDisabled(true)
                                  setIsContentCreationLoading(true)

                                  /** 1. 立即显示第五个thinking组件 */
                                  setShowOriginalWorkThinking(true)

                                  /** 注意：setShowContinueToPosting(true) 和 setShowPostLaunchCard(true) 移动到thinking完成回调中 */

                                  /** 防重复调用检查 - 检查多个标记 */
                                  if (stateStore?.isOriginalWorkRunning || stateStore?.originalWorkRequesting || stateStore?.hasTriggeredOriginalWork) {
                                    /** 直接显示已有的 ImagePreviewCard */
                                    ChatEventBus.emit('showImagePreview', { isUserAction: true })
                                    setIsContentCreationLoading(false) // 恢复加载状态但保持禁用
                                    return
                                  }

                                  /** 获取必要的参数 */
                                  const currentTaskInstanceId = stateStore?.taskInstanceId || localStorage.getItem('taskInstanceId')
                                  const selectedTopicId = stateStore?.trendSelectionState?.selectedTopicId
                                  const selectedRednoteId = stateStore?.selectedRednoteId

                                  if (!currentTaskInstanceId) {
                                    setIsContentCreationDisabled(false)
                                    setIsContentCreationLoading(false)
                                    return
                                  }

                                  /**
                                   * 不需要创建卡片，OriginalWorkCardRenderer 会负责渲染
                                   * 检查是否已经在运行，避免重复调用
                                   */
                                  let originalWorkCard = trendAg.messageStore.messages.find(
                                    (msg: any) => msg.meta?.cardId === 'original-work-report',
                                  )

                                  if (originalWorkCard && originalWorkCard.originalWorkData) {
                                    /** 如果已有数据，直接显示 */
                                    stateStore.originalWorkData = { ...originalWorkCard.originalWorkData }
                                    ChatEventBus.emit('showImagePreview', { isUserAction: true })
                                    return
                                  }

                                  /** 触发显示 ImagePreviewCard */
                                  ChatEventBus.emit('showImagePreview', { isUserAction: true })

                                  /** 根据选择类型调用不同的工作流 */
                                  try {
                                    const selectionType = stateStore.trendSelectionState?.selectionType
                                    const selectedTopicId = stateStore.trendSelectionState?.selectedTopicId || ''
                                    const selectedRednoteId = stateStore.trendSelectionState?.selectedRednoteId || ''

                                    if (selectionType === 'hotpots' && selectedTopicId) {
                                      /** 选中了hotpots_analysis的话题，调用duibiao_disassembly_text */
                                      const { callDuibiaoDisassemblyTextAPI } = await import('../stores/callDuibiaoDisassemblyTextAPI')

                                      /** 设置运行状态标志 */
                                      stateStore.isOriginalWorkRunning = true
                                      stateStore.duibiaoDisassemblyLoading = true
                                      stateStore.duibiaoDisassemblyProgress = {}

                                      /** 获取duibiao_disassembly_text所需参数 */
                                      const productDescription = stateStore.formData?.product || ''
                                      const brandName = stateStore.formData?.brand || ''
                                      const productName = stateStore.formData?.product_name || ''
                                      const pic = stateStore.formData?.pic || stateStore.userSubmitPic || ''

                                      /** 从 distill_daren_list 工作流的 biji_list 中获取数据 */
                                      let bijiContent = ''
                                      let bijiTitle = ''
                                      let bijiUrl = ''

                                      /** 从缓存的工作流输出中获取 biji_list 数据 */
                                      try {
                                        const { getWorkflowOutput } = await import('../stores/cozeStreamApi')
                                        const distillData = getWorkflowOutput('distill_daren_list')

                                        if (distillData && distillData.biji_list) {
                                          let bijiList = distillData.biji_list
                                          if (typeof bijiList === 'string') {
                                            try {
                                              bijiList = JSON.parse(bijiList)
                                            }
                                            catch (e) {
                                            }
                                          }

                                          if (Array.isArray(bijiList) && bijiList.length > 0) {
                                            const bijiData = bijiList[0]
                                            /** 优先从 KOL 获取 */
                                            if (bijiData.kol && bijiData.kol.length > 0) {
                                              const firstKol = bijiData.kol[0]
                                              if (firstKol.info) {
                                                bijiTitle = firstKol.info.title || ''
                                                bijiContent = firstKol.info.desc || ''
                                              }
                                              bijiUrl = firstKol.noteLink || ''
                                            }
                                            /** 如果没有 KOL，从 KOC 获取 */
                                            if (!bijiTitle && bijiData.koc && bijiData.koc.length > 0) {
                                              const firstKoc = bijiData.koc[0]
                                              if (firstKoc.info) {
                                                bijiTitle = firstKoc.info.title || ''
                                                bijiContent = firstKoc.info.desc || ''
                                              }
                                              bijiUrl = firstKoc.noteLink || ''
                                            }
                                            /** 如果还没有，从 suren 获取 */
                                            if (!bijiTitle && bijiData.suren && bijiData.suren.length > 0) {
                                              const firstSuren = bijiData.suren[0]
                                              if (firstSuren.info) {
                                                bijiTitle = firstSuren.info.title || ''
                                                bijiContent = firstSuren.info.desc || ''
                                              }
                                              bijiUrl = firstSuren.noteLink || ''
                                            }
                                          }
                                        }
                                      }
                                      catch (error) {

                                      }

                                      /** 如果没有获取到，使用默认值 */
                                      if (!bijiUrl) {
                                        bijiUrl = 'https://www.xiaohongshu.com/explore/6891bf9b000000002501e60c'
                                      }

                                      /** 获取daren_list - 从缓存的工作流输出中获取 */
                                      let darenListStr = ''
                                      try {
                                        const { getWorkflowOutput } = await import('../stores/cozeStreamApi')
                                        const distillData = getWorkflowOutput('distill_daren_list')
                                        if (distillData && distillData.daren_list) {
                                          darenListStr = typeof distillData.daren_list === 'string'
                                            ? distillData.daren_list
                                            : JSON.stringify(distillData.daren_list)
                                        }
                                      }
                                      catch (error) {

                                      }

                                      /** 如果没有从缓存获取到，从 store 获取 */
                                      if (!darenListStr && stateStore.distillDarenListData) {
                                        const darenData = {
                                          KOL: stateStore.distillDarenListData.KOL || [],
                                          KOC: stateStore.distillDarenListData.KOC || [],
                                          Regulars: stateStore.distillDarenListData.Regulars || [],
                                        }
                                        darenListStr = JSON.stringify(darenData)
                                      }

                                      /** 获取brand_report */
                                      const brandReport = stateStore.formData?.role || ''

                                      const parameters = {
                                        product_description: productDescription,
                                        biji_content: bijiContent,
                                        biji_url: bijiUrl,
                                        biji_title: bijiTitle,
                                        daren_list: darenListStr,
                                        brand_name: brandName,
                                        product_name: productName,
                                        brand_report: brandReport,
                                        pic,
                                        platform: 'rednote',
                                      }

                                      callDuibiaoDisassemblyTextAPI(
                                        (data) => {
                                          /** 处理thinking数据 - 推送到第五个thinking组件 */
                                          if (data.nodeTitle === 'thinking' && data.content) {
                                            streamingDataManager.pushData(STREAM_IDS.ORIGINAL_WORK_THINKING, data.content, true)
                                          }

                                          /** 更新预览数据到 stateStore */
                                          if (data.nodeTitle && data.content) {
                                            stateStore.duibiaoDisassemblyProgress[data.nodeTitle] = true
                                            if (!stateStore.duibiaoDisassemblyData) {
                                              stateStore.duibiaoDisassemblyData = {}
                                            }

                                            /** 特殊处理 content 节点 - 累积拼接 */
                                            if (data.nodeTitle === 'content') {
                                              const currentContent = stateStore.duibiaoDisassemblyData['笔记正文'] || ''
                                              stateStore.duibiaoDisassemblyData['笔记正文'] = currentContent + data.content
                                              stateStore.duibiaoDisassemblyData.content = currentContent + data.content
                                            }
                                            else {
                                              stateStore.duibiaoDisassemblyData[data.nodeTitle] = data.content
                                            }

                                            /** 同时更新到originalWorkData以兼容现有显示逻辑 */
                                            if (!stateStore.originalWorkData) {
                                              stateStore.originalWorkData = {}
                                            }

                                            if (data.nodeTitle === 'content') {
                                              const currentContent = stateStore.originalWorkData['笔记正文'] || ''
                                              stateStore.originalWorkData['笔记正文'] = currentContent + data.content
                                              stateStore.originalWorkData.content = currentContent + data.content
                                            }
                                            else {
                                              stateStore.originalWorkData[data.nodeTitle] = data.content
                                            }
                                          }
                                        },
                                        (error) => {
                                          stateStore.duibiaoDisassemblyLoading = false
                                          stateStore.isOriginalWorkRunning = false
                                        },
                                        () => {
                                          stateStore.duibiaoDisassemblyLoading = false
                                          stateStore.originalWorkLoading = false
                                          stateStore.isOriginalWorkRunning = false

                                          /** 完成第五个thinking流 */
                                          streamingDataManager.completeStream(STREAM_IDS.ORIGINAL_WORK_THINKING)
                                        },
                                        currentTaskInstanceId as string,
                                        parameters,
                                      )
                                    }
                                    else {
                                      /** 多重防重检查：如果正在请求中或已触发过，直接返回 */
                                      if (stateStore.originalWorkRequesting || stateStore.hasTriggeredOriginalWork) {
                                        return
                                      }

                                      /** 选中了小红书或者没有选择，调用original_work */
                                      const { callOriginalWorkAPI } = await import('../stores/cozeStreamApi')

                                      /** 设置运行状态标志 */
                                      stateStore.isOriginalWorkRunning = true
                                      stateStore.originalWorkLoading = true
                                      stateStore.originalWorkProgress = {}

                                      /** 获取选中的话题和小红书笔记 */
                                      const planningReport = stateStore.planningReportContent || ''
                                      const userSubmitPic = stateStore.userSubmitPic || ''

                                      callOriginalWorkAPI(
                                        (data) => {
                                          /** 处理thinking数据 - 推送到第五个thinking组件 */
                                          if (data.nodeTitle === 'thinking' && data.content) {
                                            streamingDataManager.pushData(STREAM_IDS.ORIGINAL_WORK_THINKING, data.content, true)
                                          }

                                          /** 更新预览数据到 stateStore */
                                          if (data.nodeTitle && data.content) {
                                            stateStore.originalWorkProgress[data.nodeTitle] = true
                                            if (!stateStore.originalWorkData) {
                                              stateStore.originalWorkData = {}
                                            }
                                            stateStore.originalWorkData[data.nodeTitle] = data.content

                                            /** 同时更新到卡片中，确保数据持久化 */
                                            if (originalWorkCard && !originalWorkCard.originalWorkData) {
                                              originalWorkCard.originalWorkData = {}
                                            }
                                            if (originalWorkCard) {
                                              originalWorkCard.originalWorkData[data.nodeTitle] = data.content
                                              /** 触发消息列表更新 */
                                              trendAg.messageStore.messages = [...trendAg.messageStore.messages]
                                            }
                                          }
                                        },
                                        (error) => {
                                          stateStore.originalWorkLoading = false
                                          stateStore.isOriginalWorkRunning = false // 清除运行标志
                                        },
                                        () => {
                                          stateStore.originalWorkLoading = false
                                          stateStore.isOriginalWorkRunning = false // 清除运行标志

                                          /** 完成第五个thinking流 */
                                          streamingDataManager.completeStream(STREAM_IDS.ORIGINAL_WORK_THINKING)

                                          /** 完成后确保数据同步到卡片 */
                                          if (originalWorkCard && stateStore.originalWorkData) {
                                            originalWorkCard.originalWorkData = { ...stateStore.originalWorkData }
                                            trendAg.messageStore.messages = [...trendAg.messageStore.messages]
                                          }
                                        },
                                        currentTaskInstanceId as string,
                                        {
                                          selectedTopicId,
                                          selectedRednoteId,
                                          planningReport,
                                          userSubmitPic,
                                        },
                                      )
                                    }

                                    /** 成功后只关闭加载状态，保持按钮禁用 */
                                    setIsContentCreationLoading(false)
                                  }
                                  catch (error) {
                                    stateStore.originalWorkLoading = false
                                    stateStore.isOriginalWorkRunning = false // 清除运行标志
                                    /** 失败时恢复按钮状态 */
                                    setIsContentCreationDisabled(false)
                                    setIsContentCreationLoading(false)
                                  }
                                } }
                              >
                                <div className="flex items-center gap-2">
                                  {isContentCreationLoading && (
                                    <div className="h-4 w-4 animate-spin border-2 border-white border-t-transparent rounded-full" />
                                  )}
                                  Start content creation
                                </div>
                              </button>
                            </div>

                          </div>
                        )}
                        {/* 第五个thinking组件 - original_work工作流的thinking数据 */}
                        <OriginalWorkThinkingStream
                          show={ showOriginalWorkThinking }
                          onComplete={ handleOriginalWorkThinkingComplete }
                        />
                        {/* RedNote Post Launch Plan 卡片和 Continue to content posting 按钮 - 在点击 Start content creation 后显示 */}
                        {showContinueToPosting && (
                          <>
                            {/* RedNote Post Launch Plan 卡片 */}
                            {showPostLaunchCard && (
                              <div className="mb-6 mt-6 w-106.5">
                                <SelectableGradientCard
                                  className="cursor-pointer"
                                  borderWidth={ 1.5 }
                                  borderGradient="linear-gradient(90deg, #DD9DFF 0%, #36D3FF 100%)"
                                  hoverBackground="linear-gradient(90deg, rgba(221, 157, 255, 0.10) 0%, rgba(54, 211, 255, 0.10) 100%)"
                                  selectedBackground="linear-gradient(90deg, rgba(221, 157, 255, 0.10) 0%, rgba(54, 211, 255, 0.10) 100%)"
                                  cardConfig={ {
                                    leftIcon: {
                                      show: true,
                                      icon: <img src="/src/assets/image/home/<USER>" alt="Operations Manager" className="h-12 w-12 rounded-lg" />,
                                      size: 'lg' as const,
                                    },
                                    rightIcon: {
                                      show: true,
                                      icon: (
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                          <path d="M9 18L15 12L9 6" stroke="#999" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                                        </svg>
                                      ),
                                      size: 'md' as const,
                                    },
                                    content: {
                                      title: 'RedNote Post Launch Plan',
                                      description: 'Ready to launch your content strategy on RedNote with optimized posting schedule and engagement tactics.',
                                    },
                                    socialPost: {
                                      image: 'https://images.unsplash.com/photo-1611162617474-5b21e879e113?w=400&h=300&fit=crop',
                                      description: (() => {
                                        const selectionType = stateStore.trendSelectionState?.selectionType
                                        if (selectionType === 'hotpots') {
                                          /** 使用 duibiao_disassembly_text 的数据 */
                                          if (stateStore.duibiaoDisassemblyLoading) {
                                            return 'Generating content based on trending topics...'
                                          }
                                          else if (stateStore.duibiaoDisassemblyData) {
                                            const content = stateStore.duibiaoDisassemblyData['笔记正文']
                                              || stateStore.duibiaoDisassemblyData.content
                                              || stateStore.duibiaoDisassemblyData.text || ''
                                            return content
                                              ? `${content.substring(0, 100)}...`
                                              : 'Content generation completed.'
                                          }
                                          return 'Preparing duibiao content generation...'
                                        }
                                        else {
                                          /** 使用 original_work 的数据 */
                                          if (stateStore.originalWorkLoading) {
                                            return 'Creating original content for RedNote...'
                                          }
                                          else if (stateStore.originalWorkData) {
                                            const content = stateStore.originalWorkData['笔记正文']
                                              || stateStore.originalWorkData.content
                                              || stateStore.originalWorkData.text || ''
                                            return content
                                              ? `${content.substring(0, 100)}...`
                                              : 'Original content created successfully.'
                                          }
                                          return 'Strategic content planning for maximum engagement on RedNote platform...'
                                        }
                                      })(),
                                      author: {
                                        name: 'Content Strategist',
                                        avatar: 'https://sns-avatar-qc.xhscdn.com/avatar/1040g2jo31aje2ouc6q0049042ub7u510c0c06r0?imageView2/2/w/120/format/webp|imageMogr2/strip',
                                      },
                                      stats: {
                                        likes: 2.8,
                                      },
                                    },
                                    layout: 'social' as const,
                                  } }
                                  onClick={ () => {
                                    /** 查找 original-work 卡片的数据 */
                                    const originalWorkCard = trendAg.messageStore.messages.find(
                                      (msg: any) => msg.meta?.cardId === 'original-work-report',
                                    )

                                    /** 如果有数据，同步到 stateStore */
                                    if (originalWorkCard && originalWorkCard.originalWorkData) {
                                      stateStore.originalWorkData = { ...originalWorkCard.originalWorkData }
                                    }

                                    /** 清除 imagePreviewData，确保使用 originalWorkData */
                                    ChatEventBus.emit('updateImagePreview', null)

                                    /** 显示 ImagePreviewCard */
                                    ChatEventBus.emit('showImagePreview', { isUserAction: true })
                                  } }
                                />
                              </div>
                            )}

                            {/* Continue to content posting 按钮 - 在 RedNote Post Launch Plan 卡片下方 */}
                            <div className="mb-6 flex justify-start">
                              <button
                                className={ cn(
                                  'px-8 py-3 rounded-full text-sm font-medium transition-colors flex items-center gap-2',
                                  isPostingDisabled
                                    ? 'bg-gray-400 text-gray-200 cursor-not-allowed'
                                    : 'bg-black text-white hover:bg-gray-800',
                                  'disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-black',
                                ) }
                                disabled={ isPostingDisabled }
                                onClick={ handleContinueToContentPosting }
                              >
                                {isPostingDisabled && <LoadingIcon size="sm" />}
                                Continue to content posting
                              </button>
                            </div>
                          </>
                        )}

                        {/* 第二个 RedNote Post Launch Plan 卡片 - 在点击 Continue to content posting 后显示 */}
                        {showSecondPostLaunchCard && (
                          <div className="mb-6 mt-6 w-106.5">
                            <SelectableGradientCard
                              className="cursor-pointer"
                              borderWidth={ 1.5 }
                              borderGradient="linear-gradient(90deg, #DD9DFF 0%, #36D3FF 100%)"
                              hoverBackground="linear-gradient(90deg, rgba(221, 157, 255, 0.10) 0%, rgba(54, 211, 255, 0.10) 100%)"
                              selectedBackground="linear-gradient(90deg, rgba(221, 157, 255, 0.10) 0%, rgba(54, 211, 255, 0.10) 100%)"
                              cardConfig={ {
                                leftIcon: {
                                  show: true,
                                  icon: <img src="/src/assets/image/home/<USER>" alt="Operations Manager" className="h-12 w-12 rounded-lg" />,
                                  size: 'lg' as const,
                                },
                                rightIcon: {
                                  show: true,
                                  icon: (
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                      <path d="M9 18L15 12L9 6" stroke="#999" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                                    </svg>
                                  ),
                                  size: 'md' as const,
                                },
                                content: {
                                  title: 'Post Content Review',
                                  description: 'Review and preview your content before publishing on RedNote.',
                                },
                                socialPost: {
                                  image: 'https://images.unsplash.com/photo-1600880292203-757bb62b4baf?w=400&h=300&fit=crop',
                                  description: 'Executing strategic content deployment for maximum engagement...',
                                  author: {
                                    name: 'Launch Specialist',
                                    avatar: 'https://sns-avatar-qc.xhscdn.com/avatar/1040g2jo31aje2ouc6q0049042ub7u510c0c06r0?imageView2/2/w/120/format/webp|imageMogr2/strip',
                                  },
                                  stats: {
                                    likes: 4.2,
                                  },
                                },
                                layout: 'social' as const,
                              } }
                              onClick={ async () => {
                                /**
                                 * 触发显示 PhoneFrame (右侧预览卡片)
                                 * 根据选择类型获取不同的数据
                                 */
                                let phoneFrameData = {}

                                /** 判断选择类型 */
                                const selectionType = stateStore.trendSelectionState?.selectionType

                                /** 定义变量在外层作用域 */
                                let imageUrls: string[] = []

                                if (selectionType === 'hotpots') {
                                  /** 选中了hotpots_analysis话题，使用duibiao_disassembly_text的数据 */

                                  /** 使用 duibiao_disassembly_text 的真实数据 */
                                  let title = ''
                                  let content = ''
                                  let imageUrl = ''
                                  let tag = ''

                                  /** 辅助函数：从任意字符串中提取URL */
                                  const extractUrls = (text: string): string[] => {
                                    const urlRegex = /https?:\/\/[^\s"'<>\]]+/gi
                                    const matches = text.match(urlRegex) || []
                                    /** 提取所有HTTP/HTTPS URL，不过滤 */
                                    return matches
                                  }

                                  /** 辅助函数：递归提取所有URL */
                                  const extractAllUrls = (data: any): string[] => {
                                    const urls: string[] = []

                                    if (typeof data === 'string') {
                                      urls.push(...extractUrls(data))
                                    }
                                    else if (Array.isArray(data)) {
                                      data.forEach((item) => {
                                        urls.push(...extractAllUrls(item))
                                      })
                                    }
                                    else if (data && typeof data === 'object') {
                                      Object.values(data).forEach((value) => {
                                        urls.push(...extractAllUrls(value))
                                      })
                                    }

                                    return urls
                                  }

                                  /** 从 duibiaoDisassemblyData 获取数据（数据是以nodeTitle为key的对象） */
                                  if (stateStore.duibiaoDisassemblyData) {
                                    /** 从不同的节点中提取数据 */
                                    title = stateStore.duibiaoDisassemblyData['笔记title']
                                      || stateStore.duibiaoDisassemblyData.title || ''

                                    content = stateStore.duibiaoDisassemblyData['笔记正文']
                                      || stateStore.duibiaoDisassemblyData.content
                                      || stateStore.duibiaoDisassemblyData.text || ''

                                    tag = stateStore.duibiaoDisassemblyData['标签']
                                      || stateStore.duibiaoDisassemblyData.tag
                                      || stateStore.duibiaoDisassemblyData.tags || ''

                                    /** 特殊处理 image 节点 - 根据实际数据结构提取 */
                                    // imageUrls 已在外层定义
                                    if (stateStore.duibiaoDisassemblyData.image) {
                                      let imageData = stateStore.duibiaoDisassemblyData.image

                                      /** 如果是字符串（JSON数组），解析它 */
                                      if (typeof imageData === 'string') {
                                        try {
                                          // image 节点的格式是 JSON 字符串数组，如: "[\"url1\",\"url2\"]"
                                          const parsedUrls = JSON.parse(imageData)
                                          if (Array.isArray(parsedUrls)) {
                                            imageUrls = parsedUrls.filter(url => typeof url === 'string' && url.includes('http'))
                                          }
                                        }
                                        catch (e) {
                                          /** 如果解析失败，使用递归提取 */
                                          imageUrls = extractAllUrls(imageData)
                                        }
                                      }
                                      else if (Array.isArray(imageData)) {
                                        /** 如果已经是数组，直接使用 */
                                        imageUrls = imageData.filter(url => typeof url === 'string' && url.includes('http'))
                                      }

                                      if (imageUrls.length > 0) {
                                        imageUrl = imageUrls[0] // 使用第一个URL作为封面
                                      }
                                    }

                                    /** 如果image节点没有找到，遍历所有字段查找URL */
                                    if (!imageUrl) {
                                      for (const [key, value] of Object.entries(stateStore.duibiaoDisassemblyData)) {
                                        if (value) {
                                          const urls = extractAllUrls(value)
                                          if (urls.length > 0) {
                                            imageUrl = urls[0]
                                            break
                                          }
                                        }
                                      }
                                    }
                                  }

                                  /** 如果没有从duibiao获取到数据，尝试从缓存获取 */
                                  if (!title || !content || imageUrls.length === 0) {
                                    try {
                                      const { getWorkflowOutput } = await import('../stores/cozeStreamApi')
                                      const duibiaoCache = getWorkflowOutput('duibiao_disassembly_text')

                                      if (duibiaoCache) {
                                        title = title || duibiaoCache['笔记title'] || duibiaoCache.title || ''
                                        content = content || duibiaoCache['笔记正文'] || duibiaoCache.content || duibiaoCache.text || ''
                                        tag = tag || duibiaoCache['标签'] || duibiaoCache.tag || duibiaoCache.tags || ''

                                        /** 从缓存获取图片 */
                                        if (imageUrls.length === 0 && duibiaoCache.image) {
                                          const imageData = duibiaoCache.image
                                          if (typeof imageData === 'string') {
                                            try {
                                              const parsedUrls = JSON.parse(imageData)
                                              if (Array.isArray(parsedUrls)) {
                                                imageUrls = parsedUrls.filter(url => typeof url === 'string' && url.includes('http'))
                                              }
                                            }
                                            catch (e) {
                                            }
                                          }
                                        }
                                      }
                                    }
                                    catch (e) {

                                    }
                                  }

                                  /** 如果没有图片，从 distillDarenListData 获取 */
                                  if (!imageUrl && stateStore.distillDarenListData) {
                                    const { KOC = [], KOL = [], Regulars = [] } = stateStore.distillDarenListData
                                    const firstNote = KOL[0] || KOC[0] || Regulars[0]
                                    if (firstNote && firstNote.image && Array.isArray(firstNote.image) && firstNote.image.length > 0) {
                                      imageUrl = firstNote.image[0]
                                    }
                                  }

                                  /** 确保正确设置 phoneFrameData */
                                  phoneFrameData = {
                                    title: title || 'Product Content Preview',
                                    content: content || 'Content preview will be displayed here',
                                    tag: tag || '#ProductPreview#',
                                    showPhoneFrame: true,
                                    showDarenList: false, // 明确关闭达人列表模式
                                    pic_url: imageUrls.length > 0
                                      ? imageUrls[0]
                                      : imageUrl || stateStore.userSubmitPic || '',
                                    /** 传递所有图片URL用于轮播 */
                                    images: imageUrls.length > 0
                                      ? imageUrls
                                      : imageUrl
                                        ? [imageUrl]
                                        : [],
                                  }
                                }
                                else {
                                  let imageUrl = stateStore.userSubmitPic || ''
                                  let content = ''
                                  let title = ''
                                  let tag = ''

                                  /** 辅助函数：从任意字符串中提取URL */
                                  const extractUrls = (text: string): string[] => {
                                    const urlRegex = /https?:\/\/[^\s"'<>\]]+/gi
                                    const matches = text.match(urlRegex) || []
                                    /** 提取所有HTTP/HTTPS URL，不过滤 */
                                    return matches
                                  }

                                  /** 辅助函数：递归提取所有URL */
                                  const extractAllUrls = (data: any): string[] => {
                                    const urls: string[] = []

                                    if (typeof data === 'string') {
                                      urls.push(...extractUrls(data))
                                    }
                                    else if (Array.isArray(data)) {
                                      data.forEach((item) => {
                                        urls.push(...extractAllUrls(item))
                                      })
                                    }
                                    else if (data && typeof data === 'object') {
                                      Object.values(data).forEach((value) => {
                                        urls.push(...extractAllUrls(value))
                                      })
                                    }

                                    return urls
                                  }

                                  /** 尝试从缓存获取 distill_daren_list 数据 */
                                  if (!stateStore.distillDarenListData) {
                                    try {
                                      const { getWorkflowOutput } = await import('../stores/cozeStreamApi')
                                      const distillCache = getWorkflowOutput('distill_daren_list')

                                      if (distillCache) {
                                        stateStore.distillDarenListData = distillCache
                                      }
                                    }
                                    catch (e) {

                                    }
                                  }

                                  if (stateStore.distillDarenListData) {
                                    const { KOC = [], KOL = [], Regulars = [] } = stateStore.distillDarenListData

                                    /** 如果有 daren_list 字段，尝试从中提取文案 */
                                    if (stateStore.distillDarenListData.daren_list) {
                                      try {
                                        let darenList = stateStore.distillDarenListData.daren_list
                                        if (typeof darenList === 'string') {
                                          /** 先尝试提取其中的URL作为图片 */
                                          const urls = extractUrls(darenList)
                                          if (urls.length > 0 && !imageUrl) {
                                            imageUrl = urls[0]
                                          }

                                          /** 尝试解析JSON */
                                          try {
                                            darenList = JSON.parse(darenList)
                                          }
                                          catch (e) {
                                            /** 如果不是JSON，保持原样 */
                                          }
                                        }
                                        if (Array.isArray(darenList) && darenList.length > 0) {
                                          /** 从daren_list中获取第一个文案 */
                                          const firstDaren = darenList[0]
                                          if (firstDaren) {
                                            content = firstDaren.text || firstDaren.content || firstDaren.desc || content
                                            title = firstDaren.title || title

                                            /** 尝试从对象中提取图片URL */
                                            const darenUrls = extractAllUrls(firstDaren)
                                            if (darenUrls.length > 0 && !imageUrl) {
                                              imageUrl = darenUrls[0]
                                            }
                                          }
                                        }
                                      }
                                      catch (e) {

                                      }
                                    }

                                    /** 获取选中的笔记或第一个笔记的图片 */
                                    const selectedRednoteId = stateStore.trendSelectionState?.selectedRednoteId
                                    let selectedNote = null

                                    if (selectedRednoteId) {
                                      /** 查找选中的笔记 */
                                      selectedNote = [...KOL, ...KOC, ...Regulars].find(note => note.id === selectedRednoteId)
                                    }

                                    if (!selectedNote) {
                                      /** 如果没有选中，使用第一个 */
                                      selectedNote = KOL[0] || KOC[0] || Regulars[0]
                                    }

                                    if (selectedNote) {
                                      /** 如果没有从daren_list获取到内容，从笔记获取 */
                                      if (!title) {
                                        title = selectedNote.title || ''
                                      }
                                      if (!content) {
                                        content = selectedNote.desc || selectedNote.description || ''
                                      }
                                      /** 获取图片 - 支持多种格式 */
                                      if (!imageUrl) {
                                        if (selectedNote.image) {
                                          if (Array.isArray(selectedNote.image) && selectedNote.image.length > 0) {
                                            imageUrl = selectedNote.image[0]
                                          }
                                          else if (typeof selectedNote.image === 'string') {
                                            /** 如果image是字符串，可能包含URL */
                                            const urls = extractUrls(selectedNote.image)
                                            if (urls.length > 0) {
                                              imageUrl = urls[0]
                                            }
                                            else {
                                              imageUrl = selectedNote.image
                                            }
                                          }
                                        }
                                        else if (selectedNote.imageUrl) {
                                          imageUrl = selectedNote.imageUrl
                                        }
                                        else {
                                          /** 尝试从整个对象中提取URL */
                                          const noteUrls = extractAllUrls(selectedNote)
                                          if (noteUrls.length > 0) {
                                            imageUrl = noteUrls[0]
                                          }
                                        }
                                      }
                                    }
                                  }

                                  /** 从 originalWorkData 获取数据（如果存在） */
                                  if (stateStore.originalWorkData) {
                                    // originalWorkData 也是以nodeTitle为key的对象
                                    const workTitle = stateStore.originalWorkData['笔记title']
                                      || stateStore.originalWorkData.title || ''
                                    const workContent = stateStore.originalWorkData['笔记正文']
                                      || stateStore.originalWorkData.content
                                      || stateStore.originalWorkData.text || ''
                                    const workTag = stateStore.originalWorkData['标签']
                                      || stateStore.originalWorkData.tag
                                      || stateStore.originalWorkData.tags || ''

                                    /** 优先使用 originalWorkData 的数据 */
                                    title = workTitle || title
                                    content = workContent || content
                                    tag = workTag || tag

                                    /** 尝试多种方式获取图片 */
                                    if (!imageUrl) {
                                      // 1. 直接查找 pic_url 或 image 字段
                                      const workPic = stateStore.originalWorkData.pic_url
                                        || stateStore.originalWorkData.image
                                        || stateStore.originalWorkData['图片'] || ''
                                      if (workPic) {
                                        if (typeof workPic === 'string' && workPic.includes('http')) {
                                          imageUrl = workPic
                                        }
                                        else if (typeof workPic === 'string') {
                                          /** 尝试解析JSON数组格式的图片数据 */
                                          try {
                                            /** 处理转义字符的JSON字符串 */
                                            let imageString = workPic

                                            /** 如果字符串包含转义的引号，先处理转义 */
                                            if (imageString.includes('\\"')) {
                                              /** 替换转义的引号为正常引号 */
                                              imageString = imageString.replace(/\\"/g, '"')
                                            }

                                            const parsedImages = JSON.parse(imageString)
                                            if (Array.isArray(parsedImages) && parsedImages.length > 0) {
                                              imageUrl = parsedImages[0]
                                            }
                                            else {
                                              /** 尝试从字符串中提取URL */
                                              const urls = extractUrls(workPic)
                                              if (urls.length > 0) {
                                                imageUrl = urls[0]
                                              }
                                            }
                                          }
                                          catch (e) {
                                            /** 如果不是JSON格式，尝试从字符串中提取URL */

                                            const urls = extractUrls(workPic)
                                            if (urls.length > 0) {
                                              imageUrl = urls[0]
                                            }
                                          }
                                        }
                                      }

                                      // 2. 如果还没找到，遍历所有字段查找URL
                                      if (!imageUrl) {
                                        for (const [key, value] of Object.entries(stateStore.originalWorkData)) {
                                          if (value) {
                                            const valueStr = typeof value === 'string'
                                              ? value
                                              : JSON.stringify(value)
                                            const urls = extractUrls(valueStr)
                                            if (urls.length > 0) {
                                              imageUrl = urls[0]
                                              break
                                            }
                                          }
                                        }
                                      }
                                    }
                                  }

                                  /** 处理图片数据，如果是JSON数组格式则解析 */
                                  let processedImages: string[] = []
                                  if (imageUrl) {
                                    processedImages = [imageUrl]
                                  }
                                  else if (stateStore.originalWorkData.image) {
                                    /** 检查image数据类型 */
                                    if (Array.isArray(stateStore.originalWorkData.image)) {
                                      /** 如果已经是数组，直接使用 */
                                      processedImages = stateStore.originalWorkData.image
                                    }
                                    else if (typeof stateStore.originalWorkData.image === 'string') {
                                      try {
                                        /** 处理转义字符的JSON字符串 */
                                        let imageString = stateStore.originalWorkData.image

                                        /** 如果字符串包含转义的引号，先处理转义 */
                                        if (imageString.includes('\\"')) {
                                          /** 替换转义的引号为正常引号 */
                                          imageString = imageString.replace(/\\"/g, '"')
                                        }

                                        const parsedImages = JSON.parse(imageString)
                                        if (Array.isArray(parsedImages)) {
                                          processedImages = parsedImages
                                        }
                                      }
                                      catch (e) {
                                        if (typeof stateStore.originalWorkData.image === 'string') {
                                          processedImages = [stateStore.originalWorkData.image]
                                        }
                                      }
                                    }
                                  }

                                  phoneFrameData = {
                                    title: title || 'RedNote Post Preview',
                                    content: content || 'Your content is ready to be published on RedNote.',
                                    tag: tag || '',
                                    showPhoneFrame: true,
                                    showDarenList: false, // 明确关闭达人列表模式
                                    image: processedImages.length > 0
                                      ? processedImages[0]
                                      : '',
                                    images: processedImages,
                                    pic_url: processedImages.length > 0
                                      ? processedImages[0]
                                      : imageUrl,
                                  }
                                }
                                ChatEventBus.emit('updateImagePreview', phoneFrameData)

                                /** 显示图片预览卡片 */
                                ChatEventBus.emit('showImagePreview', { isUserAction: true })
                              } }
                            />
                          </div>
                        )}

                        {/* Influencer Collaboration 卡片 - 在 Post Content Review 下方 */}
                        {showSecondPostLaunchCard && (
                          <div className="mb-6 mt-6 w-106.5">
                            <SelectableGradientCard
                              className="cursor-pointer"
                              borderWidth={ 1.5 }
                              borderGradient="linear-gradient(90deg, #DD9DFF 0%, #36D3FF 100%)"
                              hoverBackground="linear-gradient(90deg, rgba(221, 157, 255, 0.10) 0%, rgba(54, 211, 255, 0.10) 100%)"
                              selectedBackground="linear-gradient(90deg, rgba(221, 157, 255, 0.10) 0%, rgba(54, 211, 255, 0.10) 100%)"
                              cardConfig={ {
                                leftIcon: {
                                  show: true,
                                  icon: <img src="/src/assets/image/home/<USER>" alt="Strategist" className="h-12 w-12 rounded-lg" />,
                                  size: 'lg' as const,
                                },
                                rightIcon: {
                                  show: true,
                                  icon: (
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                      <path d="M9 18L15 12L9 6" stroke="#999" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                                    </svg>
                                  ),
                                  size: 'md' as const,
                                },
                                content: {
                                  title: 'Influencer Collaboration List',
                                  description: 'Select influencers for potential collaboration opportunities.',
                                },
                                layout: 'simple' as const,
                              } }
                              onClick={ async () => {
                                /** 触发显示达人列表 */
                                let darenListData = null

                                /** 根据选择类型获取对应的达人列表数据 */
                                const selectionType = stateStore.trendSelectionState?.selectionType

                                if (selectionType === 'hotpots') {
                                  /** 使用 duibiao_disassembly_text 的 daren_list */
                                  if (stateStore.duibiaoDisassemblyData?.daren_list) {
                                    try {
                                      let darenList = stateStore.duibiaoDisassemblyData.daren_list
                                      if (typeof darenList === 'string') {
                                        darenList = JSON.parse(darenList)
                                      }
                                      if (Array.isArray(darenList) && darenList.length > 0) {
                                        /** 提取数据结构 */
                                        const firstItem = darenList[0]
                                        if (firstItem && (firstItem.koc || firstItem.kol || firstItem.suren)) {
                                          darenListData = {
                                            KOC: firstItem.koc || [],
                                            KOL: firstItem.kol || [],
                                            suren: firstItem.suren || [],
                                            Regulars: firstItem.suren || [], // 兼容字段
                                          }
                                        }
                                      }
                                    }
                                    catch (e) {

                                    }
                                  }

                                  /** 如果没有从 duibiao 获取到，尝试从缓存获取 */
                                  if (!darenListData) {
                                    try {
                                      const { getWorkflowOutput } = await import('../stores/cozeStreamApi')
                                      const duibiaoCache = getWorkflowOutput('duibiao_disassembly_text')
                                      if (duibiaoCache?.daren_list) {
                                        let darenList = duibiaoCache.daren_list
                                        if (typeof darenList === 'string') {
                                          darenList = JSON.parse(darenList)
                                        }
                                        if (Array.isArray(darenList) && darenList.length > 0) {
                                          const firstItem = darenList[0]
                                          if (firstItem && (firstItem.koc || firstItem.kol || firstItem.suren)) {
                                            darenListData = {
                                              KOC: firstItem.koc || [],
                                              KOL: firstItem.kol || [],
                                              suren: firstItem.suren || [],
                                              Regulars: firstItem.suren || [],
                                            }
                                          }
                                        }
                                      }
                                    }
                                    catch (e) {

                                    }
                                  }
                                }
                                else {
                                  /** 使用 distill_daren_list 的数据 */
                                  darenListData = stateStore.distillDarenListData
                                }

                                /** 获取内容数据（用于切换回 Post Content Review） */
                                let title = ''
                                let content = ''
                                let tag = ''
                                let images: string[] = []

                                if (selectionType === 'hotpots' && stateStore.duibiaoDisassemblyData) {
                                  title = stateStore.duibiaoDisassemblyData.title
                                    || stateStore.duibiaoDisassemblyData['笔记title'] || ''
                                  content = stateStore.duibiaoDisassemblyData.content
                                    || stateStore.duibiaoDisassemblyData['笔记正文'] || ''
                                  tag = stateStore.duibiaoDisassemblyData.tag
                                    || stateStore.duibiaoDisassemblyData['标签'] || ''

                                  /** 提取图片 */
                                  if (stateStore.duibiaoDisassemblyData.image) {
                                    const imageData = stateStore.duibiaoDisassemblyData.image
                                    if (typeof imageData === 'string') {
                                      try {
                                        const parsedUrls = JSON.parse(imageData)
                                        if (Array.isArray(parsedUrls)) {
                                          images = parsedUrls.filter(url => typeof url === 'string' && url.includes('http'))
                                        }
                                      }
                                      catch (e) {

                                      }
                                    }
                                  }
                                }
                                else if (stateStore.originalWorkData) {
                                  title = stateStore.originalWorkData.title
                                    || stateStore.originalWorkData['笔记title'] || ''
                                  content = stateStore.originalWorkData.content
                                    || stateStore.originalWorkData['笔记正文'] || ''
                                  tag = stateStore.originalWorkData.tag
                                    || stateStore.originalWorkData['标签'] || ''
                                }

                                /** 使用获取到的数据或默认值 */
                                const darenData = {
                                  showDarenList: true, // 特殊标识，表示需要显示达人列表
                                  darenListData: darenListData || {
                                    KOC: [],
                                    KOL: [],
                                    suren: [],
                                    Regulars: [],
                                  },
                                  /** 保留内容数据以便切换回 Post Content Review */
                                  title,
                                  content,
                                  tag,
                                  pic_url: images.length > 0
                                    ? images[0]
                                    : '',
                                  images,
                                }

                                /** 更新图片预览数据为达人列表数据 */
                                ChatEventBus.emit('updateImagePreview', darenData)

                                /** 显示图片预览卡片 */
                                ChatEventBus.emit('showImagePreview', { isUserAction: true })
                              } }
                            />
                          </div>
                        )}

                        {/* Original Work 卡片容器 - 功能已移到 RedNote Post Launch Plan 卡片，所以隐藏 */}
                        {false && showTrendReportAfterClick && (
                          <OriginalWorkCardRenderer
                            messageStore={ trendAg.messageStore }
                            stateStore={ stateStore }
                            className="mt-4 w-106.5"
                          />
                        )}

                        {/* OperationsManager 组件区域 - 在Implementation卡片下方 */}
                        {showOperationsManagerComponents && (
                          <>
                            {/* OperationsManager 文本显示组件 */}
                            {operationsManagerDisplayText && (
                              <TextDisplayWithPagination
                                content={ operationsManagerDisplayText }
                                charsPerPage={ 600 }
                                className="mt-4 w-106.5"
                                onCopySuccess={ () => {
                                } }
                                onCopyError={ (error) => {
                                } }
                              />
                            )}

                            {/* Operations阶段 - 流式Thinking显示组件 */}
                            <OperationsThinkingStream
                              operationsManagerDisplayText={ operationsManagerDisplayText }
                            />

                            {/* OperationsManager 专用卡片容器 */}
                            {showOperationsManagerCard && (
                              <OperationsManagerCardRenderer
                                stateStore={ stateStore }
                                className="mt-4 w-106.5"
                              />
                            )}
                          </>
                        )}

                      </motion.div>
                    )}
                  </div>
                </div>

                {/* 悬浮的输入框 - 始终显示在底部 */}
                {showWorkflow && (
                  <div className="absolute bottom-0 left-0 right-0 bg-white p-4">
                    <div className="mx-auto max-w-4xl">
                      <div className="flex items-center gap-3 border rounded-lg bg-gray-50 p-3">
                        <input
                          type="text"
                          value={ isAnalysisStarted
                            ? 'AI analysis in progress...'
                            : 'Analysis will start from workflow above' }
                          readOnly
                          className="flex-1 border-none bg-transparent text-gray-700 outline-none placeholder-gray-400"
                          placeholder={ isAnalysisStarted
                            ? 'Analysis running...'
                            : 'Click the button in workflow to start analysis...' }
                        />
                        {isAnalysisStarted && (
                          <div className="h-8 w-8 flex items-center justify-center">
                            <div className="h-4 w-4 animate-spin border-b-2 border-blue-500 rounded-full"></div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )
          : (
            /** 正常的聊天界面 */
              <div className="relative h-full flex flex-col">
                <motion.div
                  layout
                  className={ cn(
                    'flex-1 flex flex-col gap-4 max-w-4xl mx-auto overflow-auto',
                    isReportOpen && 'max-w-3xl',
                  ) }>

                  <ChatHistory
                    taskStore={ taskStore }
                    messageStore={ messageStore }
                    ref={ chatHistoryRef }
                    className="min-h-0 w-full flex-1 p-4"
                    onDeleteMessage={ removeMessage }
                    stateStore={ stateStore }
                  />
                </motion.div>

                {/* 预留悬浮输入框位置 - 目前不显示 */}
                {false && (
                  <div className="absolute bottom-0 left-0 right-0 border-t border-gray-200 bg-white p-4">
                    <div className="mx-auto max-w-4xl">
                      <div className="flex items-center gap-3 border rounded-lg bg-gray-50 p-3">
                        <input
                          type="text"
                          placeholder="Type your console..."
                          className="flex-1 border-none bg-transparent text-gray-700 outline-none placeholder-gray-400"
                        />
                        <button className="h-8 w-8 flex items-center justify-center rounded-full bg-blue-500 text-white transition-colors hover:bg-blue-600">
                          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M5 12H19M19 12L12 5M19 12L12 19" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                          </svg>
                        </button>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}
      </div>
    </div>

    {/* ReportPreview 已移至父组件的右侧区域 */}
  </div>
})

ChatPage.displayName = 'ChatPage'

export type ChatPageProps = {
  className?: string
  style?: React.CSSProperties
  children?: React.ReactNode

  taskStore: TaskStoreType
  stateStore: StateStoreType
  messageStore: MessageStoreType
  mdToCodePreview: MdToCodePreviewType
  resetDistributionStore: () => void
  reportStore: ReportStoreType
  stepState: StepStateType

  // TopBar 相关 props
  showTopBar?: boolean
  topBarAgents?: any[]
  onTopBarAgentClick?: (agent: any) => void
  topBarDropdownExpanded?: boolean
  onTopBarDropdownToggle?: (expanded: boolean) => void
  onTopBarNewProject?: () => void

  // ChatV2 流程相关 props
  onStartAIAnalysis?: (formData: any) => Promise<void>
}
& React.DetailedHTMLProps<React.ImgHTMLAttributes<HTMLDivElement>, HTMLDivElement>
