import type { MdToCodePreviewType, ReportStoreType, StateStoreType, StepStateType, TaskStoreType } from '../../stores'

import type { WorkflowStatusEvent } from '../../stores/workflowStatusManager'
import type { ReportContentItem } from '../../types'
import { downloadByData, getWinWidth } from '@jl-org/tool'
import jsPDF from 'jspdf'
import {
  FileText,
  PanelLeft,
  PanelRight,
  Send,
} from 'lucide-react'
import { memo, useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { AnimateShow } from '@/components/Animate'
import { Button } from '@/components/Button'
import { god } from '@/god'
import { useRefresh, vShow } from '@/hooks'
import { cn } from '@/utils'
import { DistributionEvent, eventBus } from '../../constants'
import { callModifyReportAPI, getModifiedReportContent, getWorkflowOutput } from '../../stores/cozeStreamApi'
import { workflowStatusManager } from '../../stores/workflowStatusManager'
import { ReportContent } from './ReportContent'

const ReportDisplay = memo((
  {
    onClose,
    handleDownload: _handleDownload,
    reportStore,
    taskStore,
    stepState,
    mdToCodePreview,
    stateStore,
  }: {
    onClose?: () => void
    handleDownload: () => void
    reportStore: ReportStoreType
    taskStore: TaskStoreType
    stepState: StepStateType
    mdToCodePreview: MdToCodePreviewType
    stateStore: StateStoreType
  },
) => {
  const stepSnap = stepState.use()
  const taskSnap = taskStore.use()
  const { items } = reportStore.use()
  const curStepState = stepSnap[taskSnap.currentStep]

  const [activeReportId, setActiveReportId] = useState<string>('')
  const { currentStep } = taskSnap
  const r = useRefresh()

  /** 报告修改相关状态 */
  const [isEditMode, setIsEditMode] = useState(false)
  const [modifyDemand, setModifyDemand] = useState('')
  const [isModifying, setIsModifying] = useState(false)

  const [modifyReportEndData, setModifyReportEndData] = useState<string>('')
  const [streamingModifiedContent, setStreamingModifiedContent] = useState<string>('')

  /** 使用 ref 跟踪当前正在修改的报告 ID，防止状态冲突 */
  const currentModifyingReportId = useRef<string>('')

  /** 工作流状态管理 - 基于事件通知的状态跟踪 */
  const [workflowStates, setWorkflowStates] = useState({
    insight_report: 'idle' as 'idle' | 'started' | 'completed' | 'error',
    competitor_report: 'idle' as 'idle' | 'started' | 'completed' | 'error',
    planning_scheme: 'idle' as 'idle' | 'started' | 'completed' | 'error',
    visualization: 'idle' as 'idle' | 'started' | 'completed' | 'error',
    modify_report: 'idle' as 'idle' | 'started' | 'completed' | 'error',
  })

  /** 保存状态管理 - 区分修改中和已保存状态 */
  const [reportSaveStates, setReportSaveStates] = useState<Map<string, 'unsaved' | 'saved'>>(new Map())

  /** 当前是否在查看可视化结果 */
  const [isViewingVisualization, setIsViewingVisualization] = useState(false)

  /** planning_scheme 工作流是否已经开始过 - 一旦开始，其他报告永久失去编辑权限 */
  const [planningSchemeHasStarted, setPlanningSchemeHasStarted] = useState(false)

  /** 工作流状态监听器 - 替代定时检查机制 */
  useEffect(() => {
    /** 初始化时检查当前工作流状态 */
    const initializeWorkflowStates = () => {
      const currentStates = workflowStatusManager.getAllStatus()
      setWorkflowStates(prev => ({
        ...prev,
        ...currentStates,
      }))
    }

    initializeWorkflowStates()

    const unsubscribe = workflowStatusManager.addGlobalListener((event: WorkflowStatusEvent) => {
      const { workflowName, status } = event

      /** 更新工作流状态 */
      if (['insight_report', 'competitor_report', 'planning_scheme', 'visualization', 'modify_report'].includes(workflowName)) {
        setWorkflowStates(prev => ({
          ...prev,
          [workflowName]: status,
        }))
      }

      /** 特殊处理：一旦 planning_scheme 工作流开始，标记为已开始 */
      if (workflowName === 'planning_scheme' && status === 'started') {
        setPlanningSchemeHasStarted(true)
      }

      /** 重置机制：当用户重新生成 insight_report 或 competitor_report 工作流时，重置限制 */
      if ((workflowName === 'insight_report' || workflowName === 'competitor_report') && status === 'started') {
        setPlanningSchemeHasStarted(false)
      }
    })

    return unsubscribe
  }, [])

  const curReports = useMemo(() => items.filter(item => currentStep === item.meta.step), [items, currentStep])
  const activeReport = useMemo(() => curReports.find(item => item.id === activeReportId), [curReports, activeReportId])

  /** 获取 mapperStore 用于报告类型识别 */
  const mapperStore = useMemo(() => {
    return reportStore.constructor === Object
      ? { nodeTitleToReportId: {} }
      : (reportStore as any).mapperStore || { nodeTitleToReportId: {} }
  }, [reportStore])

  /** 获取当前报告类型 - 使用 ReportContent.tsx 中验证过的成熟逻辑 */
  const getCurrentReportType = useCallback(() => {
    if (!activeReport) {
      return ''
    }

    const { id, title, content, meta: metadata } = activeReport

    /** 方法1: 通过 mapperStore 反向查找（最可靠的方法） */
    for (const [nodeTitle, reportId] of Object.entries(mapperStore.nodeTitleToReportId || {})) {
      if (reportId === id) {
        /** 基于 node_title 确定报告类型 */
        if (nodeTitle.includes('insight') || nodeTitle.includes('research_content')) {
          return 'insight_report'
        }
        if (nodeTitle.includes('competitor') || nodeTitle.includes('research_content_1')) {
          return 'competitor_report'
        }
        if (nodeTitle.includes('planning') || nodeTitle.includes('scheme')) {
          return 'planning_report'
        }
      }
    }

    /** 方法2: 基于 meta.step 和内容特征的组合判断 */
    if (metadata?.step) {
      const step = metadata.step
      const contentText = content || ''

      if (step === 'step1' || step === 'step0') {
        if (contentText.includes('竞品') || contentText.includes('competitor') || contentText.includes('competitive')) {
          return 'competitor_report'
        }
        if (contentText.includes('策划') || contentText.includes('planning') || contentText.includes('marketing')) {
          return 'planning_report'
        }
        return 'insight_report' // 默认为洞察报告
      }
    }

    /** 方法3: 基于标题的详细匹配（作为备选方案） */
    if (!title) {
      return 'unknown'
    }

    const titleLower = title.toLowerCase()

    if (titleLower.includes('insight') || titleLower.includes('洞察') || titleLower.includes('分析报告')) {
      return 'insight_report'
    }
    if (titleLower.includes('planning') || titleLower.includes('scheme') || titleLower.includes('策划') || titleLower.includes('营销') || titleLower.includes('marketing')) {
      return 'planning_report'
    }
    if (titleLower.includes('competitor') || titleLower.includes('competitive') || titleLower.includes('竞品') || titleLower.includes('竞争') || titleLower.includes('对手')) {
      return 'competitor_report'
    }

    return 'unknown'
  }, [activeReport, currentStep, mapperStore])

  /** 获取原始报告内容 */
  const getOriginalReportContent = useCallback((reportType: string): string => {
    try {
      if (reportType === 'insight_report') {
        const insightData = getWorkflowOutput('insight_report')
        return insightData.insight_report || ''
      }
      else if (reportType === 'competitor_report') {
        const competitorData = getWorkflowOutput('competitor_report')
        return competitorData.competitor_report || ''
      }
      else if (reportType === 'planning_report') {
        const planningData = getWorkflowOutput('planning_scheme')
        return planningData.planning_report || ''
      }
      return ''
    }
    catch (error) {
      console.error(`[ReportPreview] 获取 ${reportType} 原始内容失败:`, error)
      return ''
    }
  }, [])

  /** 获取当前报告的正确内容（优先使用修改后的内容） */
  const getCurrentReportContent = useCallback(() => {
    if (!activeReport) {
      return ''
    }

    const reportType = getCurrentReportType()

    if (reportType === 'unknown' || reportType === '') {
      /** 如果无法识别报告类型，返回当前显示的内容 */
      return activeReport.content || ''
    }

    /** 首先尝试获取修改后的内容 */
    const modifiedContent = getModifiedReportContent(reportType)
    if (modifiedContent) {
      console.warn(`[ReportPreview] 使用修改后的 ${reportType} 内容进行下载`)
      return modifiedContent
    }

    /** 如果没有修改内容，获取原始报告内容 */
    const originalContent = getOriginalReportContent(reportType)
    if (originalContent) {
      console.warn(`[ReportPreview] 使用原始 ${reportType} 内容进行下载`)
      return originalContent
    }

    /** 最后回退到当前显示的内容 */
    console.warn(`[ReportPreview] 回退使用当前显示的内容进行下载`)
    return activeReport.content || ''
  }, [activeReport, getCurrentReportType, getOriginalReportContent])

  /** 生成报告文件名 */
  const generateReportFileName = useCallback((reportType: string): string => {
    const timestamp = new Date().toISOString().slice(0, 10) // YYYY-MM-DD

    switch (reportType) {
      case 'insight_report':
        return `Insight_Report_${timestamp}.pdf`
      case 'competitor_report':
        return `Competitor_Analysis_${timestamp}.pdf`
      case 'planning_report':
        return `Marketing_Plan_${timestamp}.pdf`
      default:
        return `Report_${timestamp}.pdf`
    }
  }, [])

  /** 将 Markdown 内容转换为 HTML */
  const convertMarkdownToHtml = useCallback((markdown: string): string => {
    if (!markdown)
      return ''

    /** 简单的 Markdown 转 HTML 转换 */
    let html = markdown
      /** 标题转换 */
      .replace(/^### (.*$)/gim, '<h3>$1</h3>')
      .replace(/^## (.*$)/gim, '<h2>$1</h2>')
      .replace(/^# (.*$)/gim, '<h1>$1</h1>')
      /** 粗体和斜体 */
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      /** 列表 */
      .replace(/^\* (.*$)/gim, '<li>$1</li>')
      .replace(/^- (.*$)/gim, '<li>$1</li>')
      /** 换行 */
      .replace(/\n\n/g, '</p><p>')
      .replace(/\n/g, '<br>')

    /** 包装列表项 */
    html = html.replace(/(<li>.*<\/li>)/gs, '<ul>$1</ul>')

    /** 包装段落 */
    if (!html.startsWith('<h') && !html.startsWith('<ul')) {
      html = `<p>${html}</p>`
    }

    return html
  }, [])

  /** 生成 PDF 文件 */
  const generatePDF = useCallback(async (content: string, fileName: string): Promise<void> => {
    try {
      /** 创建 jsPDF 实例 */
      const pdf = new (jsPDF as any)({
        orientation: 'portrait',
        unit: 'mm',
        format: 'a4',
      })

      /** 设置字体 - 使用 courier 字体，对中文字符有更好的支持 */
      pdf.setFont('courier', 'normal')
      pdf.setFontSize(12)

      /** 页面配置 */
      const pageWidth = pdf.internal.pageSize.getWidth()
      const pageHeight = pdf.internal.pageSize.getHeight()
      const margin = 20
      const maxWidth = pageWidth - 2 * margin
      const lineHeight = 7
      let currentY = margin

      /** 添加标题 */
      const reportTitle = fileName.replace('.pdf', '').replace(/_/g, ' ')
      pdf.setFontSize(16)
      pdf.setFont('courier', 'normal')
      pdf.text(reportTitle, margin, currentY)
      currentY += lineHeight * 2

      /** 添加生成时间 */
      pdf.setFontSize(10)
      pdf.setFont('courier', 'normal')
      const timestamp = new Date().toLocaleString('zh-CN')
      pdf.text(`Generated: ${timestamp}`, margin, currentY)
      currentY += lineHeight * 2

      /** 重置字体 */
      pdf.setFontSize(12)
      pdf.setFont('courier', 'normal')

      /** 处理内容 - 改进的文本处理 */
      let processedContent = content

      /** 如果内容看起来像 Markdown，进行转换 */
      if (content.includes('#') || content.includes('**') || content.includes('*') || content.includes('-')) {
        const htmlContent = convertMarkdownToHtml(content)

        /** 更精细的 HTML 标签处理 */
        processedContent = htmlContent
          /** 标题处理 - 添加更多空白 */
          .replace(/<h1>/g, '\n\n【')
          .replace(/<\/h1>/g, '】\n')
          .replace(/<h2>/g, '\n\n■ ')
          .replace(/<\/h2>/g, '\n')
          .replace(/<h3>/g, '\n▲ ')
          .replace(/<\/h3>/g, '\n')
          /** 强调文本 */
          .replace(/<strong>/g, '【')
          .replace(/<\/strong>/g, '】')
          .replace(/<em>/g, '')
          .replace(/<\/em>/g, '')
          /** 列表处理 */
          .replace(/<ul>/g, '\n')
          .replace(/<\/ul>/g, '\n')
          .replace(/<li>/g, '• ')
          .replace(/<\/li>/g, '\n')
          /** 段落和换行 */
          .replace(/<p>/g, '\n')
          .replace(/<\/p>/g, '\n')
          .replace(/<br>/g, '\n')
          /** HTML 实体 */
          .replace(/&nbsp;/g, ' ')
          .replace(/&amp;/g, '&')
          .replace(/&lt;/g, '<')
          .replace(/&gt;/g, '>')
          /** 清理多余的换行 */
          .replace(/\n{3,}/g, '\n\n')
          .trim()
      }

      /** 分割文本为行 */
      const lines = processedContent.split('\n')

      for (const line of lines) {
        if (!line.trim()) {
          currentY += lineHeight / 2
          continue
        }

        /** 检查是否需要换页 */
        if (currentY > pageHeight - margin - lineHeight) {
          pdf.addPage()
          currentY = margin
        }

        /** 根据内容类型设置不同的字体样式 */
        if (line.startsWith('【') && line.endsWith('】')) {
          /** 主标题 */
          pdf.setFontSize(14)
          pdf.setFont('courier', 'normal')
        }
        else if (line.startsWith('■ ')) {
          /** 二级标题 */
          pdf.setFontSize(13)
          pdf.setFont('courier', 'normal')
        }
        else if (line.startsWith('▲ ')) {
          /** 三级标题 */
          pdf.setFontSize(12)
          pdf.setFont('courier', 'normal')
        }
        else if (line.startsWith('• ')) {
          /** 列表项 */
          pdf.setFontSize(11)
          pdf.setFont('courier', 'normal')
        }
        else {
          /** 普通文本 */
          pdf.setFontSize(11)
          pdf.setFont('courier', 'normal')
        }

        /** 处理长行文本换行 - 改进中文字符处理 */
        try {
          const wrappedLines = pdf.splitTextToSize(line, maxWidth)

          for (const wrappedLine of wrappedLines) {
            /** 再次检查是否需要换页 */
            if (currentY > pageHeight - margin - lineHeight) {
              pdf.addPage()
              currentY = margin
            }

            /** 使用更安全的文本输出方式 */
            pdf.text(String(wrappedLine), margin, currentY)
            currentY += lineHeight
          }
        }
        catch (textError) {
          /** 如果文本处理失败，尝试简化处理 */
          console.warn('[ReportPreview] 文本处理警告:', textError)
          if (currentY > pageHeight - margin - lineHeight) {
            pdf.addPage()
            currentY = margin
          }
          /** 直接输出原始行，让 jsPDF 自行处理 */
          pdf.text(String(line), margin, currentY)
          currentY += lineHeight
        }

        /** 标题后添加额外间距 */
        if (line.startsWith('【') || line.startsWith('■ ') || line.startsWith('▲ ')) {
          currentY += lineHeight / 2
        }
      }

      /** 保存 PDF */
      pdf.save(fileName)

      console.warn(`[ReportPreview] 成功生成 PDF: ${fileName}`)
    }
    catch (error) {
      console.error('[ReportPreview] 生成 PDF 失败:', error)
      throw error
    }
  }, [convertMarkdownToHtml])

  /** 修复后的下载方法 */
  const handleDownloadCurrent = useCallback(async () => {
    if (!activeReport) {
      return
    }

    const reportType = getCurrentReportType()
    const content = getCurrentReportContent()

    if (!content || content.trim().length === 0) {
      return
    }

    const fileName = generateReportFileName(reportType)

    try {
      console.warn(`[ReportPreview] 开始生成 PDF 报告: ${fileName}`)
      console.warn(`[ReportPreview] 报告类型: ${reportType}`)
      console.warn(`[ReportPreview] 内容长度: ${content.length}`)

      /** 使用 PDF 格式下载 */
      await generatePDF(content, fileName)
    }
    catch (error) {
      console.error('[ReportPreview] 下载报告失败:', error)
    }
  }, [activeReport, getCurrentReportType, getCurrentReportContent, generateReportFileName, generatePDF])

  /** 基于工作流状态通知的动态编辑按钮控制 */
  const canEditReport = useMemo(() => {
    if (!activeReport) {
      return false
    }

    /** 获取当前报告类型 */
    const reportType = getCurrentReportType()

    /** 处理未知报告类型 */
    if (reportType === 'unknown' || reportType === '') {
      return false
    }

    /** 检查报告是否已保存（一旦保存就不能再次修改） */
    const saveState = reportSaveStates.get(activeReport.id)
    if (saveState === 'saved') {
      return false
    }
    console.log(planningSchemeHasStarted, 'planningSchemeHasStartedplanningSchemeHasStartedplanningSchemeHasStartedplanningSchemeHasStarted')

    /** 1. planning_scheme 工作流开始后的永久限制 */
    if (planningSchemeHasStarted) {
      /**
       * 一旦 planning_scheme 工作流开始过，其他报告永久失去编辑权限
       * 除非用户重新生成这些报告的工作流
       */
      if (reportType === 'insight_report' || reportType === 'competitor_report') {
        return false
      }
    }

    /** 2. HTML可视化工作流期间的限制 */
    if (workflowStates.visualization === 'started') {
      return false
    }

    /** 3. 如果用户正在查看可视化结果，隐藏编辑按钮 */
    if (isViewingVisualization) {
      return false
    }

    /** 4. 修改报告工作流执行期间的限制 */
    if (workflowStates.modify_report === 'started') {
      return false
    }

    /** 5. 基于当前报告类型的工作流完成状态检查 */
    let currentWorkflowCompleted = false
    if (reportType === 'insight_report') {
      currentWorkflowCompleted = workflowStates.insight_report === 'completed'
    }
    else if (reportType === 'competitor_report') {
      currentWorkflowCompleted = workflowStates.competitor_report === 'completed'
    }
    else if (reportType === 'planning_report') {
      currentWorkflowCompleted = workflowStates.planning_scheme === 'completed'
    }
    else {
      return false
    }
    /** 6. 确保当前步骤状态不是处理中 */
    const isNotProcessing = !curStepState?.isProcessing

    return currentWorkflowCompleted && isNotProcessing
  }, [
    activeReport,
    getCurrentReportType,
    reportSaveStates,
    workflowStates,
    isViewingVisualization,
    planningSchemeHasStarted,
    curStepState?.isProcessing,
  ])

  /** 监听可视化状态变化 - 从 ReportContent 组件接收通知 */
  useEffect(() => {
    const handleVisualizationStateChange = (event: CustomEvent) => {
      setIsViewingVisualization(event.detail.isViewing)
    }

    window.addEventListener('visualization-state-change', handleVisualizationStateChange as EventListener)

    return () => {
      window.removeEventListener('visualization-state-change', handleVisualizationStateChange as EventListener)
    }
  }, [])

  /** 处理发送修改请求 */
  const handleSendModification = useCallback(async () => {
    /** 防重复触发检查 */
    if (!modifyDemand.trim()) {
      return
    }

    if (!activeReport) {
      return
    }

    if (isModifying) {
      god.messageWarn('正在修改中，请等待当前操作完成')
      return
    }

    /** 获取并验证报告类型 */
    const reportType = getCurrentReportType()

    if (!reportType || reportType === 'unknown') {
      console.error('[ReportPreview] 无法确定报告类型或报告类型未知，取消操作')
      god.messageError('无法确定报告类型，请确认当前报告是否支持修改')
      return
    }

    /** 验证报告类型是否支持修改 */
    const supportedTypes = ['insight_report', 'competitor_report', 'planning_report']
    if (!supportedTypes.includes(reportType)) {
      console.error('[ReportPreview] 不支持的报告类型:', reportType)
      god.messageError(`不支持的报告类型: ${reportType}`)
      return
    }

    setIsModifying(true)

    /** 记录当前正在修改的报告 ID，防止状态冲突 */
    currentModifyingReportId.current = activeReport.id

    /** 清空之前的数据，准备接收新的流式内容 */
    setModifyReportEndData('')
    setStreamingModifiedContent('')

    /** 步骤1：清除原本的报告内容，准备接收新内容 */
    if (activeReport) {
      try {
        const currentItems = reportStore.items
        const reportIndex = currentItems.findIndex(item => item.id === activeReport.id)

        if (reportIndex !== -1) {
          /** 使用更安全的更新方式，避免触发不必要的重新渲染 */
          const targetItem = currentItems[reportIndex]
          if (targetItem.content !== '') {
            const updatedItems = [...currentItems]
            updatedItems[reportIndex] = {
              ...updatedItems[reportIndex],
              content: '', // 清空原内容
            }
            reportStore.items = updatedItems
          }
          else {
            /** 报告内容已为空，跳过清空操作 */
          }
        }
      }
      catch (error) {
        console.error('[ReportPreview] 清空原报告内容失败:', error)
      }
    }

    try {
      await callModifyReportAPI(
        reportType,
        modifyDemand,
        (data) => {
          /** 处理流式报告内容 - 改为追加而不是替换 */
          if (data.nodeTitle === 'modified_report' && data.content && activeReport) {
            /** 累积流式内容 */
            setStreamingModifiedContent((prev) => {
              const newContent = prev + data.content

              /** 实时更新报告内容显示 */
              try {
                const currentItems = reportStore.items
                const reportIndex = currentItems.findIndex(item => item.id === activeReport.id)

                if (reportIndex !== -1) {
                  const updatedItems = [...currentItems]
                  updatedItems[reportIndex] = {
                    ...updatedItems[reportIndex],
                    content: newContent, // 使用累积的内容
                  }
                  reportStore.items = updatedItems
                }
              }
              catch (error) {
                console.error('[ReportPreview] 流式更新报告内容失败:', error)
              }

              return newContent
            })
          }

          /** 存储 End 数据作为最终版本 */
          if (data.nodeTitle === 'End' && data.content) {
            setModifyReportEndData(data.content)
          }
        },
        (error) => {
          console.error('[ReportPreview] 修改报告失败:', error)
          god.messageError(`修改报告失败: ${error.message}`)
          setIsModifying(false)

          /** 清理保护状态 */
          currentModifyingReportId.current = ''
        },
        () => {
          /** 步骤3：工作流完成，Send 按钮恢复可点击状态 */
          setIsModifying(false)

          /** 清理保护状态 */
          currentModifyingReportId.current = ''

          /** 在数据存储层面，将当前报告内容替换为 End 数据（如果有的话） */
          if (modifyReportEndData && activeReport) {
            try {
              const currentItems = reportStore.items
              const reportIndex = currentItems.findIndex(item => item.id === activeReport.id)

              if (reportIndex !== -1) {
                const updatedItems = [...currentItems]
                updatedItems[reportIndex] = {
                  ...updatedItems[reportIndex],
                  content: modifyReportEndData, // 使用 End 数据
                }
                reportStore.items = updatedItems
              }
            }
            catch (error) {
              console.error('[ReportPreview] 使用 End 数据更新报告失败:', error)
            }
          }
        },
      )
    }
    catch (error) {
      console.error('[ReportPreview] 修改报告异常:', error)
      god.messageError('修改报告异常')
      setIsModifying(false)

      /** 清理保护状态 */
      currentModifyingReportId.current = ''
    }
  }, [modifyDemand, activeReport, isModifying, getCurrentReportType, reportStore, modifyReportEndData])

  /** 处理保存修改 */
  const handleSaveModification = useCallback(() => {
    if (!activeReport)
      return

    /** 步骤4：点击 Save 按钮，只在控制台输出保存信息 */

    /** 检查是否有 End 数据可以保存 */
    if (modifyReportEndData) {
      try {
        /** 使用 End 数据替换原本报告的内容 */
        const currentItems = reportStore.items
        const reportIndex = currentItems.findIndex(item => item.id === activeReport.id)

        if (reportIndex !== -1) {
          /** 创建新的报告项数组，使用 End 数据更新内容 */
          const updatedItems = [...currentItems]
          updatedItems[reportIndex] = {
            ...updatedItems[reportIndex],
            content: modifyReportEndData, // 使用最终版本数据
          }

          /** 更新 reportStore */
          reportStore.items = updatedItems
        }
      }
      catch (error) {
        console.error('[ReportPreview] 保存最终报告失败:', error)
        return
      }
    }
    else {
      /** 没有 End 数据，保存当前显示的内容 */
    }

    /** 标记报告为已保存（一旦保存就不能再次修改） */
    setReportSaveStates(prev => new Map(prev.set(activeReport.id, 'saved')))

    /** 退出编辑模式并清理状态 */
    setIsEditMode(false)
    setModifyDemand('')
    setModifyReportEndData('')
    setStreamingModifiedContent('')
  }, [activeReport, modifyReportEndData, reportStore])

  /** 处理编辑按钮点击 */
  const handleEditClick = useCallback(() => {
    setIsEditMode(true)
    setModifyDemand('')
  }, [])

  useEffect(
    () => {
      const id = curReports.at(-1)?.id || ''
      setActiveReportId(id)
    },
    [curReports],
  )

  /** 报告切换时重置编辑状态 - 使用 ref 避免循环依赖 */
  const previousActiveReportId = useRef<string>('')

  useEffect(() => {
    /** 只有当 activeReportId 真正发生变化时才执行重置逻辑 */
    if (activeReportId && activeReportId !== previousActiveReportId.current) {
      /** 只有在不是修改操作过程中才执行重置 */
      if (!isModifying) {
        /** 自动关闭当前的修改报告模式 */
        setIsEditMode(false)
        setModifyDemand('')
        setModifyReportEndData('')
        setStreamingModifiedContent('')
      }

      /** 更新 ref */
      previousActiveReportId.current = activeReportId
    }
  }, [activeReportId, isModifying])

  useEffect(
    () => {
      eventBus.on(
        DistributionEvent.SetActiveTab,
        (tabId: string) => {
          setActiveReportId(tabId)
        },
      )

      eventBus.on(
        DistributionEvent.RefreshReport,
        refresh,
      )

      function refresh() {
        const id = curReports.at(0)?.id || ''
        setActiveReportId(id)

        setTimeout(() => {
          const id = curReports.at(-1)?.id || ''
          setActiveReportId(id)
        }, 10)
      }

      return () => {
        eventBus.off(DistributionEvent.SetActiveTab)
        eventBus.off(DistributionEvent.SetActiveTab, r)
      }
    },
    [curReports, r],
  )

  return (
    <div className="h-full flex flex-col">
      {/* 顶部标题栏 */}
      <div className="flex items-center justify-between border-b border-gray-100 bg-white px-4 py-3 dark:bg-slate-700/30">
        <div className="flex items-center gap-3">
          <h3 className="text-lg text-gray-800 font-semibold dark:text-gray-200">
            {activeReport?.title || 'Report'}
          </h3>
        </div>
        <div className="flex items-center gap-2">
          {/* Edit 按钮 - 只在可编辑时显示 */}
          {canEditReport && !isEditMode && (
            <div
              className="cursor-pointer text-sm text-blue-500 transition-all duration-300 hover:text-blue-600"
              onClick={ handleEditClick }
            >
              Edit
            </div>
          )}

          {/* Save 按钮 - 只在编辑模式时显示 */}
          {isEditMode && (
            <div
              className="cursor-pointer text-sm text-blue-500 transition-all duration-300 hover:text-blue-600"
              onClick={ handleSaveModification }
            >
              Save
            </div>
          )}

          <div
            className="cursor-pointer text-sm text-blue-500 transition-all duration-300 hover:text-blue-600"
            onClick={ handleDownloadCurrent }
            style={ {
              ...vShow(!curStepState?.isProcessing || false),
            } }
          >
            Download
          </div>
          <Button
            rightIcon={ <PanelRight size={ 22 } strokeWidth={ 1.5 } /> }
            rounded="full"
            designStyle="ghost"
            onClick={ onClose }
          />
        </div>
      </div>

      {/* 报告内容区域 */}
      <div className="relative flex-1 overflow-auto">
        {activeReport
          ? (
              <ReportContent
                item={ activeReport as ReportContentItem }
                mdToCodePreview={ mdToCodePreview }
                reportStore={ reportStore }
                stateStore={ stateStore }
              />
            )
          : (
              <div className="h-full flex items-center justify-center">
                <div className="text-center text-gray-400">
                  <FileText className="mx-auto mb-2 h-8 w-8" />
                  <p>No report selected</p>
                </div>
              </div>
            )}

        {/* 悬浮输入框 - 在编辑模式时显示 */}
        {isEditMode && (
          <div className="absolute bottom-0 left-0 right-0 border-t border-gray-200 bg-white p-4 shadow-lg">
            <div className="flex items-center gap-3">
              <input
                type="text"
                value={ modifyDemand }
                onChange={ e => setModifyDemand(e.target.value) }
                placeholder="Ask what's on your mind"
                disabled={ isModifying }
                className="flex-1 border border-gray-300 rounded-lg px-4 py-2 disabled:cursor-not-allowed focus:border-transparent disabled:bg-gray-100 disabled:opacity-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
                style={ {
                  background: isModifying
                    ? '#f3f4f6'
                    : 'linear-gradient(135deg, rgba(219, 158, 255, 0.1) 0%, rgba(181, 170, 255, 0.1) 50%, rgba(123, 189, 255, 0.1) 100%)',
                  borderImage: 'linear-gradient(135deg, #DB9EFF, #B5AAFF, #7BBDFF) 1',
                } }
                onKeyDown={ (e) => {
                  if (e.key === 'Enter' && !e.shiftKey && !isModifying) {
                    e.preventDefault()
                    handleSendModification()
                  }
                } }
              />
              <button
                onClick={ handleSendModification }
                disabled={ !modifyDemand.trim() || isModifying }
                className="flex items-center gap-2 rounded-lg bg-blue-500 px-4 py-2 text-white transition-all duration-300 disabled:cursor-not-allowed hover:bg-blue-600 disabled:opacity-50"
              >
                {isModifying
                  ? (
                      <>
                        <div className="h-4 w-4 animate-spin border-2 border-white border-t-transparent rounded-full" />
                        Sending...
                      </>
                    )
                  : (
                      <>
                        <Send size={ 16 } />
                        Send
                      </>
                    )}
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  )
},
)

export const ReportPreview = memo<ReportPreviewProps>((
  {
    className,
    isOpen = false,
    onClose,
    collapsedWidth = 48,
    expandedWidth = getWinWidth() / 2,
    reportStore,
    taskStore,
    stepState,
    mdToCodePreview,
    stateStore,
  },
) => {
  const { items } = reportStore.use()
  const { currentStep } = taskStore.use()

  const curReports = useMemo(() => items.filter(item => currentStep === item.meta.step), [items, currentStep])
  const len = curReports.length

  /** 处理下载全部报告 */
  const handleDownload = useCallback(() => {
    for (const item of curReports || []) {
      if (item.type === 'code') {
        downloadByData(item.content, `${item.title || 'report'}.html`)
      }
      else if (['markdown', 'text'].includes(item.type)) {
        downloadByData(item.content, `${item.title || 'report'}.md`)
      }
      else {
        god.messageInfo('can not download this type of report')
      }
    }
  }, [curReports])

  // const Wrapper: React.FC<{ children: React.ReactNode }> = (props) => <motion.div
  //   animate={ {
  //     x: 0,
  //     width: isOpen
  //       ? expandedWidth
  //       : collapsedWidth,
  //   } }
  //   transition={ { duration: 0.3 } }
  //   className={ cn(
  //     'overflow-hidden rounded-2xl bg-white dark:bg-slate-800',
  //     className,
  //   ) }
  // >
  //   { props.children }
  // </motion.div>

  const reportSnap = reportStore.use()
  const taskSnap = taskStore.use()
  const show = isOpen && reportSnap.items.filter(item => taskSnap.currentStep === item.meta.step).length > 0

  return (
    <AnimateShow
      show={ show }
      className={ cn(
        'overflow-hidden rounded-2xl bg-white dark:bg-slate-800',
        className,
      ) }
      style={ {
        width: expandedWidth,
      } }
      variants={ {
        initial: {
          opacity: 0,
          width: collapsedWidth,
        },
        animate: {
          opacity: 1,
          width: expandedWidth,
        },
        exit: {
          opacity: 0,
          width: collapsedWidth,
        },
      } }
    >
      {
        isOpen
          ? (
              len > 0
                ? (
                    <ReportDisplay
                      onClose={ onClose }
                      handleDownload={ handleDownload }
                      reportStore={ reportStore }
                      taskStore={ taskStore }
                      stepState={ stepState }
                      mdToCodePreview={ mdToCodePreview }
                      stateStore={ stateStore }
                    />
                  )
                : (
                    <div className="text-center text-sm text-gray-500 dark:text-gray-400">
                      <div className="w-full flex justify-start p-2">
                        <Button
                          onClick={ onClose }
                          rounded="full"
                          designStyle="ghost"
                          rightIcon={ <PanelRight size={ 22 } strokeWidth={ 1.5 } /> }
                        />
                      </div>

                      <FileText className="mx-auto mb-2 h-8 w-8 text-gray-300 dark:text-slate-600" />
                      <p>Empty</p>
                    </div>
                  )
            )
          : (
              <div className="flex justify-center p-2">
                <Button
                  onClick={ onClose }
                  rounded="full"
                  designStyle="ghost"
                  leftIcon={ <PanelLeft
                    size={ 22 }
                    strokeWidth={ 1.5 }
                  /> }
                />
              </div>
            )
      }
    </AnimateShow>
  )
})

ReportPreview.displayName = 'ReportPreview'

export type ReportPreviewProps = {
  /**
   * 自定义类名
   */
  className?: string
  /**
   * 是否打开
   * @default false
   */
  isOpen?: boolean
  /**
   * 关闭回调
   */
  onClose?: () => void
  /**
   * 展开时的宽度
   * @default 450
   */
  expandedWidth?: number
  /**
   * 收起时的宽度
   * @default 72
   */
  collapsedWidth?: number

  reportStore: ReportStoreType
  taskStore: TaskStoreType
  stepState: StepStateType
  mdToCodePreview: MdToCodePreviewType
  stateStore: StateStoreType
}
