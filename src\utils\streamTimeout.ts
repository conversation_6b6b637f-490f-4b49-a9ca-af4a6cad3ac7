/**
 * 流式接口超时处理工具
 * 为 app/market/stream/execute-main 接口添加10分钟超时检测
 */

/**
 * 超时配置
 */
export const STREAM_TIMEOUT_CONFIG = {
  /** 超时时间：10分钟 */
  TIMEOUT_DURATION: 10 * 60 * 1000,
  /** 超时提示信息 */
  TIMEOUT_MESSAGE: '请求超时：服务器响应时间超过10分钟，请稍后重试或联系技术支持',
}

/**
 * 创建超时定时器
 * @param onTimeout 超时回调函数
 * @param timeoutDuration 超时时间，默认10分钟
 * @returns 定时器ID，用于清理
 */
export function createStreamTimeout(
  onTimeout: () => void,
  timeoutDuration: number = STREAM_TIMEOUT_CONFIG.TIMEOUT_DURATION,
): NodeJS.Timeout {
  return setTimeout(() => {
    console.error('[StreamTimeout] 流式接口超时，持续时间:', timeoutDuration / 1000, '秒')
    onTimeout()
  }, timeoutDuration)
}

/**
 * 清理超时定时器
 * @param timeoutId 定时器ID
 */
export function clearStreamTimeout(timeoutId: NodeJS.Timeout | null): void {
  if (timeoutId) {
    clearTimeout(timeoutId)
  }
}

/**
 * 显示超时提示
 * @param customMessage 自定义提示信息
 */
export function showTimeoutNotification(customMessage?: string): void {
  const message = customMessage || STREAM_TIMEOUT_CONFIG.TIMEOUT_MESSAGE
  
  // 使用 alert 作为临时解决方案，后续可以替换为更好的通知组件
  alert(message)
  
  // 也在控制台输出错误信息
  console.error('[StreamTimeout] 超时提示:', message)
}

/**
 * fetchEventSource 超时包装器
 * 为使用 @microsoft/fetch-event-source 的调用添加超时处理
 */
export async function fetchEventSourceWithTimeout(
  url: string,
  options: any,
  timeoutDuration: number = STREAM_TIMEOUT_CONFIG.TIMEOUT_DURATION,
): Promise<void> {
  const { fetchEventSource } = await import('@microsoft/fetch-event-source')
  
  let timeoutId: NodeJS.Timeout | null = null
  let isCompleted = false
  
  // 包装原始的回调函数，添加超时处理
  const wrappedOptions = {
    ...options,
    onopen: async (response: Response) => {
      // 创建超时定时器
      timeoutId = createStreamTimeout(() => {
        if (!isCompleted) {
          showTimeoutNotification()
        }
      }, timeoutDuration)
      
      // 调用原始的 onopen 回调
      if (options.onopen) {
        return await options.onopen(response)
      }
    },
    onmessage: (event: any) => {
      // 每次收到消息时重置超时定时器
      if (timeoutId) {
        clearStreamTimeout(timeoutId)
        timeoutId = createStreamTimeout(() => {
          if (!isCompleted) {
            showTimeoutNotification()
          }
        }, timeoutDuration)
      }
      
      // 调用原始的 onmessage 回调
      if (options.onmessage) {
        options.onmessage(event)
      }
    },
    onerror: (error: any) => {
      isCompleted = true
      clearStreamTimeout(timeoutId)
      
      // 调用原始的 onerror 回调
      if (options.onerror) {
        options.onerror(error)
      }
    },
    onclose: () => {
      isCompleted = true
      clearStreamTimeout(timeoutId)
      
      // 调用原始的 onclose 回调
      if (options.onclose) {
        options.onclose()
      }
    },
  }
  
  return fetchEventSource(url, wrappedOptions)
}

/**
 * 原生 fetch 超时包装器
 * 为使用原生 fetch 的流式调用添加超时处理
 */
export async function fetchWithStreamTimeout(
  url: string,
  options: RequestInit,
  timeoutDuration: number = STREAM_TIMEOUT_CONFIG.TIMEOUT_DURATION,
): Promise<Response> {
  let timeoutId: NodeJS.Timeout | null = null
  let isCompleted = false
  
  // 创建超时定时器
  timeoutId = createStreamTimeout(() => {
    if (!isCompleted) {
      showTimeoutNotification()
    }
  }, timeoutDuration)
  
  try {
    const response = await fetch(url, options)
    
    // 如果响应成功，清理初始超时定时器
    // 后续的超时处理将在读取流数据时进行
    clearStreamTimeout(timeoutId)
    
    return response
  } catch (error) {
    isCompleted = true
    clearStreamTimeout(timeoutId)
    throw error
  }
}

/**
 * 流式读取器超时包装器
 * 为流式数据读取添加超时处理
 */
export function createReaderTimeoutHandler(
  reader: ReadableStreamDefaultReader<Uint8Array>,
  timeoutDuration: number = STREAM_TIMEOUT_CONFIG.TIMEOUT_DURATION,
): {
  readWithTimeout: () => Promise<ReadableStreamReadResult<Uint8Array>>
  cleanup: () => void
} {
  let timeoutId: NodeJS.Timeout | null = null
  let isCompleted = false
  
  const readWithTimeout = async (): Promise<ReadableStreamReadResult<Uint8Array>> => {
    // 每次读取前清理之前的定时器并创建新的
    clearStreamTimeout(timeoutId)
    
    if (!isCompleted) {
      timeoutId = createStreamTimeout(() => {
        if (!isCompleted) {
          showTimeoutNotification()
          // 尝试取消读取器
          try {
            reader.cancel('Timeout')
          } catch (e) {
            console.warn('[StreamTimeout] 无法取消读取器:', e)
          }
        }
      }, timeoutDuration)
    }
    
    try {
      const result = await reader.read()
      
      if (result.done) {
        isCompleted = true
        clearStreamTimeout(timeoutId)
      }
      
      return result
    } catch (error) {
      isCompleted = true
      clearStreamTimeout(timeoutId)
      throw error
    }
  }
  
  const cleanup = () => {
    isCompleted = true
    clearStreamTimeout(timeoutId)
  }
  
  return { readWithTimeout, cleanup }
}