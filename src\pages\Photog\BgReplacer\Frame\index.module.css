.frame {
  flex: 1;
  min-width: 0;
  background-color: #fff;
  display: flex;
  padding: var(--ant-padding);
  flex-direction: column;
  gap: var(--ant-margin);
  position: relative;
}
.frame .tips {
  text-align: center;
}
.frame .tips svg {
  margin-right: 12px;
}
.frame .wrapper {
  flex: 1;
  min-height: 0;
  display: flex;
  justify-content: center;
  align-items: center;
}
.frame .wrapper .framer {
  position: relative;
  background-color: #f9fafc;
  max-width: 100%;
  max-height: 100%;
}
.frame .wrapper .framer .frame-img {
  position: relative;
}
.frame .wrapper .framer .frame-img img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.frame .wrapper .framer .frame-img .menu {
  position: absolute;
  bottom: calc(100% + 8px);
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 2px;
  border-radius: 26px;
  cursor: default;
  background-color: #fff;
  box-shadow: 0px 0px 6px 0px #0a15320f, 0px 4px 8px 0px #0a15320f;
}
.frame .wrapper .framer .frame-img .menu .menu-item {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: inherit;
  cursor: pointer;
  opacity: 0.5;
}
.frame .wrapper .framer .frame-img .menu .menu-item:hover {
  opacity: 1;
  background-color: rgba(41, 47, 55, 0.03);
}
.frame .wrapper .framer .frame-img .menu .menu-item svg {
  width: 16px;
  height: 16px;
}
.frame .wrapper .framer .frame-img .menu :global(.ant-upload-wrapper),
.frame .wrapper .framer .frame-img .menu :global(.ant-upload) {
  border-radius: inherit;
}
.frame .wrapper :global(.moveable-control-box) {
  z-index: 1 !important;
}
.mask-editor :global(.ant-modal-header) {
  border: none !important;
  padding: 16px !important;
}
.mask-editor :global(.ant-modal-header) :global(.ant-modal-title) {
  user-select: none;
}
.mask-editor :global(.ant-modal-body) {
  padding: 0 16px !important;
}
.mask-editor :global(.ant-modal-footer) {
  border: none !important;
  padding: 16px !important;
}
