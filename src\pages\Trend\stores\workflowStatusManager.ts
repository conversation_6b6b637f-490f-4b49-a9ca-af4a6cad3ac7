/**
 * 工作流状态管理器
 * 提供基于事件的工作流状态通知机制，替代定时检查
 */

export type WorkflowName
  = | 'insight_report'
    | 'competitor_report'
    | 'planning_scheme'
    | 'visualization'
    | 'modify_report'

export type WorkflowStatus = 'idle' | 'started' | 'completed' | 'error'

export interface WorkflowStatusEvent {
  workflowName: WorkflowName
  status: WorkflowStatus
  timestamp: number
  data?: any
}

export type WorkflowStatusListener = (event: WorkflowStatusEvent) => void

/**
 * 工作流状态管理器类
 */
class WorkflowStatusManager {
  private listeners: Map<WorkflowName, Set<WorkflowStatusListener>> = new Map()
  private globalListeners: Set<WorkflowStatusListener> = new Set()
  private workflowStatus: Map<WorkflowName, WorkflowStatus> = new Map()

  /**
   * 添加特定工作流的状态监听器
   */
  addListener(workflowName: WorkflowName, listener: WorkflowStatusListener): () => void {
    if (!this.listeners.has(workflowName)) {
      this.listeners.set(workflowName, new Set())
    }

    this.listeners.get(workflowName)!.add(listener)

    /** 返回取消监听的函数 */
    return () => {
      this.listeners.get(workflowName)?.delete(listener)
    }
  }

  /**
   * 添加全局状态监听器（监听所有工作流状态变化）
   */
  addGlobalListener(listener: WorkflowStatusListener): () => void {
    this.globalListeners.add(listener)

    return () => {
      this.globalListeners.delete(listener)
    }
  }

  /**
   * 通知工作流状态变化
   */
  notifyStatus(workflowName: WorkflowName, status: WorkflowStatus, data?: any): void {
    const event: WorkflowStatusEvent = {
      workflowName,
      status,
      timestamp: Date.now(),
      data,
    }

    /** 更新内部状态 */
    this.workflowStatus.set(workflowName, status)

    /** 通知特定工作流的监听器 */
    const specificListeners = this.listeners.get(workflowName)
    if (specificListeners) {
      specificListeners.forEach((listener) => {
        try {
          listener(event)
        }
        catch (error) {
          console.error(`工作流状态监听器执行错误:`, error)
        }
      })
    }

    /** 通知全局监听器 */
    this.globalListeners.forEach((listener) => {
      try {
        listener(event)
      }
      catch (error) {
        console.error(`全局工作流状态监听器执行错误:`, error)
      }
    })
  }

  /**
   * 获取当前工作流状态
   */
  getStatus(workflowName: WorkflowName): WorkflowStatus {
    return this.workflowStatus.get(workflowName) || 'idle'
  }

  /**
   * 检查工作流是否已完成
   */
  isCompleted(workflowName: WorkflowName): boolean {
    return this.getStatus(workflowName) === 'completed'
  }

  /**
   * 检查工作流是否正在运行
   */
  isRunning(workflowName: WorkflowName): boolean {
    return this.getStatus(workflowName) === 'started'
  }

  /**
   * 检查是否有任何工作流正在运行
   */
  hasRunningWorkflow(): boolean {
    for (const status of this.workflowStatus.values()) {
      if (status === 'started') {
        return true
      }
    }
    return false
  }

  /**
   * 获取所有工作流状态
   */
  getAllStatus(): Record<WorkflowName, WorkflowStatus> {
    const result = {} as Record<WorkflowName, WorkflowStatus>
    for (const [name, status] of this.workflowStatus.entries()) {
      result[name] = status
    }
    return result
  }

  /**
   * 重置特定工作流状态
   */
  resetStatus(workflowName: WorkflowName): void {
    this.workflowStatus.set(workflowName, 'idle')
    this.notifyStatus(workflowName, 'idle')
  }

  /**
   * 重置所有工作流状态
   */
  resetAllStatus(): void {
    const workflowNames = Array.from(this.workflowStatus.keys())
    workflowNames.forEach((name) => {
      this.resetStatus(name)
    })
  }

  /**
   * 清理所有监听器
   */
  cleanup(): void {
    this.listeners.clear()
    this.globalListeners.clear()
  }
}

/** 创建全局实例 */
export const workflowStatusManager = new WorkflowStatusManager()

/** 开发环境下暴露到全局对象，便于调试 */
if (import.meta.env.DEV) {
  (window as any).workflowStatusManager = workflowStatusManager
}
