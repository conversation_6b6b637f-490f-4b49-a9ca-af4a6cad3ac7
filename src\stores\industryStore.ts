/**
 * 行业数据全局缓存管理器
 * 避免重复调用 /api/system/industries 接口
 */

import { userStore } from '@/store/userStore'

// ==================== 类型定义 ====================

/**
 * 下拉框选项配置
 */
export interface SelectOption {
  value: string | number
  label: string
}

/**
 * 旧版行业数据结构（保留兼容性）
 */
interface IndustryData {
  id: number
  name: string
  code: string
  sort: number
  description: string
  status: boolean
}

/**
 * 新版行业数据结构（xhs-category/tree接口）
 */
interface XhsCategoryTreeData {
  label: string
  parentId: number
  tagId: number
  subTagList?: XhsCategoryTreeData[]
}

/**
 * 旧版行业API响应结构
 */
interface IndustryApiResponse {
  success: boolean
  code: number
  msg: string
  data: IndustryData[]
}

/**
 * 新版行业API响应结构（xhs-category/tree接口）
 */
interface XhsCategoryTreeApiResponse {
  success: boolean
  code: number
  msg: string
  data: XhsCategoryTreeData[]
  timestamp: number
}

/**
 * 扩展的下拉选项配置（支持多级结构）
 */
export interface TreeSelectOption extends SelectOption {
  children?: TreeSelectOption[]
  level?: number
  parentTagId?: number
}

/**
 * 缓存状态（支持多级结构）
 */
interface CacheState {
  data: TreeSelectOption[]
  flatData: SelectOption[] // 扁平化数据，用于兼容现有组件
  loading: boolean
  error: string | null
  lastFetchTime: number
  isInitialized: boolean
}

// ==================== 缓存管理 ====================

/**
 * 缓存配置
 */
const CACHE_CONFIG = {
  /** 缓存有效期：会话期间有效（设为0表示不使用时间缓存） */
  CACHE_DURATION: 0,
  /** 本地存储键名（已禁用本地存储缓存） */
  STORAGE_KEY: 'industries_cache',
  /** 缓存版本（用于缓存失效） */
  CACHE_VERSION: '1.0.0',
  /** 是否启用本地存储缓存（设为false禁用） */
  ENABLE_LOCAL_STORAGE: false,
}

/**
 * 全局缓存状态
 */
let cacheState: CacheState = {
  data: [],
  flatData: [],
  loading: false,
  error: null,
  lastFetchTime: 0,
  isInitialized: false,
}

/**
 * 订阅者列表（用于通知状态变化）
 */
const subscribers: Array<(state: CacheState) => void> = []

// ==================== 本地存储管理 ====================

/**
 * 从本地存储加载缓存数据（已禁用）
 */
function loadFromStorage(): SelectOption[] | null {
  /** 禁用本地存储缓存，每次页面加载都重新请求接口 */
  if (!CACHE_CONFIG.ENABLE_LOCAL_STORAGE) {
    return null
  }

  try {
    const stored = localStorage.getItem(CACHE_CONFIG.STORAGE_KEY)
    if (!stored)
      return null

    const parsed = JSON.parse(stored)

    /** 检查缓存版本和有效期 */
    if (
      parsed.version !== CACHE_CONFIG.CACHE_VERSION
      || Date.now() - parsed.timestamp > CACHE_CONFIG.CACHE_DURATION
    ) {
      localStorage.removeItem(CACHE_CONFIG.STORAGE_KEY)
      return null
    }

    return parsed.data || null
  }
  catch (error) {
    console.error('[IndustryStore] 加载本地缓存失败:', error)
    localStorage.removeItem(CACHE_CONFIG.STORAGE_KEY)
    return null
  }
}

/**
 * 保存数据到本地存储（已禁用）
 */
function saveToStorage(data: SelectOption[]): void {
  /** 禁用本地存储缓存，不保存数据到本地存储 */
  if (!CACHE_CONFIG.ENABLE_LOCAL_STORAGE) {
    console.warn('[IndustryStore] 本地存储缓存已禁用，跳过保存')
    return
  }

  try {
    const cacheData = {
      version: CACHE_CONFIG.CACHE_VERSION,
      timestamp: Date.now(),
      data,
    }
    localStorage.setItem(CACHE_CONFIG.STORAGE_KEY, JSON.stringify(cacheData))
  }
  catch (error) {
    console.error('[IndustryStore] 保存本地缓存失败:', error)
  }
}

// ==================== 状态管理 ====================

/**
 * 更新缓存状态并通知订阅者
 */
function updateCacheState(updates: Partial<CacheState>): void {
  cacheState = { ...cacheState, ...updates }

  /** 通知所有订阅者 */
  subscribers.forEach((callback) => {
    try {
      callback(cacheState)
    }
    catch (error) {
      console.error('[IndustryStore] 订阅者回调执行失败:', error)
    }
  })
}

/**
 * 订阅状态变化
 */
export function subscribeToIndustryStore(callback: (state: CacheState) => void): () => void {
  subscribers.push(callback)

  /** 立即调用一次回调，提供当前状态 */
  callback(cacheState)

  /** 返回取消订阅函数 */
  return () => {
    const index = subscribers.indexOf(callback)
    if (index > -1) {
      subscribers.splice(index, 1)
    }
  }
}

// ==================== 数据转换工具 ====================

/**
 * 将多级树形数据转换为TreeSelectOption格式
 */
function convertToTreeSelectOptions(data: XhsCategoryTreeData[], level = 0, parentTagId?: number): TreeSelectOption[] {
  return data.map((item) => {
    const option: TreeSelectOption = {
      value: item.tagId, // 确保tagId是数字类型
      label: item.label,
      level,
      parentTagId,
    }

    if (item.subTagList && item.subTagList.length > 0) {
      option.children = convertToTreeSelectOptions(item.subTagList, level + 1, item.tagId)
    }

    return option
  })
}

/**
 * 将树形数据扁平化为SelectOption数组（用于兼容现有组件）
 */
function flattenTreeSelectOptions(treeOptions: TreeSelectOption[]): SelectOption[] {
  const result: SelectOption[] = []

  function traverse(options: TreeSelectOption[], prefix = '') {
    options.forEach((option) => {
      /** 添加层级前缀以区分不同级别的选项 */
      const displayLabel = prefix
        ? `${prefix} > ${option.label}`
        : option.label
      result.push({
        value: option.value,
        label: displayLabel,
      })

      if (option.children && option.children.length > 0) {
        traverse(option.children, displayLabel)
      }
    })
  }

  traverse(treeOptions)
  return result
}

// ==================== API调用 ====================

/**
 * 从新版API获取行业数据（xhs-category/tree接口）
 */
async function fetchFromNewTreeAPI(): Promise<{ treeData: TreeSelectOption[], flatData: SelectOption[] }> {
  const token = userStore.token

  if (!token) {
    throw new Error('用户未登录，无法获取行业数据')
  }

  const apiUrl = import.meta.env.VITE_API_BASE_URL || '/api'
  const response = await fetch(`${apiUrl}/app/xhs-category/tree`, {
    method: 'GET',
    headers: {
      Authorization: `Bearer ${token}`,
    },
  })

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`)
  }

  const result: XhsCategoryTreeApiResponse = await response.json()

  if (!result.success || !result.data) {
    throw new Error(result.msg || '获取行业数据失败')
  }

  /** 转换为TreeSelectOption格式 */
  const treeData = convertToTreeSelectOptions(result.data)

  /** 生成扁平化数据用于兼容现有组件 */
  const flatData = flattenTreeSelectOptions(treeData)

  console.warn('[IndustryStore] 成功获取新版行业数据:', {
    treeCount: treeData.length,
    flatCount: flatData.length,
  })

  return { treeData, flatData }
}

/**
 * 从旧版API获取行业数据（保留作为降级方案）
 */
async function fetchFromOldAPI(): Promise<{ treeData: TreeSelectOption[], flatData: SelectOption[] }> {
  const token = userStore.token

  if (!token) {
    throw new Error('用户未登录，无法获取行业数据')
  }

  const apiUrl = import.meta.env.VITE_API_BASE_URL || '/api'
  const response = await fetch(`${apiUrl}/system/industries`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`,
    },
  })

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`)
  }

  const result: IndustryApiResponse = await response.json()

  if (!result.success || !result.data) {
    throw new Error(result.msg || '获取行业数据失败')
  }

  /** 过滤有效行业，按sort排序，转换为SelectOption格式 */
  const flatData = result.data
    .filter(industry => industry.status === true)
    .sort((a, b) => a.sort - b.sort)
    .map(industry => ({
      value: industry.code, // 使用code作为值
      label: industry.name, // 使用name作为显示文本
    }))

  /** 将扁平数据转换为树形结构（单级） */
  const treeData: TreeSelectOption[] = flatData.map(item => ({
    ...item,
    level: 0,
  }))

  return { treeData, flatData }
}

/**
 * 从API获取行业数据（主要接口，优先使用新接口）
 */
async function fetchFromAPI(): Promise<{ treeData: TreeSelectOption[], flatData: SelectOption[] }> {
  try {
    /** 优先使用新接口 */
    console.warn('[IndustryStore] 尝试使用新接口 /app/xhs-category/tree')
    return await fetchFromNewTreeAPI()
  }
  catch (error) {
    console.warn('[IndustryStore] 新接口失败，尝试使用旧接口:', error)
    /** 降级到旧接口 */
    try {
      return await fetchFromOldAPI()
    }
    catch (fallbackError) {
      console.error('[IndustryStore] 所有接口都失败了:', { newError: error, oldError: fallbackError })
      throw fallbackError
    }
  }
}

// ==================== 公共API ====================

/**
 * 获取行业数据（主要接口）- 返回扁平化数据用于兼容现有组件
 * 优先使用缓存，缓存失效时自动刷新
 */
export async function getIndustries(forceRefresh = false): Promise<SelectOption[]> {
  /** 如果正在加载中，等待当前请求完成 */
  if (cacheState.loading) {
    return new Promise((resolve, reject) => {
      const unsubscribe = subscribeToIndustryStore((state) => {
        if (!state.loading) {
          unsubscribe()
          if (state.error) {
            reject(new Error(state.error))
          }
          else {
            resolve(state.flatData) // 返回扁平化数据
          }
        }
      })
    })
  }

  /** 检查是否需要刷新缓存 - 修改为会话期间缓存策略 */
  const now = Date.now()

  /** 会话期间缓存：如果已初始化且有数据，且不是强制刷新，则使用缓存 */
  const isSessionCacheValid
    = !forceRefresh
      && cacheState.isInitialized
      && cacheState.flatData.length > 0

  if (isSessionCacheValid) {
    console.warn('[IndustryStore] 使用会话缓存数据:', cacheState.flatData.length, '个行业')
    return cacheState.flatData
  }

  /** 不再从本地存储加载，每次页面加载都重新请求接口 */
  console.warn('[IndustryStore] 会话缓存无效或未初始化，准备从接口获取数据')

  /** 从API获取数据 */
  try {
    updateCacheState({ loading: true, error: null })

    const freshData = await fetchFromAPI()

    /** 更新缓存状态 */
    updateCacheState({
      data: freshData.treeData,
      flatData: freshData.flatData,
      loading: false,
      error: null,
      lastFetchTime: now,
      isInitialized: true,
    })

    /** 尝试保存到本地存储（已禁用，仅保存到会话缓存） */
    saveToStorage(freshData.flatData)

    console.warn('[IndustryStore] 成功获取行业数据并保存到会话缓存:', freshData.flatData.length, '个行业')
    return freshData.flatData
  }
  catch (error) {
    const errorMessage = error instanceof Error
      ? error.message
      : '获取行业数据失败'

    updateCacheState({
      loading: false,
      error: errorMessage,
    })

    console.error('[IndustryStore] 获取行业数据失败:', error)

    /** 如果有缓存数据，返回缓存数据作为降级方案 */
    if (cacheState.flatData.length > 0) {
      console.warn('[IndustryStore] 使用缓存数据作为降级方案')
      return cacheState.flatData
    }

    throw error
  }
}

/**
 * 获取树形结构的行业数据（用于多级下拉组件）
 */
export async function getIndustriesTree(forceRefresh = false): Promise<TreeSelectOption[]> {
  /** 先确保数据已加载 */
  await getIndustries(forceRefresh)
  return cacheState.data
}

/**
 * 清除会话缓存（强制下次请求重新获取数据）
 */
export function clearSessionCache(): void {
  console.warn('[IndustryStore] 清除会话缓存')
  updateCacheState({
    data: [],
    flatData: [],
    isInitialized: false,
    lastFetchTime: 0,
    error: null,
  })
}

/**
 * 获取当前缓存状态（用于调试）
 */
export function getCacheStatus(): {
  isInitialized: boolean
  dataCount: number
  lastFetchTime: number
  error: string | null
} {
  return {
    isInitialized: cacheState.isInitialized,
    dataCount: cacheState.flatData.length,
    lastFetchTime: cacheState.lastFetchTime,
    error: cacheState.error,
  }
}

/**
 * 获取当前缓存状态（同步）
 */
export function getIndustriesState(): CacheState {
  return { ...cacheState }
}

/**
 * 清除缓存
 */
export function clearIndustriesCache(): void {
  localStorage.removeItem(CACHE_CONFIG.STORAGE_KEY)
  updateCacheState({
    data: [],
    loading: false,
    error: null,
    lastFetchTime: 0,
    isInitialized: false,
  })
  console.warn('[IndustryStore] 缓存已清除')
}

/**
 * 预加载行业数据（可选，用于应用启动时预加载）
 */
export function preloadIndustries(): void {
  /** 异步预加载，不阻塞主流程 */
  getIndustries().catch((error) => {
    console.warn('[IndustryStore] 预加载行业数据失败:', error)
  })
}
