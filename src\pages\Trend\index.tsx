/**
 * Trend 趋势分析页面主组件
 *
 * 功能：
 * - 提供趋势分析的完整工作流
 * - 支持表单模式和聊天模式
 * - 集成 AI 代理进行智能分析
 * - 支持历史记录管理
 */

import type { AIAgent } from '../ChatV2/components/TopBar'
import type { TrendStep1Params } from '@/api/TrendApi'
import { motion } from 'framer-motion'
import { ArrowLeft } from 'lucide-react'
import { Suspense, useEffect, useState } from 'react'
import { TrendApi } from '@/api/TrendApi'
import { GlowBorder } from '@/components/Card'
import { Loading } from '@/components/Loading'

import { ExtGuide } from '../ChatV2/components/ExtGuide'
import TopBar from '../ChatV2/components/TopBar'
import { ChatEventBus } from '../ChatV2/constants'
import TrendSelectionPage from '../TrendSelection/components/TrendSelectionPage'
import AIChat from './components/AIChat'
import { ChatPage } from './components/ChatPage'
import { ImagePreviewCard } from './components/ImagePreviewCard'
import { ReportPreview } from './components/ReportComponents/ReportPreview'
import TrendWelcome from './components/TrendWelcome'
import { trendAg, trendHi } from './stores'
import { handleFormSubmit } from './stores/chatActions'
import { clearFormCacheData } from './stores'
import { handleTrendAgentError } from './stores/error'

/** 设置为模拟数据模式 */
TrendApi.isMock = true

const {
  mdToCodePreview,
  messageStore,
  reportStore,
  resetTrendStore,
  stateStore,
  stepState,
  taskStore,
} = trendAg

const {
  mdToCodePreview: hiMdToCodePreview,
  messageStore: hiMessageStore,
  reportStore: hiReportStore,
  resetTrendStore: resetHiStore,
  stateStore: hiStateStore,
  stepState: hiStepState,
  taskStore: hiTaskStore,
} = trendHi

function App() {
  const [viewMode, setViewMode] = useState<'agent' | 'history'>('agent')
  const { mode, isProcessing, isReportOpen, showTrendSelection, planningReportContent, taskInstanceId, originalWorkData, originalWorkLoading, originalWorkProgress } = stateStore.useAndDispose()
  const { isLoading } = hiStateStore.useAndDispose()

  /** 前置流程状态管理 */
  const [showWelcome, setShowWelcome] = useState(true)
  const [isGenerating, setIsGenerating] = useState(false)
  const [message, setMessage] = useState('')
  const [uploadedImage, setUploadedImage] = useState<string | null>(null)
  const [urlInputValue, setUrlInputValue] = useState('')
  const [selectedButton, setSelectedButton] = useState<string | null>(null)
  const [showAIChat, setShowAIChat] = useState(false) // AI 聊天界面显示状态
  const [aiChatSessionData, setAIChatSessionData] = useState<any>(null) // AI 聊天会话数据
  const [imagePreviewData, setImagePreviewData] = useState<any>(null) // 图片预览数据
  const [showImagePreview, setShowImagePreview] = useState(false) // 图片预览状态
  const [userClosedImagePreview, setUserClosedImagePreview] = useState(false) // 用户是否主动关闭了图片预览
  const [formData, setFormData] = useState<any>(null) // 表单数据状态

  // TopBar 状态管理
  const [isTopBarDropdownExpanded, setIsTopBarDropdownExpanded] = useState(false)

  // data_report工作流执行状态
  const [isDataReportStarted, setIsDataReportStarted] = useState(false)

  /** 监听data_report工作流执行状态 */
  useEffect(() => {
    // 检查localStorage中是否有dataReportParams，表示data_report已经开始执行
    const checkDataReportStatus = () => {
      const dataReportParams = localStorage.getItem('dataReportParams')
      if (dataReportParams) {
        setIsDataReportStarted(true)
      }
    }

    // 初始检查
    checkDataReportStatus()

    // 监听localStorage变化
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'dataReportParams' && e.newValue) {
        setIsDataReportStarted(true)
      }
    }

    window.addEventListener('storage', handleStorageChange)
    return () => window.removeEventListener('storage', handleStorageChange)
  }, [])

  /** 监听 showTrendSelection 状态变化 */
  useEffect(() => {
    /** 当有数据时强制刷新 */
    if (showTrendSelection && planningReportContent) {
      // TrendSelection should be displayed
    }
  }, [showTrendSelection, planningReportContent, taskInstanceId])

  /** 监听来自 ChatWorkflow 的预览事件 */
  useEffect(() => {
    const handleShowPreview = (event?: any) => {
      /** 如果是用户主动点击（通过事件参数判断），重置关闭标志 */
      if (event && event.isUserAction) {
        setUserClosedImagePreview(false)
      }

      /** 如果用户主动关闭过且不是用户主动点击，不自动显示 */
      if (!userClosedImagePreview || (event && event.isUserAction)) {
        setShowImagePreview(true)
        /** 当显示图片预览时，保持报告的状态，这样关闭图片后可以回到报告 */
      }
    }

    const handleHidePreview = () => {
      setShowImagePreview(false)
    }

    const handleUpdateImagePreview = (data: any) => {
      if (data === null) {
        setImagePreviewData(null)
      }
      else {
        setImagePreviewData((prev: any) => ({
          ...prev,
          ...data,
        }))
      }
    }

    /** 注册事件监听器 */
    ChatEventBus.on('showImagePreview', handleShowPreview)
    ChatEventBus.on('hideImagePreview', handleHidePreview)
    ChatEventBus.on('updateImagePreview', handleUpdateImagePreview)

    return () => {
      ChatEventBus.off('showImagePreview', handleShowPreview)
      ChatEventBus.off('hideImagePreview', handleHidePreview)
      ChatEventBus.off('updateImagePreview', handleUpdateImagePreview)
    }
  }, [])

  /** 监听状态变化 */
  useEffect(() => {
    // State change monitoring
  }, [showImagePreview, isReportOpen])

  /** 固定的 4 个 Bot - 与 ChatV2 保持一致 */
  const fixedAgents: AIAgent[] = [
    { id: 'research-analyst', name: 'Research Analyst', icon: new URL('@/assets/image/home/<USER>', import.meta.url).href },
    { id: 'brand-strategist', name: 'Brand Strategist', icon: new URL('@/assets/image/home/<USER>', import.meta.url).href },
    { id: 'creative-director', name: 'Creative Director', icon: new URL('@/assets/image/home/<USER>', import.meta.url).href },
    { id: 'operations-manager', name: 'Operations Manager', icon: new URL('@/assets/image/home/<USER>', import.meta.url).href },
  ]

  // TopBar Agent 点击事件（暂时无操作）
  const handleTopBarAgentClick = (_agent: AIAgent) => {
    /** 暂时不执行任何操作，保持 4 个 bot 固定显示 */
  }

  /** 开始 AI 分析的处理函数 */
  const handleStartAIAnalysis = async (formData: TrendStep1Params) => {
    try {
      await handleFormSubmit(formData, trendAg)
    }
    catch (error) {
      handleTrendAgentError(true)
    }
  }

  function handleBackToAgentClick() {
    setViewMode('agent')
  }

  /** 前置流程事件处理 */
  function handleImageUpload(fileOrUrl: File | string) {
    /** 如果是字符串（URL），直接设置 */
    if (typeof fileOrUrl === 'string') {
      setUploadedImage(fileOrUrl)
    }
    /** 如果是 File 对象，读取为 base64 */
    else if (fileOrUrl instanceof File) {
      const reader = new FileReader()
      reader.onload = (e) => {
        setUploadedImage(e.target?.result as string)
      }
      reader.readAsDataURL(fileOrUrl)
    }
  }

  function handleImageRemove() {
    setUploadedImage(null)
  }

  async function handleGenerate() {
    if (!message.trim())
      return

    setIsGenerating(true)

    /**
     * 不要在这里立即切换界面，等待 create-session 的响应
     * 响应会通过 onSessionCreated 回调传递
     */
  }

  /** 处理会话创建的回调 */
  const handleSessionCreated = (sessionData: any) => {
    /** 检查是否需要直接执行 text_link_disassemble */
    if (sessionData.needTextLinkDisassemble) {

      /** 直接切换到 ChatPage 的 text_link_disassemble 模式 */
      setShowWelcome(false)
      setShowAIChat(false)
      /** 设置为聊天模式，直接进入 ChatPage */
      stateStore.mode = 'chat'

      /** 设置 ChatPage 的初始状态为 text_link_disassemble 流程模式 */
      stateStore.chatV2FlowMode = 'text_link_disassemble'
      stateStore.userDescription = message // 保存用户描述
      stateStore.uploadedImage = uploadedImage // 保存上传的图片
      stateStore.taskInstanceId = sessionData.taskInstanceId
      stateStore.detectedIntent = sessionData.detectedIntent

      /** 清空之前的数据 */
      // @ts-ignore
      stateStore.sseStreamMessages = []
      // @ts-ignore
      stateStore.collectedFormData = null
      setIsGenerating(false)
    }
    else if (sessionData.nextStep === 'code_0' || sessionData.detectedIntent === 'code_0') {
      /** 保存会话数据 */
      setAIChatSessionData(sessionData)
      /** 显示 AI 聊天界面 */
      setShowAIChat(true)
      setShowWelcome(false)
      setIsGenerating(false)
    }
    else {
      /** 继续原有流程 */
      try {
        /** 立即切换到 ChatPage 并显示 Thinking */
        setShowWelcome(false)
        setShowAIChat(false)

        /** 设置为聊天模式，直接进入 ChatPage */
        stateStore.mode = 'chat'

        /** 设置 ChatPage 的初始状态为 ChatV2 流程模式 */
        stateStore.chatV2FlowMode = 'thinking' // 新增状态
        stateStore.userDescription = message // 保存用户描述
        stateStore.uploadedImage = uploadedImage // 保存上传的图片

        /** 清空之前的数据 */
        // @ts-ignore
        stateStore.sseStreamMessages = []
        // @ts-ignore
        stateStore.collectedFormData = null
      }
      catch (error) {
        handleTrendAgentError(true)
      }
      finally {
        setIsGenerating(false)
      }
    }
  }

  // AI 聊天退出处理（当 nextStep 不再是 code_0/code_1 时）
  const handleExitAIChat = (data?: any) => {
    /** 退出 AI 聊天 */
    setShowAIChat(false)

    /** 如果有传递数据，说明需要切换到 text_link_disassemble 工作流 */
    if (data && data.detectedIntent && data.detectedIntent !== 'code_0') {

      try {
        /** 立即切换到 ChatPage */
        setShowWelcome(false)

        /** 设置为聊天模式，直接进入 ChatPage */
        stateStore.mode = 'chat'

        /** 设置 ChatPage 的初始状态为 text_link_disassemble 流程模式 */
        stateStore.chatV2FlowMode = 'text_link_disassemble'
        stateStore.userDescription = data.userMessage || message // 保存用户描述
        stateStore.uploadedImage = data.uploadedImage || uploadedImage // 保存上传的图片
        stateStore.taskInstanceId = data.taskInstanceId
        stateStore.detectedIntent = data.detectedIntent

        /** 同时保存到 localStorage 作为备份 */
        localStorage.setItem('taskInstanceId', data.taskInstanceId)
        localStorage.setItem('userMessage', data.userMessage || message)
        localStorage.setItem('detectedIntent', data.detectedIntent)

        /** 清空之前的数据 */
        // @ts-ignore
        stateStore.sseStreamMessages = []
        // @ts-ignore
        stateStore.collectedFormData = null
      }
      catch (error) {
        handleTrendAgentError(true)
      }
    }
    else {
      /** 原有逻辑：继续主流程 */
      try {
        /** 立即切换到 ChatPage 并显示 Thinking */
        setShowWelcome(false)

        /** 设置为聊天模式，直接进入 ChatPage */
        stateStore.mode = 'chat'

        /** 设置 ChatPage 的初始状态为 ChatV2 流程模式 */
        stateStore.chatV2FlowMode = 'thinking' // 新增状态
        stateStore.userDescription = message // 保存用户描述
        stateStore.uploadedImage = uploadedImage // 保存上传的图片

        /** 清空之前的数据 */
        // @ts-ignore
        stateStore.sseStreamMessages = []
        // @ts-ignore
        stateStore.collectedFormData = null
      }
      catch (error) {
        handleTrendAgentError(true)
      }
    }
  }

  return (
    <>
      {/* TopBar 只在前置页面显示 */}
      {showWelcome && viewMode === 'agent' && !showAIChat && (
        <TopBar
          agents={ fixedAgents }
          showAgents={ isDataReportStarted } // 只在data_report工作流开始后显示AI代理图标
          onAgentClick={ handleTopBarAgentClick }
          dropdownExpanded={ isTopBarDropdownExpanded }
          onDropdownToggle={ setIsTopBarDropdownExpanded }
          onNewProject={ () => {
            /** 清理表单缓存数据，防止异常数据回填 */
            clearFormCacheData()

            /** 重置到 TrendWelcome 初始状态 */
            setShowWelcome(true)
            setShowAIChat(false)
            setIsGenerating(false)
            setMessage('')
            setUploadedImage(null)
            setUrlInputValue('')
            setSelectedButton(null)
            setImagePreviewData(null)
            setShowImagePreview(false)
            setUserClosedImagePreview(false)
            setFormData(null)
            setAIChatSessionData(null)
            setViewMode('agent')
            setIsDataReportStarted(false) // 重置data_report状态

            /** 重置 store 状态 */
            stateStore.mode = 'form'
            stateStore.isReportOpen = false
            stateStore.showTrendSelection = false
            stateStore.chatV2FlowMode = null
            stateStore.userDescription = ''
            stateStore.uploadedImage = null
            stateStore.taskInstanceId = ''
            stateStore.detectedIntent = ''
            // @ts-ignore
            stateStore.sseStreamMessages = []
            // @ts-ignore
            stateStore.collectedFormData = null

            /** 清理 localStorage */
            localStorage.removeItem('taskInstanceId')
            localStorage.removeItem('detectedIntent')
            localStorage.removeItem('userMessage')
            localStorage.removeItem('uploadedImage')
            localStorage.removeItem('confirmed_taskInstanceId')

            console.log('🔄 Reset to TrendWelcome initial state')
          } }
          containerClassName="fixed left-0 right-0 top-0 z-50"
          hideAgentsInWelcome
        />
      )}

      {/* AI 聊天界面 */}
      {showAIChat ? (
        <motion.div
          key="ai-chat"
          initial={ { opacity: 0 } }
          animate={ { opacity: 1 } }
          exit={ { opacity: 0 } }
          transition={ { duration: 0.3 } }
          className="h-full w-full"
        >
          <AIChat
            onExitChat={ handleExitAIChat }
            initialMessage={ message }
            uploadedImage={ uploadedImage }
            initialSessionData={ aiChatSessionData }
            showInitialResponse
          />
        </motion.div>
      ) : showWelcome && viewMode === 'agent'
        ? (
            // Welcome 前置界面
            <motion.div
              key="welcome"
              initial={ { opacity: 0 } }
              animate={ { opacity: 1 } }
              exit={ { opacity: 0 } }
              transition={ { duration: 0.3 } }
              className="h-full w-full"
            >
              <TrendWelcome
                message={ message }
                onMessageChange={ setMessage }
                uploadedImage={ uploadedImage }
                onImageUpload={ handleImageUpload }
                onImageRemove={ handleImageRemove }
                urlInputValue={ urlInputValue }
                onUrlInputChange={ setUrlInputValue }
                selectedButton={ selectedButton }
                onButtonSelect={ setSelectedButton }
                onGenerate={ handleGenerate }
                isGenerating={ isGenerating }
                inputPlaceholder="Enter a description of your brand and product for trend analysis"
                onSessionCreated={ handleSessionCreated }
                onFormDataCollected={ (data) => {
                  setFormData(data)
                  /** 保存到 store 中，以便 ChatPage 使用 */
                  stateStore.collectedFormData = data
                  /** 如果有 thinking 数据，也保存到 store */
                  if (data.thinking) {
                    // @ts-ignore
                    stateStore.thinkingContent = data.thinking
                  }
                } }
                onSSEMessagesCollected={ (messages) => {
                  /** 保存到 store 中，以便 ChatPage 使用 */
                  // @ts-ignore
                  stateStore.sseStreamMessages = messages
                } }
              />
            </motion.div>
          )
        : viewMode === 'history' || (viewMode === 'agent' && mode === 'chat')
          ? (
              <div className="relative h-full w-full flex gap-4 bg-[#EDF4FF]">
                {/* 左侧主内容区域 - 根据是否有右侧卡片调整宽度 */}
                <motion.div
                  key="chat"
                  initial={ { opacity: 0 } }
                  animate={ {
                    opacity: 1,
                    width: showImagePreview || isReportOpen || showTrendSelection
                      ? 'calc(50% - 8px)'
                      : '100%',
                  } }
                  exit={ { opacity: 0 } }
                  transition={ { type: 'spring', damping: 25, stiffness: 300 } }
                  className="relative overflow-hidden rounded-lg bg-white"
                >
                  { viewMode === 'history' && isProcessing && (
                    <GlowBorder
                      className="group absolute left-2 top-4 z-50 cursor-pointer rounded-full transition-all duration-300 hover:opacity-50"
                      onClick={ handleBackToAgentClick }
                    >
                      <div className="flex items-center gap-2 bg-white px-3 py-1.5 text-sm font-medium">
                        <ArrowLeft
                          size={ 16 }
                          strokeWidth={ 1.5 }
                          className="transition-transform duration-300 group-hover:-translate-x-1"
                        />
                        Back
                      </div>
                    </GlowBorder>
                  ) }

                  <Loading loading={ isLoading } />

                  <ChatPage
                    className="h-full flex-1"
                    style={ {
                      display: viewMode === 'agent'
                        ? 'flex'
                        : 'none',
                    } }
                    taskStore={ taskStore }
                    messageStore={ messageStore }
                    mdToCodePreview={ mdToCodePreview }
                    resetDistributionStore={ resetTrendStore }
                    reportStore={ reportStore }
                    stepState={ stepState }
                    stateStore={ stateStore }
                    showTopBar={ viewMode === 'agent' && !showWelcome }
                    topBarAgents={ fixedAgents }
                    onTopBarAgentClick={ handleTopBarAgentClick }
                    topBarDropdownExpanded={ isTopBarDropdownExpanded }
                    onTopBarDropdownToggle={ setIsTopBarDropdownExpanded }
                    onTopBarNewProject={ () => {
                      /** 清理表单缓存数据，防止异常数据回填 */
                      clearFormCacheData()

                      /** 重置到 TrendWelcome 初始状态 */
                      setShowWelcome(true)
                      setShowAIChat(false)
                      setIsGenerating(false)
                      setMessage('')
                      setUploadedImage(null)
                      setUrlInputValue('')
                      setSelectedButton(null)
                      setImagePreviewData(null)
                      setShowImagePreview(false)
                      setUserClosedImagePreview(false)
                      setFormData(null)
                      setAIChatSessionData(null)
                      setViewMode('agent')
                      setIsDataReportStarted(false) // 重置data_report状态

                      /** 重置 store 状态 */
                      stateStore.mode = 'form'
                      stateStore.isReportOpen = false
                      stateStore.showTrendSelection = false
                      stateStore.chatV2FlowMode = null
                      stateStore.userDescription = ''
                      stateStore.uploadedImage = null
                      stateStore.taskInstanceId = ''
                      stateStore.detectedIntent = ''
                      // @ts-ignore
                      stateStore.sseStreamMessages = []
                      // @ts-ignore
                      stateStore.collectedFormData = null

                      /** 清理 localStorage */
                      localStorage.removeItem('taskInstanceId')
                      localStorage.removeItem('detectedIntent')
                      localStorage.removeItem('userMessage')
                      localStorage.removeItem('uploadedImage')
                      localStorage.removeItem('confirmed_taskInstanceId')

                      console.log('🔄 Reset to TrendWelcome initial state from ChatPage')
                    } }
                    onStartAIAnalysis={ handleStartAIAnalysis }
                  />

                  { viewMode === 'history' && <ChatPage
                    className="h-full flex-1"
                    taskStore={ hiTaskStore }
                    messageStore={ hiMessageStore }
                    mdToCodePreview={ hiMdToCodePreview }
                    resetDistributionStore={ resetHiStore }
                    reportStore={ hiReportStore }
                    stepState={ hiStepState }
                    stateStore={ hiStateStore }
                  /> }
                </motion.div>

                {/* 右侧区域 - 只在需要时显示 */}
                {(showImagePreview || isReportOpen || showTrendSelection) && (
                  <div style={ { width: 'calc(50% - 8px)' } } className="relative h-full">
                    {/* 图片预览卡片 - 最高优先级 */}
                    {showImagePreview && (
                      <ImagePreviewCard
                        isOpen={ showImagePreview }
                        onClose={ () => {
                          setShowImagePreview(false)
                          setUserClosedImagePreview(true) // 标记用户主动关闭
                        } }
                        data={ imagePreviewData
                          || (stateStore.trendSelectionState?.selectionType === 'hotpots' && stateStore.duibiaoDisassemblyData
                            ? stateStore.duibiaoDisassemblyData
                            : originalWorkData) }
                        isLoading={ stateStore.trendSelectionState?.selectionType === 'hotpots'
                          ? stateStore.duibiaoDisassemblyLoading
                          : originalWorkLoading }
                        loadingProgress={ stateStore.trendSelectionState?.selectionType === 'hotpots'
                          ? stateStore.duibiaoDisassemblyProgress
                          : originalWorkProgress }
                      />
                    )}

                    {/* TrendSelection - 当showTrendSelection为true时显示 */}
                    {showTrendSelection && (
                      <div
                        className="h-full overflow-auto rounded-lg bg-white"
                        style={ { display: showImagePreview
                          ? 'none'
                          : 'block' } }
                      >
                        {/* 准备渲染 TrendSelectionPage */}
                          taskInstanceId,
                          localStorageTaskId: localStorage.getItem('confirmed_taskInstanceId'),
                          finalTaskId: taskInstanceId || localStorage.getItem('confirmed_taskInstanceId') || '',
                        })}
                        <Suspense fallback={ <div className="h-full flex items-center justify-center"><Loading /></div> }>
                          <TrendSelectionPage
                            planning_report={ planningReportContent }
                            taskInstanceId={ taskInstanceId || localStorage.getItem('confirmed_taskInstanceId') || localStorage.getItem('taskInstanceId') || '' }
                            className="h-full"
                            storeInstance={ trendAg }
                            onClose={ () => {
                              /** 关闭 TrendSelection 页面，不显示其他内容 */
                              stateStore.showTrendSelection = false
                              stateStore.isReportOpen = false // 同时关闭报告预览
                            } }
                            handlerSelectTopic={ (topicId) => {
                              /** 处理话题选择逻辑 */
                            } }
                          />
                        </Suspense>
                      </div>
                    )}

                    {/* 报告预览 - 当isReportOpen为true且showTrendSelection为false时显示 */}
                    {!showImagePreview && isReportOpen && !showTrendSelection && (
                      <div className="h-full">
                        <ReportPreview
                          isOpen={ isReportOpen && !showTrendSelection }
                          onClose={ () => {
                            stateStore.isReportOpen = false
                            /** 不自动显示 TrendSelection，让用户手动点击 */
                          } }
                          className="h-full"
                          reportStore={ reportStore }
                          taskStore={ taskStore }
                          stepState={ stepState }
                          mdToCodePreview={ mdToCodePreview }
                          stateStore={ stateStore }
                        />
                      </div>
                    )}
                  </div>
                )}
              </div>
            )
          : null }

      <ExtGuide />
    </>
  )
}

export default App
