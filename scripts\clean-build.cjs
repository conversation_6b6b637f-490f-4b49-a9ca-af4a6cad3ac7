const { rmSync, existsSync } = require('node:fs')
const { resolve } = require('node:path')

/**
 * 清理构建缓存和产物
 */
function cleanBuild() {
  const pathsToClean = [
    resolve(__dirname, '../dist'),
    resolve(__dirname, '../node_modules/.vite'),
    resolve(__dirname, '../node_modules/.cache'),
  ]

  pathsToClean.forEach((path) => {
    if (existsSync(path)) {
      console.log(`🧹 Cleaning: ${path}`)
      rmSync(path, { recursive: true, force: true })
    }
  })

  console.log('✅ Build cache cleaned successfully!')
}

cleanBuild()
