# Trend 页面手动控制说明

## 🎯 修改后的执行流程

现在 Trend 页面的四个机器人不再自动连续执行，而是需要用户手动控制每一步：

### 执行步骤：

1. **进入页面** → 显示 "Marketing Team Ready" 卡片
   - 点击 `开始执行 Research Analyst` 按钮启动第一个机器人

2. **Research Analyst 完成后** → 显示结果
   - 点击 `Approve` 按钮确认结果
   - 出现 `执行 Brand Strategist` 按钮
   - 点击后启动第二个机器人

3. **Brand Strategist 完成后** → 显示结果
   - 点击 `Approve` 按钮确认结果
   - 出现 `执行 Creative Director` 按钮
   - 点击后启动第三个机器人

4. **Creative Director 完成后** → 显示结果
   - 点击 `Approve` 按钮确认结果
   - 出现 `执行 Content Manager` 按钮
   - 点击后启动第四个机器人

5. **Content Manager 完成后** → 显示最终结果
   - 点击 `Approve` 按钮完成整个流程

## 📝 修改的文件

### 1. `src/pages/Trend/components/ChatPage.tsx`
- 不再自动执行 `handleFormSubmit`
- 创建初始的 "Marketing Team Ready" 任务

### 2. `src/pages/Trend/stores/chatActions.ts`
- 修改 `handleTaskAction` 函数
- `Approve` 按钮不再自动执行下一步
- 添加了"执行下一个机器人"的按钮
- 用户点击后才执行对应的步骤

## 🔄 恢复自动执行

如果想恢复原来的自动执行模式：

1. 在 `ChatPage.tsx` 中恢复自动执行：
```tsx
// 恢复这段代码
useAsyncEffect(async () => {
  if (!formData) {
    return
  }
  await handleFormSubmit(formData)
  stateStore.formData = null
}, [formData])
```

2. 在 `chatActions.ts` 中恢复自动执行逻辑：
```tsx
// 将 handleTaskAction 改回原来的逻辑
if (action.label === 'Approve') {
  switch (step) {
    case 'step1':
      await handleStep3(store)
      break
    // ... 其他步骤
  }
}
```

## 💡 优势

1. **更好的控制** - 用户可以在每一步查看结果后决定是否继续
2. **便于调试** - 可以单独测试每个机器人的输出
3. **灵活性** - 可以在任何步骤停止，不会浪费资源
4. **用户体验** - 用户更清楚地了解每个步骤的进展

## 🎨 按钮样式说明

- `primary` (蓝色) - 开始执行第一个机器人
- `success` (绿色) - 执行下一个机器人
- `primary` (蓝色) - Approve 确认按钮

## 📌 注意事项

1. 每个机器人执行完成后，必须先点击 `Approve` 才能看到下一步按钮
2. 如果出现错误，流程会停止，不会继续执行
3. 刷新页面会重置整个流程，需要重新开始