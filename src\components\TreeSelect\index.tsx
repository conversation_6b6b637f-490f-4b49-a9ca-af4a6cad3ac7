/**
 * 多级下拉选择组件
 * 支持树形结构的行业数据选择
 */

import type { TreeSelectOption } from '@/stores/industryStore'
import { ChevronDownIcon, ChevronRightIcon } from 'lucide-react'
import React, { useCallback, useEffect, useRef, useState } from 'react'

export interface TreeSelectProps {
  /** 树形数据 */
  options: TreeSelectOption[]
  /** 当前选中的值 */
  value?: string | number
  /** 选择变化回调 */
  onChange?: (value: string | number, option: TreeSelectOption) => void
  /** 占位符 */
  placeholder?: string
  /** 是否禁用 */
  disabled?: boolean
  /** 样式类名 */
  className?: string
  /** 样式 */
  style?: React.CSSProperties
}

export const TreeSelect: React.FC<TreeSelectProps> = ({
  options,
  value,
  onChange,
  placeholder = '请选择行业',
  disabled = false,
  className = '',
  style = {},
}) => {
  const [isOpen, setIsOpen] = useState(false)
  const [expandedKeys, setExpandedKeys] = useState<Set<string | number>>(new Set())
  const [selectedOption, setSelectedOption] = useState<TreeSelectOption | null>(null)
  const dropdownRef = useRef<HTMLDivElement>(null)

  /** 查找选中的选项 */
  const findOptionByValue = useCallback((
    opts: TreeSelectOption[],
    targetValue: string | number,
  ): TreeSelectOption | null => {
    for (const option of opts) {
      if (option.value === targetValue) {
        return option
      }
      if (option.children) {
        const found = findOptionByValue(option.children, targetValue)
        if (found)
          return found
      }
    }
    return null
  }, [])

  /** 初始化选中状态 */
  useEffect(() => {
    if (value !== undefined) {
      const option = findOptionByValue(options, value)
      setSelectedOption(option)
    }
    else {
      setSelectedOption(null)
    }
  }, [value, options, findOptionByValue])

  /** 处理选项点击 */
  const handleOptionClick = useCallback((option: TreeSelectOption) => {
    setSelectedOption(option)
    setIsOpen(false)
    onChange?.(option.value, option)
  }, [onChange])

  /** 处理展开/收起 */
  const handleToggleExpand = useCallback((key: string | number, e: React.MouseEvent) => {
    e.stopPropagation()
    setExpandedKeys((prev) => {
      const newSet = new Set(prev)
      if (newSet.has(key)) {
        newSet.delete(key)
      }
      else {
        newSet.add(key)
      }
      return newSet
    })
  }, [])

  /** 点击外部关闭下拉框 */
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  /** 渲染选项 */
  const renderOption = useCallback((option: TreeSelectOption, level = 0) => {
    const hasChildren = option.children && option.children.length > 0
    const isExpanded = expandedKeys.has(option.value)
    const isSelected = selectedOption?.value === option.value

    return (
      <div key={ option.value }>
        <div
          className={ ` flex cursor-pointer items-center px-3 py-2 transition-colors hover:bg-gray-50 ${isSelected
            ? 'bg-blue-50 text-blue-600'
            : 'text-gray-700'}  ` }
          style={ { paddingLeft: `${12 + level * 20}px` } }
          onClick={ () => handleOptionClick(option) }
        >
          {hasChildren && (
            <button
              className="mr-2 rounded p-0.5 transition-colors hover:bg-gray-200"
              onClick={ e => handleToggleExpand(option.value, e) }
            >
              {isExpanded
                ? (
                    <ChevronDownIcon className="h-4 w-4" />
                  )
                : (
                    <ChevronRightIcon className="h-4 w-4" />
                  )}
            </button>
          )}
          {!hasChildren && <div className="mr-2 w-6" />}
          <span className="flex-1">{option.label}</span>
        </div>

        {hasChildren && isExpanded && (
          <div>
            {option.children!.map(child => renderOption(child, level + 1))}
          </div>
        )}
      </div>
    )
  }, [expandedKeys, selectedOption, handleOptionClick, handleToggleExpand])

  return (
    <div ref={ dropdownRef } className={ `relative ${className}` } style={ style }>
      {/* 选择框 */}
      <div
        className={ ` flex cursor-pointer items-center justify-between border border-gray-300 rounded-md bg-white px-3 py-2 transition-colors ${disabled
          ? 'bg-gray-50 cursor-not-allowed'
          : 'hover:border-gray-400'}  ${isOpen
          ? 'border-blue-500 ring-1 ring-blue-500'
          : ''}  ` }
        onClick={ () => !disabled && setIsOpen(!isOpen) }
      >
        <span className={ selectedOption
          ? 'text-gray-900'
          : 'text-gray-500' }>
          {selectedOption
            ? selectedOption.label
            : placeholder}
        </span>
        <ChevronDownIcon
          className={ `h-4 w-4 text-gray-400 transition-transform ${isOpen
            ? 'rotate-180'
            : ''}` }
        />
      </div>

      {/* 下拉选项 */}
      {isOpen && !disabled && (
        <div className="absolute z-50 mt-1 max-h-60 w-full overflow-y-auto border border-gray-300 rounded-md bg-white shadow-lg">
          {options.length > 0
            ? (
                options.map(option => renderOption(option))
              )
            : (
                <div className="px-3 py-2 text-center text-gray-500">暂无数据</div>
              )}
        </div>
      )}
    </div>
  )
}

export default TreeSelect
