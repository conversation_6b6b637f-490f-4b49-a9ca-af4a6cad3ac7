import AIGenerateIcon from '@/assets/svg/aI-generate.svg'
import { userStore } from '@/store/userStore'
import { request } from '@/utils'
import { fetchWithStreamTimeout, createReaderTimeoutHandler } from '@/utils/streamTimeout'
import { message as antMessage } from 'antd'
import { motion } from 'framer-motion'
import { Send } from 'lucide-react'
import React, { useEffect, useRef, useState } from 'react'
import { TypewriterText } from './TypewriterText'

interface AIChatProps {
  onExitChat?: (data?: any) => void
  initialMessage?: string
  uploadedImage?: string | null
  initialSessionData?: any // 添加初始会话数据
  showInitialResponse?: boolean // 是否显示初始响应
}

interface ChatMessage {
  id: string
  content: string
  role: 'user' | 'assistant'
  showButtons?: boolean
  buttonType?: 'confirm' | 'approve'
  showCountdown?: boolean // 是否显示倒计时
}

export const AIChat: React.FC<AIChatProps> = ({
  onExitChat,
  initialMessage = '',
  uploadedImage,
  initialSessionData,
  showInitialResponse = true,
}) => {
  const [messages, setMessages] = useState<ChatMessage[]>([])
  const [inputValue, setInputValue] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const [sessionData, setSessionData] = useState<any>(initialSessionData || null)
  const [taskInstanceId, setTaskInstanceId] = useState<string>(initialSessionData?.taskInstanceId || '')
  const [isInDialogue, setIsInDialogue] = useState(false) // 标记是否已经进入对话流程
  const [showRedirect, setShowRedirect] = useState(false) // 显示重定向倒计时
  const [countdown, setCountdown] = useState(10) // 倒计时秒数
  const [pendingExitData, setPendingExitData] = useState<any>(null) // 暂存退出数据
  const countdownIntervalRef = useRef<NodeJS.Timeout | null>(null)

  /** 自动滚动到底部 */
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  /** 处理倒计时 */
  useEffect(() => {
    if (showRedirect && countdown > 0) {
      countdownIntervalRef.current = setInterval(() => {
        setCountdown((prev) => {
          if (prev <= 1) {
            /** 倒计时结束，执行退出 */
            if (pendingExitData && onExitChat) {
              onExitChat(pendingExitData)
            }
            setShowRedirect(false)
            return 10
          }
          return prev - 1
        })
      }, 1000)
    }
    else {
      if (countdownIntervalRef.current) {
        clearInterval(countdownIntervalRef.current)
        countdownIntervalRef.current = null
      }
    }

    return () => {
      if (countdownIntervalRef.current) {
        clearInterval(countdownIntervalRef.current)
      }
    }
  }, [showRedirect, countdown, pendingExitData, onExitChat])

  /** 处理停止倒计时 */
  const handleStopRedirect = () => {
    setShowRedirect(false)
    setCountdown(10)
    setPendingExitData(null)
    if (countdownIntervalRef.current) {
      clearInterval(countdownIntervalRef.current)
      countdownIntervalRef.current = null
    }
    /** 继续对话流程 */
    console.log('✅ User stopped redirect, continuing dialogue')
  }

  /** 初始化时显示初始消息并调用 execute-main 获取 AI 回复 */
  useEffect(() => {
    if (initialMessage && showInitialResponse && initialSessionData && taskInstanceId) {
      /** 添加用户的初始消息 */
      const userMessage: ChatMessage = {
        id: 'initial-user',
        content: initialMessage,
        role: 'user',
      }
      setMessages([userMessage])

      /** 立即调用 execute-main 获取 AI 回复 */
      fetchInitialAIResponse()
    }
  }, [])

  /** 获取初始 AI 回复 */
  const fetchInitialAIResponse = async () => {
    if (!taskInstanceId || !initialMessage) {
      console.error('Missing taskInstanceId or initialMessage')
      return
    }

    setIsLoading(true)
    /** 标记已经进入对话流程 */
    setIsInDialogue(true)

    try {
      /** 准备 SSE 请求参数 */
      const sseParams = {
        taskInstanceId,
        platform: 'rednote',
        workflowName: 'dialogue',
        parameters: {
          USER_INPUT: initialMessage,
        },
      }

      console.log('Fetching initial AI response:', sseParams)

      /** 创建 AI 消息占位符 */
      const aiMessageId = 'initial-ai'
      const aiMessage: ChatMessage = {
        id: aiMessageId,
        content: '',
        role: 'assistant',
      }
      setMessages(prev => [...prev, aiMessage])

      /** 调用 SSE 流式接口 */
      const token = userStore.token
      const sseResponse = await fetchWithStreamTimeout(`${import.meta.env.VITE_API_BASE_URL}/app/market/stream/execute-main`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(sseParams),
      })

      if (!sseResponse.ok) {
        throw new Error(`SSE request failed: ${sseResponse.status}`)
      }

      /** 处理 SSE 流式数据 */
      const reader = sseResponse.body?.getReader()
      const decoder = new TextDecoder()

      if (reader) {
        console.log('🚀 开始接收初始 AI 回复的 SSE 流式数据...')
        const { readWithTimeout, cleanup } = createReaderTimeoutHandler(reader)
        let buffer = ''
        let accumulatedContent = ''
        let eventType = ''
        let chunkCount = 0

        try {
          while (true) {
            const { done, value } = await readWithTimeout()
            if (done) {
              console.log('✅ Reader done, total chunks:', chunkCount)
              console.log('📝 Final accumulated content:', accumulatedContent)

              /** 如果有累积的内容但没有正常结束，也要更新消息 */
              if (accumulatedContent) {
                setMessages(prev => prev.map(msg =>
                  msg.id === aiMessageId
                    ? { ...msg, content: accumulatedContent }
                    : msg,
                ))
              }
              break
            }

            const chunk = decoder.decode(value, { stream: true })
            chunkCount++
            console.log(`📦 Chunk #${chunkCount} (length: ${chunk.length}):`, chunk)

            /** 检查是否是纯文本响应（非SSE格式） */
            if (chunkCount === 1 && !chunk.includes('event:') && !chunk.includes('data:')) {
              console.log('⚠️ 响应可能不是SSE格式，尝试作为普通文本处理')
              accumulatedContent += chunk
              setMessages(prev => prev.map(msg =>
                msg.id === aiMessageId
                  ? { ...msg, content: accumulatedContent }
                  : msg,
              ))
              continue
            }

            buffer += chunk
            const lines = buffer.split('\n')
            buffer = lines.pop() || ''

            for (const line of lines) {
              console.log(`📄 Processing line (length: ${line.length}):`, line)

              if (line.trim() === '')
                continue

              /** 处理 event: 行 */
              if (line.startsWith('event:')) {
                eventType = line.slice(6).trim()
                console.log('🔔 Event type:', eventType)
                continue
              }

              /** 处理 id: 行 */
              if (line.startsWith('id:')) {
                console.log('🆔 Event ID:', line.slice(3).trim())
                continue
              }

              /** 处理 data: 行 */
              if (line.startsWith('data:')) {
                const data = line.slice(5).trim()
                console.log('📊 Data received (length:', data.length, '):', data)

                if (data === '[DONE]' || eventType === 'Done') {
                  console.log('SSE stream ended, final content:', accumulatedContent)
                  /** 流结束，更新消息状态 */
                  setMessages(prev => prev.map(msg =>
                    msg.id === aiMessageId
                      ? { ...msg, content: accumulatedContent || 'AI 回复完成' }
                      : msg,
                  ))

                  /** 检查是否需要显示按钮 */
                  let showButtons = false
                  let buttonType: 'confirm' | 'approve' | undefined

                  if (accumulatedContent.includes('Please review and edit these details')
                    || accumulatedContent.includes('make sure they match your brand perfectly')) {
                    showButtons = true
                    buttonType = 'confirm'
                  }
                  else if (accumulatedContent.includes('Brand strategy is ready')
                    || accumulatedContent.includes('comprehensive positioning strategy')) {
                    showButtons = true
                    buttonType = 'approve'
                  }

                  if (showButtons) {
                    setMessages(prev => prev.map(msg =>
                      msg.id === aiMessageId
                        ? { ...msg, showButtons, buttonType }
                        : msg,
                    ))
                  }
                  continue
                }

                try {
                  const jsonData = JSON.parse(data)
                  console.log('Parsed JSON data:', jsonData)

                  /** 处理消息内容 */
                  if (jsonData.content) {
                    accumulatedContent += jsonData.content
                    console.log('Accumulated content:', accumulatedContent)
                    /** 实时更新消息内容 */
                    setMessages(prev => prev.map(msg =>
                      msg.id === aiMessageId
                        ? { ...msg, content: accumulatedContent }
                      : msg,
                  ))
                }

                /** 检查 code 字段（dialogue 工作流返回）或 nextStep（初始响应） */
                const exitCode = jsonData.code || jsonData.nextStep
                if (exitCode && exitCode !== 'code_0') {
                  console.log('Initial response code/nextStep changed to:', exitCode, ', showing redirect countdown')

                  /** 清空累积内容，只显示固定文案 */
                  accumulatedContent = ''

                  /** 更新消息为固定的提示文案，带倒计时标记 */
                  setMessages(prev => prev.map(msg =>
                    msg.id === aiMessageId
                      ? {
                          ...msg,
                          content: 'This seems like a new ask, for better project management, I will start a new chat for you. You can still come back to this chat by going to your project panels.',
                          showCountdown: true,
                        }
                      : msg,
                  ))

                  /** 保存退出数据并显示倒计时 */
                  setPendingExitData({
                    detectedIntent: exitCode,
                    nextStep: exitCode,
                    taskInstanceId,
                    userMessage: initialMessage,
                    uploadedImage,
                  })
                  setShowRedirect(true)
                  setCountdown(10)

                  /** 停止继续处理后续消息 */
                  break
                }
                }
                catch (e) {
                  console.error('❌ Failed to parse SSE data in initial response:', e, 'Raw data:', data)
                }
              }
            }
          }
        } catch (error) {
            cleanup()
            throw error
          } finally {
            cleanup()
          }
        }
      }
    catch (error) {
      console.error('Failed to fetch initial AI response:', error)
      /** 如果失败，显示一个默认消息 */
      setMessages(prev => prev.map(msg =>
        msg.id === 'initial-ai'
          ? {
              ...msg,
              content: 'How can I help you with your brand today?',
            }
          : msg,
      ))
    }
    finally {
      setIsLoading(false)
    }
  }

  const handleSendMessage = async (messageText?: string) => {
    const textToSend = messageText || inputValue.trim()
    if (!textToSend || isLoading)
      return

    /** 添加用户消息 */
    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      content: textToSend,
      role: 'user',
    }
    setMessages(prev => [...prev, userMessage])
    setInputValue('')
    setIsLoading(true)

    try {
      /** 如果已经在对话流程中，直接调用 dialogue 工作流 */
      if (isInDialogue && taskInstanceId) {
        console.log('💬 已在对话流程中，直接调用 dialogue 工作流...')

        /** 准备 SSE 请求参数 */
        const sseParams = {
          taskInstanceId,
          platform: 'rednote',
          workflowName: 'dialogue',
          parameters: {
            USER_INPUT: textToSend,
          },
        }

        console.log('Sending SSE request to execute-main:', sseParams)

        /** 创建 AI 消息占位符 */
        const aiMessageId = (Date.now() + 1).toString()
        const aiMessage: ChatMessage = {
          id: aiMessageId,
          content: '',
          role: 'assistant',
        }
        setMessages(prev => [...prev, aiMessage])

        /** 调用 SSE 流式接口 */
        const token = userStore.token
        const sseResponse = await fetchWithStreamTimeout(`${import.meta.env.VITE_API_BASE_URL}/app/market/stream/execute-main`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`,
          },
          body: JSON.stringify(sseParams),
        })

        if (!sseResponse.ok) {
          throw new Error(`SSE request failed: ${sseResponse.status}`)
        }

        /** 处理 SSE 流式数据 */
        const reader = sseResponse.body?.getReader()
        const decoder = new TextDecoder()

        if (reader) {
          console.log('🚀 开始接收用户消息的 SSE 流式数据...')
          const { readWithTimeout, cleanup } = createReaderTimeoutHandler(reader)
          let buffer = ''
          let accumulatedContent = ''
          let messageCount = 0
          let eventType = ''
          let chunkCount = 0

          try {
            while (true) {
              const { done, value } = await readWithTimeout()
              if (done) {
                console.log('✅ SSE 流式数据接收完成，共接收', chunkCount, '个chunks，', messageCount, '条消息')
                console.log('📝 Final content:', accumulatedContent)

                /** 如果有累积的内容但没有正常结束，也要更新消息 */
                if (accumulatedContent) {
                  setMessages(prev => prev.map(msg =>
                    msg.id === aiMessageId
                      ? { ...msg, content: accumulatedContent }
                      : msg,
                  ))
                }
                break
              }

              const chunk = decoder.decode(value, { stream: true })
              chunkCount++
              console.log(`📦 User message chunk #${chunkCount} (length: ${chunk.length}):`, chunk)

              /** 检查是否是纯文本响应（非SSE格式） */
              if (chunkCount === 1 && !chunk.includes('event:') && !chunk.includes('data:')) {
                console.log('⚠️ 响应可能不是SSE格式，尝试作为普通文本处理')
                accumulatedContent += chunk
                setMessages(prev => prev.map(msg =>
                  msg.id === aiMessageId
                    ? { ...msg, content: accumulatedContent }
                    : msg,
                ))
                continue
              }

              buffer += chunk
              const lines = buffer.split('\n')
              buffer = lines.pop() || ''

              for (const line of lines) {
                console.log(`📄 Processing line in handleSend (length: ${line.length}):`, line)

                if (line.trim() === '')
                  continue

                /** 处理 event: 行 */
                if (line.startsWith('event:')) {
                  eventType = line.slice(6).trim()
                  console.log('🔔 Event type in handleSend:', eventType)
                  continue
                }

                /** 处理 id: 行 */
                if (line.startsWith('id:')) {
                  console.log('🆔 Event ID in handleSend:', line.slice(3).trim())
                  continue
                }

                /** 处理 data: 行 */
                if (line.startsWith('data:')) {
                  const data = line.slice(5).trim()
                  console.log('📊 Data in handleSend (length:', data.length, '):', data)

                  if (data === '[DONE]') {
                    console.log('SSE stream ended')
                    /** 流结束，更新消息状态 */
                    setMessages(prev => prev.map(msg =>
                      msg.id === aiMessageId
                        ? { ...msg, content: accumulatedContent }
                        : msg,
                    ))

                    /** 检查是否需要显示按钮 */
                    let showButtons = false
                    let buttonType: 'confirm' | 'approve' | undefined

                    if (accumulatedContent.includes('Please review and edit these details')
                      || accumulatedContent.includes('make sure they match your brand perfectly')) {
                      showButtons = true
                      buttonType = 'confirm'
                    }
                    else if (accumulatedContent.includes('Brand strategy is ready')
                      || accumulatedContent.includes('comprehensive positioning strategy')) {
                      showButtons = true
                      buttonType = 'approve'
                    }

                    if (showButtons) {
                      setMessages(prev => prev.map(msg =>
                        msg.id === aiMessageId
                          ? { ...msg, showButtons, buttonType }
                          : msg,
                      ))
                    }
                    continue
                  }

                  try {
                    const jsonData = JSON.parse(data)
                    messageCount++
                    console.log('✅ Parsed JSON data in handleSend:', jsonData)

                    /** 检查是否是 code 节点的响应 */
                    if (jsonData.node_title === 'code' && jsonData.content) {
                      console.log('📍 Received code node:', jsonData.content)

                      /** 如果 code 不是 code_0，显示倒计时界面 */
                      if (jsonData.content !== 'code_0') {
                        console.log('🔄 Code changed to:', jsonData.content, ', showing redirect countdown')

                        /** 立即更新 localStorage 中的 detectedIntent */
                        localStorage.setItem('detectedIntent', jsonData.content)
                        console.log('💾 Updated detectedIntent in localStorage:', jsonData.content)

                        /** 清空之前累积的内容，只显示固定文案 */
                        accumulatedContent = '' // 清空累积内容

                        /** 更新消息为固定的提示文案 */
                        setMessages(prev => prev.map(msg =>
                          msg.id === aiMessageId
                            ? {
                                ...msg,
                                content: 'This seems like a new ask, for better project management, I will start a new chat for you. You can still come back to this chat by going to your project panels.',
                                showCountdown: true, // 添加标记显示倒计时
                              }
                            : msg,
                        ))

                        /** 保存退出数据并显示倒计时 */
                        setPendingExitData({
                          detectedIntent: jsonData.content,
                          nextStep: jsonData.content,
                          taskInstanceId,
                          userMessage: textToSend,
                          uploadedImage,
                        })
                        setShowRedirect(true)
                        setCountdown(10)

                        /** 停止继续处理后续消息 */
                        break
                      }
                      else {
                        console.log('✅ Code is code_0, continue dialogue')
                      }
                    }
                    else if (jsonData.content && !showRedirect) {
                      /** 只有在不显示重定向时才处理其他消息内容 */
                      accumulatedContent += jsonData.content
                      console.log('📝 Accumulated content in handleSend:', accumulatedContent)
                      /** 实时更新消息内容 */
                      setMessages(prev => prev.map(msg =>
                        msg.id === aiMessageId
                          ? { ...msg, content: accumulatedContent }
                          : msg,
                      ))
                    }
                  }
                  catch (e) {
                    console.error('❌ Failed to parse SSE data in handleSend:', e, 'Raw data:', data)
                  }
                }
              }
            }
          } catch (error) {
            cleanup()
            throw error
          } finally {
            cleanup()
          }
        }
      }
      else {
        /** 第一次进入对话，需要调用 create-session */
        console.log('🎯 第一次进入对话，调用 create-session...')

        const sessionParams = {
          platform: 'rednote',
          userMessage: textToSend,
          uploadedImage,
        }

        const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/app/market/create-session`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${userStore.token}`,
          },
          body: JSON.stringify(sessionParams),
        })

        if (!response.ok) {
          throw new Error(`Create session failed: ${response.status}`)
        }

        const sessionData = await response.json()
        console.log('Create session response:', sessionData)

        /** 保存 taskInstanceId */
        const newTaskInstanceId = sessionData.data?.taskInstanceId
        if (newTaskInstanceId) {
          setTaskInstanceId(newTaskInstanceId)
        }

        /** 检查是否进入对话流程 */
        const detectedCode = sessionData.data?.detectedIntent || sessionData.data?.nextStep
        if (detectedCode === 'code_0') {
          console.log('✅ Entering dialogue flow')
          setIsInDialogue(true)

          /** 添加 AI 响应消息 */
          const aiMessageId = (Date.now() + 1).toString()
          const aiMessage: ChatMessage = {
            id: aiMessageId,
            content: sessionData.data?.response || 'How can I help you with your brand strategy today?',
            role: 'assistant',
          }
          setMessages(prev => [...prev, aiMessage])
        }
        else {
          /** 不是对话流程，需要切换到其他工作流 */
          console.log('🔄 Not dialogue flow, switching to:', detectedCode)

          /** 显示提示消息 */
          const aiMessageId = (Date.now() + 1).toString()
          const aiMessage: ChatMessage = {
            id: aiMessageId,
            content: 'I\'ll help you create content for that. Switching to content creation mode...',
            role: 'assistant',
          }
          setMessages(prev => [...prev, aiMessage])

          /** 延迟退出 */
          setTimeout(() => {
            if (onExitChat) {
              onExitChat({
                detectedIntent: detectedCode,
                nextStep: detectedCode,
                taskInstanceId: newTaskInstanceId,
                userMessage: textToSend,
                uploadedImage,
              })
            }
          }, 1500)
        }
      }
    }
    catch (error) {
      console.error('Failed to send message:', error)
      antMessage.error('Failed to send message. Please try again.')
    }
    finally {
      setIsLoading(false)
    }
  }

  const handleButtonClick = (action: 'confirm' | 'approve') => {
    /** 处理按钮点击 */
    console.log('Button clicked:', action)

    /** 发送确认或批准的消息 */
    if (action === 'confirm') {
      handleSendMessage('Confirm')
    }
    else if (action === 'approve') {
      handleSendMessage('Approve Strategy')
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  /** 模拟的初始消息 */
  const initialMessages: ChatMessage[] = [
    {
      id: '1',
      content: initialMessage || 'Create a Rednote post for my interior design company. We offer whole-house design, soft furnishing, and space renovation for young families and first-time homeowners. Our specialties are eco-friendly materials, personalized solutions, and full-service support. Write 200-300 words in a professional yet warm tone, include popular design trend keywords, add relevant hashtags, and end with an engaging question. Also suggest suitable images.',
      role: 'user',
    },
    {
      id: '2',
      content: 'Please review and edit these details to make sure they match your brand perfectly',
      role: 'assistant',
      showButtons: true,
      buttonType: 'confirm',
    },
  ]

  return (
    <div className="h-full w-full flex flex-col bg-gray-50">
      {/* 主内容区域 */}
      <div className="flex-1 overflow-y-auto" style={ { background: '#fff' } }>
        <div className="mx-auto max-w-6xl p-6">
          {/* 消息列表 */}
          <div className="space-y-4">
            {(messages.length > 0
              ? messages
              : initialMessages).map((msg, index) => {
              const messagesArray = messages.length > 0
                ? messages
                : initialMessages
              const isLastMessage = index === messagesArray.length - 1

              return (
                <motion.div
                  key={ msg.id }
                  initial={ { opacity: 0, y: 10 } }
                  animate={ { opacity: 1, y: 0 } }
                  transition={ { duration: 0.3 } }
                  className={ msg.role === 'user'
                    ? 'flex justify-end'
                    : 'flex justify-start' }
                >
                  {msg.role === 'user' ? (
                    /** 用户消息 - 居右，灰色背景 */
                    <div className="max-w-[70%] rounded-2xl bg-gray-100 px-4 py-3">
                      <p className="text-gray-800">{msg.content}</p>
                    </div>
                  ) : (
                    // AI 消息 - 居左，无背景
                    <div className="max-w-[70%] space-y-3">
                      <div className="px-1">
                        {/** 直接渲染内容，保留换行和格式 */}
                        <p className="whitespace-pre-wrap text-gray-800">{msg.content}</p>
                      </div>

                      {/* 倒计时显示 - 只在最后一条带有 showCountdown 标记的消息显示 */}
                      {msg.showCountdown && showRedirect && isLastMessage && (
                        <div className="flex items-center gap-2 px-1 text-sm">
                          <AIGenerateIcon className="h-3 w-3" />
                          <span className="text-gray-500">Starting a new chat for you...</span>
                          <span className="text-gray-500">
                            {countdown}
                            s
                          </span>
                          <button
                            onClick={ handleStopRedirect }
                            className="ml-1 text-blue-600 font-medium hover:text-blue-700"
                          >
                            Stop
                          </button>
                        </div>
                      )}

                      {/* 按钮区域 */}
                      {msg.showButtons && (
                        <div className="flex justify-end">
                          {msg.buttonType === 'confirm'
                            ? (
                                <button
                                  onClick={ () => handleButtonClick('confirm') }
                                  className="rounded-md bg-black px-6 py-2 text-sm text-white transition-colors hover:bg-gray-800"
                                >
                                  Confirm
                                </button>
                              )
                            : msg.buttonType === 'approve'
                              ? (
                                  <button
                                    onClick={ () => handleButtonClick('approve') }
                                    className="rounded-md bg-black px-6 py-2 text-sm text-white transition-colors hover:bg-gray-800"
                                  >
                                    Approve Strategy
                                  </button>
                                )
                              : null}
                        </div>
                      )}
                    </div>
                  )}
                </motion.div>
              )
            })}

            {/* Loading 指示器 */}
            {isLoading && (
              <div className="flex justify-start">
                <div className="max-w-[70%] px-1">
                  <div className="flex space-x-1">
                    <div className="h-2 w-2 animate-bounce rounded-full bg-gray-400"></div>
                    <div className="h-2 w-2 animate-bounce rounded-full bg-gray-400" style={ { animationDelay: '0.1s' } }></div>
                    <div className="h-2 w-2 animate-bounce rounded-full bg-gray-400" style={ { animationDelay: '0.2s' } }></div>
                  </div>
                </div>
              </div>
            )}

            <div ref={ messagesEndRef } />
          </div>
        </div>
      </div>

      {/* 底部输入区域 */}
      <div className="border-t border-gray-200 bg-white">
        <div className="mx-auto max-w-3xl p-4">
          <div className="flex items-center gap-3">
            <input
              type="text"
              value={ inputValue }
              onChange={ e => setInputValue(e.target.value) }
              onKeyPress={ handleKeyPress }
              placeholder="Ask what's on your mind"
              disabled={ isLoading }
              className="flex-1 border border-gray-300 rounded-lg bg-gray-50 px-4 py-3 text-sm focus:border-gray-400 disabled:bg-gray-100 focus:outline-none"
            />
            <button
              onClick={ () => handleSendMessage() }
              disabled={ !inputValue.trim() || isLoading }
              className="h-10 w-10 flex items-center justify-center rounded-lg bg-gray-100 text-gray-600 transition-colors hover:bg-gray-200 disabled:opacity-50"
            >
              <Send size={ 18 } />
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default AIChat
