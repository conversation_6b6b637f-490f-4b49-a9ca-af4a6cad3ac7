/* eslint-disable style/multiline-ternary */
/**
 * ChatWorkflow - 统一的聊天工作流组件
 * 包含内容展示、Thinking状态、工作流展示、表单等所有功能
 * 高度可配置，通过props控制所有行为
 */

import type { MarketStep1Params } from '@/api/MarketApi'
import type { SelectOption, TreeSelectOption } from '@/stores/industryStore'

import cn from 'clsx'
import { AnimatePresence, motion } from 'framer-motion'
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { Button } from '@/components/Button'

import { SelectableGradientCard } from '@/components/Card/SelectableGradientCard'
import TreeSelect from '@/components/TreeSelect'
// ==================== 导入行业数据管理器 ====================
import { getIndustries, getIndustriesTree, subscribeToIndustryStore } from '@/stores/industryStore'
import { Workflow } from '../../../utils/fabricTools/constant'
import { ChatEventBus } from '../../ChatV2/constants'

import { TextLinkThinkingStream } from './OptimizedThinkingStream'
import { StreamingText, TypewriterMessages, TypewriterText } from './TypewriterText'

// ==================== 类型定义 ====================

/**
 * 工作流步骤配置
 */
export interface WorkflowStep {
  step: number
  agent: string
  description: string
  borderColor?: string
  agentStyle?: React.CSSProperties
}

/**
 * Thinking消息配置
 */
export interface ThinkingMessage {
  id: string
  text: string
}

/**
 * 表单字段配置
 */
export interface FormFieldConfig {
  key: keyof MarketStep1Params
  label: string
  type?: 'input' | 'textarea' | 'select' | 'treeselect'
  placeholder?: string
  required?: boolean
  rows?: number
  gridColumn?: string // 控制在网格中占几列
  options?: SelectOption[] // 下拉框选项（扁平化）
  treeOptions?: TreeSelectOption[] // 树形下拉框选项
}

/**
 * 组件配置Props
 */
export interface ChatWorkflowProps {
  // ========== 内容展示配置 ==========
  content?: string
  uploadedImage?: string | null
  charsPerPage?: number
  showContentDisplay?: boolean
  onContentCopy?: (text: string) => void
  defaultContent?: string

  // ========== Thinking配置 ==========
  isThinking?: boolean
  showThinking?: boolean
  thinkingExpanded?: boolean
  thinkingMessages?: ThinkingMessage[]
  onThinkingToggle?: (expanded: boolean) => void
  onThinkingComplete?: () => void
  thinkingButtonText?: string
  thinkingButtonStyle?: React.CSSProperties

  // ========== 工作流配置 ==========
  showWorkflow?: boolean
  workflowSteps?: WorkflowStep[]
  isLoadingWorkflowSteps?: boolean // 新增：工作流步骤加载状态
  workflowTitle?: string
  workflowDescription?: string
  WorkflowEndingTag?: string
  workflowContainerStyle?: React.CSSProperties
  isStartAiAnalysis?: boolean
  startAiAnalysisText?: string
  onStartAnalysis?: () => void | Promise<void>
  startAnalysisButtonLoading?: boolean
  startAnalysisButtonDisabled?: boolean

  // ========== 表单配置 ==========
  showForm?: boolean
  formData?: Partial<MarketStep1Params>
  formErrors?: Record<string, string>
  formUploadedImage?: string | null
  formFields?: FormFieldConfig[]
  formTitle?: string
  isSubmitting?: boolean
  isUploadingImage?: boolean // 新增图片上传状态
  submitButtonText?: string
  submitButtonLoadingText?: string
  onFormChange?: (field: keyof MarketStep1Params, value: string) => void
  onFormSubmit?: () => void | Promise<void>
  onImageUpload?: (file: File) => void

  // ========== 其他配置 ==========
  onPreviewImages?: () => void
  onImageRemove?: () => void
  onStartContentCreation?: () => void // 新增：开始内容创建回调
  isHotpotsAnalysisComplete?: boolean // 新增：hotpots_analysis 是否完成
  isOriginalWorkLoading?: boolean // 新增：original_work 加载状态

  // ========== Ask输入框配置 ==========
  showAskInput?: boolean
  askInputPlaceholder?: string
  askInputDisabled?: boolean
  askInputValue?: string
  onAskInputChange?: (value: string) => void
  onAskInputSubmit?: () => void

  // ========== 通用配置 ==========
  className?: string
  style?: React.CSSProperties
  containerPadding?: string
  onStepRendered: () => void
}

// ==================== 子组件 ====================

/**
 * 渐变边框标题组件
 */
const GradientTitle: React.FC<{
  title: string
  className?: string
  style?: React.CSSProperties
  borderGradient?: string
}> = ({
  title,
  className = '',
  style = {},
  borderGradient = 'linear-gradient(90deg, #DD9DFF 0%, #36D3FF 100%)',
}) => {
  return (
    <div
      className={ `inline-block ${className}` }
      style={ {
        padding: '2px',
        background: borderGradient,
        borderRadius: '22px',
        ...style,
      } }
    >
      <div className="bg-white px-4 py-1" style={ { borderRadius: '20px' } }>
        <h3 className="text-black font-medium" style={ { fontSize: '14px', margin: 0 } }>
          {title}
        </h3>
      </div>
    </div>
  )
}

/**
 * 带自动滚动的 TypewriterText 包装组件
 */
const TypewriterTextWithScroll: React.FC<{
  text: string
  speed?: number
  className?: string
  style?: React.CSSProperties
  scrollContainer?: React.RefObject<HTMLElement>
  onComplete?: () => void
}> = ({ text, speed = 15, className, style, scrollContainer, onComplete }) => {
  const [isScrolling, setIsScrolling] = useState(true)

  const handleComplete = useCallback(() => {
    setIsScrolling(false)
    if (onComplete) {
      onComplete()
    }
  }, [onComplete])

  useEffect(() => {
    if (!isScrolling)
      return

    /** 每次文本更新时滚动到底部 */
    const interval = setInterval(() => {
      if (scrollContainer?.current) {
        scrollContainer.current.scrollTop = scrollContainer.current.scrollHeight
      }
    }, 50) // 每50ms检查一次滚动

    return () => clearInterval(interval)
  }, [text, scrollContainer, isScrolling])

  return (
    <TypewriterText
      text={ text }
      speed={ speed }
      className={ className }
      style={ style }
      onComplete={ handleComplete }
    />
  )
}

// ==================== 主组件 ====================

/**
 * ChatWorkflow 主组件
 */
export const ChatWorkflow: React.FC<ChatWorkflowProps> = ({
  /** 内容展示 */
  content = '',
  uploadedImage = null,
  charsPerPage = 250,
  showContentDisplay = true,
  onContentCopy,
  defaultContent = 'Create a Rednote post for my interior design company. We offer whole-house design, soft furnishing, and space renovation for young families and first-time homeowners. Our specialties are eco-friendly materials, personalized solutions, and full-service support. Write 200-300 words in a professional yet warm tone, include popular design trend keywords, add relevant hashtags, and end with an engaging question. Also suggest suitable images. This is additional content to test pagination. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Additional content continues here to make the second page look full and complete. More content for page three. The pagination should work for multiple pages now. Each page displays a portion of the content. Page four content here. The system should handle any number of pages dynamically. Page five with more example text to demonstrate the functionality.',

  // Thinking
  isThinking = false,
  showThinking = true,
  thinkingExpanded: thinkingExpandedProp,
  thinkingMessages,
  onThinkingToggle,
  onThinkingComplete,
  thinkingButtonText = 'Thinking...',
  thinkingButtonStyle = {},

  /** 工作流 */
  showWorkflow = false,
  workflowSteps,
  isLoadingWorkflowSteps = false,
  workflowTitle = 'Your AI Agent Team\'s Action plan',
  workflowDescription = 'Perfect! Your brand profile is ready. Now I\'m bringing together my AI agent team to create amazing post content for you. Here\'s how we\'ll work together:',
  WorkflowEndingTag = 'Ready to build your brand strategy? I\'ll use these insights to position your brand effectively in the market.',
  workflowContainerStyle = {},
  startAnalysisButtonLoading: propStartAnalysisButtonLoading = false,
  startAnalysisButtonDisabled: propStartAnalysisButtonDisabled = false,
  isStartAiAnalysis = false,
  startAiAnalysisText = 'Continue to Strategy',
  onStartAnalysis,

  /** 表单 */
  showForm = false,
  formData = {},
  formErrors = {},
  formUploadedImage = null,
  formFields,
  formTitle = 'Action plan',
  isSubmitting = false,
  isUploadingImage = false, // 添加默认值
  submitButtonText = 'Confirm',
  submitButtonLoadingText = 'Processing...',
  onFormChange,
  onFormSubmit,
  onImageUpload,
  onImageRemove,
  onPreviewImages,
  onStartContentCreation, // 新增
  isHotpotsAnalysisComplete = false, // 新增
  isOriginalWorkLoading = false, // 新增

  // Ask输入框
  showAskInput = false,
  askInputPlaceholder = 'Ask what\'s on your mind',
  askInputDisabled = true,
  askInputValue = '',
  onAskInputChange,
  onAskInputSubmit,

  /** 通用 */
  className = '',
  style = {},
  containerPadding = 'clamp(16px, 2vh, 24px)',
  onStepRendered,
}) => {
  // ========== 状态管理 ==========
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [thinkingExpanded, setThinkingExpanded] = useState(thinkingExpandedProp ?? true)
  const thinkingScrollRef = useRef<HTMLDivElement>(null)
  const [renderedSteps, setRenderedSteps] = useState<number>(0) // 已渲染的步骤数量
  const [isRenderingStep, setIsRenderingStep] = useState(false) // 是否正在渲染步骤

  /** 按钮状态管理 - 监听全局按钮状态变化 */
  const [startAnalysisButtonLoading, setStartAnalysisButtonLoading] = useState(propStartAnalysisButtonLoading)
  const [startAnalysisButtonDisabled, setStartAnalysisButtonDisabled] = useState(propStartAnalysisButtonDisabled)

  /** 行业数据状态 - 使用全局缓存管理器 */
  const [industries, setIndustries] = useState<SelectOption[]>([])
  const [industriesTree, setIndustriesTree] = useState<TreeSelectOption[]>([])
  const [industriesLoading, setIndustriesLoading] = useState(false)

  /** 使用缓存管理器获取行业数据 */
  useEffect(() => {
    let unsubscribe: (() => void) | null = null

    const initIndustries = async () => {
      try {
        /** 订阅状态变化 */
        unsubscribe = subscribeToIndustryStore((state) => {
          setIndustries(state.flatData) // 使用扁平化数据
          setIndustriesTree(state.data) // 使用树形数据
          setIndustriesLoading(state.loading)
        })

        /** 获取行业数据（使用缓存） */
        await getIndustries()
        /** 同时获取树形数据 */
        await getIndustriesTree()
      }
      catch (error) {
        console.error('[ChatWorkflow] 获取行业数据失败:', error)
      }
    }

    initIndustries()

    /** 清理订阅 */
    return () => {
      if (unsubscribe) {
        unsubscribe()
      }
    }
  }, [])

  /** 当行业数据加载完成且表单中没有industry值时，自动设置第一个选项 */
  useEffect(() => {
    if (industries.length > 0 && !formData.industry && onFormChange) {
      const firstIndustry = industries[0]
      console.warn('[ChatWorkflow] 自动设置默认行业:', firstIndustry.label, firstIndustry.value)
      onFormChange('industry', firstIndustry.value as string)
    }
  }, [industries, formData.industry, onFormChange])

  // ========== 默认配置 ==========

  /** 默认工作流步骤 */
  const defaultWorkflowSteps: WorkflowStep[] = [
    {
      step: 1,
      agent: 'Research Analyst Agent',
      description: 'Analyze your industry and competitors to understand market trends and identify opportunities for your content strategy.',
    },
    {
      step: 2,
      agent: 'Brand Strategist Agent',
      description: 'Develop your brand positioning and find reference posts that align with your style and target audience.',
    },
    {
      step: 3,
      agent: 'Creative Director Agent',
      description: 'Create compelling post content and generate eye-catching images that will boost engagement and attract followers.',
    },
    {
      step: 4,
      agent: 'Operations Manager Agent',
      description: 'Finalize your content and prepare it for publishing with optimized timing and hashtag recommendations.',
    },
  ]

  /** 默认表单字段 - 动态生成industry字段 */
  const defaultFormFields: FormFieldConfig[] = useMemo(() => [
    { key: 'brand', label: 'Brand name (Required)', required: true },
    { key: 'product_name', label: 'Product name (Required)', required: true },
    {
      key: 'industry',
      label: 'Industry (Required)',
      type: 'treeselect', // 改为树形选择
      required: true,
      options: industries, // 保留扁平化数据用于兼容
      treeOptions: industriesTree, // 新增树形数据
    },
    { key: 'competitor', label: 'List 3 of your main competitors (Required)', required: true, gridColumn: 'span 3' },
    { key: 'product', label: 'Product description (Required)', type: 'textarea', required: true, gridColumn: 'span 3' },
  ], [industries, industriesTree])

  /** 默认Thinking消息 */
  const defaultThinkingMessages = useMemo(() => {
    if (showWorkflow) {
      return [
        { id: '1', text: 'Processing your brand information...' },
        { id: '2', text: 'Planning your content creation strategy...' },
        { id: '3', text: 'Assembling AI agent team for your project...' },
      ]
    }
    /** 只显示一条 loading 消息 */
    return [
      { id: '1', text: 'Catching data from internet...' },
    ]
  }, [showWorkflow])

  // ========== 使用配置或默认值 ==========
  const displayWorkflowSteps = workflowSteps !== undefined
    ? workflowSteps
    : defaultWorkflowSteps
  const displayFormFields = formFields || defaultFormFields
  const displayThinkingMessages = thinkingMessages || defaultThinkingMessages
  const displayContent = content || defaultContent

  // ========== 副作用 ==========

  /** 计算分页 */
  useEffect(() => {
    const pages = Math.ceil(displayContent.length / charsPerPage)
    setTotalPages(pages || 1)
    setCurrentPage(1)
  }, [displayContent, charsPerPage])

  /** 同步外部thinking状态 */
  useEffect(() => {
    if (thinkingExpandedProp !== undefined) {
      setThinkingExpanded(thinkingExpandedProp)
    }
  }, [thinkingExpandedProp])

  /** 监听全局按钮状态变化 */
  useEffect(() => {
    const handleButtonStateChange = (event: CustomEvent) => {
      const { loading, disabled } = event.detail
      setStartAnalysisButtonLoading(loading)
      setStartAnalysisButtonDisabled(disabled)
    }

    window.addEventListener('chatWorkflowButtonStateChange', handleButtonStateChange as EventListener)

    return () => {
      window.removeEventListener('chatWorkflowButtonStateChange', handleButtonStateChange as EventListener)
    }
  }, [])

  /** 同步外部thinking状态 */
  useEffect(() => {
    if (thinkingExpandedProp !== undefined) {
      setThinkingExpanded(thinkingExpandedProp)
    }
  }, [thinkingExpandedProp])

  /** 当工作流步骤数据变化时，启动渲染流程 */
  useEffect(() => {
    if (workflowSteps && workflowSteps.length > 0 && !isLoadingWorkflowSteps) {
      console.log('🎯 工作流步骤数据更新，启动渲染流程', workflowSteps)
      /** 重置并立即开始渲染 */
      setRenderedSteps(0)
      setIsRenderingStep(false)

      /** 使用循环定时器逐步渲染所有步骤 */
      let currentStep = 0
      const renderInterval = setInterval(() => {
        currentStep++
        console.log(`📝 渲染步骤 ${currentStep}/${workflowSteps.length}`)
        setRenderedSteps(currentStep)

        if (currentStep >= workflowSteps.length) {
          console.log('✅ 所有步骤渲染完成')
          onStepRendered()
          clearInterval(renderInterval)
        }
      }, 600) // 每600ms渲染一个步骤

      /** 清理函数 */
      return () => {
        clearInterval(renderInterval)
      }
    }
    else if (isLoadingWorkflowSteps) {
      /** 加载中时重置状态 */
      console.log('⏳ 工作流步骤加载中，重置渲染状态')
      setRenderedSteps(0)
      setIsRenderingStep(false)
    }
  }, [workflowSteps, isLoadingWorkflowSteps])

  // ========== 事件处理 ==========

  const handleCopy = useCallback(() => {
    if (onContentCopy) {
      onContentCopy(displayContent)
    }
    else {
      navigator.clipboard.writeText(displayContent).then(() => {
        const toast = document.createElement('div')
        toast.className = 'fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-gray-800 text-white px-4 py-2 rounded-lg shadow-lg z-50 transition-opacity duration-300'
        toast.textContent = '复制成功'
        document.body.appendChild(toast)

        setTimeout(() => {
          toast.style.opacity = '0'
          setTimeout(() => {
            document.body.removeChild(toast)
          }, 300)
        }, 2000)
      })
    }
  }, [displayContent, onContentCopy])

  const handleThinkingToggle = useCallback(() => {
    const newExpanded = !thinkingExpanded
    setThinkingExpanded(newExpanded)
    onThinkingToggle?.(newExpanded)
  }, [thinkingExpanded, onThinkingToggle])

  const handleFileChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file && onImageUpload) {
      onImageUpload(file)
    }
  }, [onImageUpload])

  // ========== 渲染 ==========

  return (
    <div className={ cn('w-full h-full flex justify-center', className) } style={ style }>
      <div className="h-full max-w-4xl w-full flex justify-center overflow-auto px-4">
        <div
          className="w-full"
          style={ {
            paddingTop: containerPadding,
            paddingBottom: containerPadding,
          } }>

          {/* 内容展示区 */}
          {showContentDisplay && (
            <>
              <div
                className="relative flex flex-col border border-gray-200 rounded-lg bg-gray-50"
                style={ {
                  height: '222px',
                  padding: '16px',
                  marginBottom: 'clamp(12px, 2vh, 16px)',
                } }
              >
                {/* 文本内容区域 - 动态高度 */}
                <div
                  className="relative overflow-hidden"
                  style={ {
                    height: uploadedImage
                      ? '88px'
                      : 'calc(100% - 16px)',
                    marginBottom: uploadedImage
                      ? '16px'
                      : '0',
                  } }>
                  <div
                    className="absolute inset-0 flex transition-transform duration-500 ease-in-out"
                    style={ { transform: `translateX(-${(currentPage - 1) * 100}%)` } }
                  >
                    {Array.from({ length: totalPages }, (_, index) => {
                      const startIndex = index * charsPerPage
                      const endIndex = startIndex + charsPerPage
                      const pageContent = displayContent.substring(startIndex, endIndex)

                      return (
                        <div
                          key={ index }
                          className="h-full flex-shrink-0 overflow-y-auto px-2"
                          style={ { width: '100%', minWidth: '100%' } }
                        >
                          <p
                            className="whitespace-pre-wrap break-words text-gray-600 leading-relaxed"
                            style={ { fontSize: 'clamp(12px, 1.2vw, 14px)' } }
                          >
                            {pageContent}
                          </p>
                        </div>
                      )
                    })}
                  </div>
                </div>

                {/* 占位空间 - 只在有图片时显示 */}
                {uploadedImage && <div className="flex-1"></div>}

                {/* 图片展示 */}
                {uploadedImage && (
                  <div
                    className="mt-auto overflow-hidden border border-gray-200 rounded-lg bg-white"
                    style={ { width: '104px', height: '104px' } }
                  >
                    <img src={ uploadedImage } alt="Uploaded content" className="h-full w-full object-cover" />
                  </div>
                )}
              </div>

              {/* 操作按钮 */}
              <div className="flex items-center justify-between">
                {/* 左侧：占位div，保持布局 */}
                <div></div>

                {/* 右侧：复制和分页按钮 */}
                <div className="flex items-center gap-2">
                  <button
                    className="rounded-lg transition-colors hover:bg-gray-100"
                    style={ { padding: 'clamp(6px, 1vw, 8px)' } }
                    onClick={ handleCopy }
                  >
                    <svg
                      style={ { width: 'clamp(16px, 1.5vw, 18px)', height: 'clamp(16px, 1.5vw, 18px)' } }
                      viewBox="0 0 24 24"
                      fill="none"
                    >
                      <rect x="9" y="9" width="13" height="13" rx="2" stroke="#666" strokeWidth="1.5" />
                      <path d="M5 15H4C3 15 2 14 2 13V4C2 3 3 2 4 2H13C14 2 15 3 15 4V5" stroke="#666" strokeWidth="1.5" />
                    </svg>
                  </button>

                  <div className="flex items-center gap-1">
                    <button
                      className="rounded transition-colors disabled:cursor-not-allowed hover:bg-gray-100 disabled:opacity-50"
                      style={ { padding: 'clamp(4px, 0.5vw, 6px)' } }
                      onClick={ () => setCurrentPage(prev => Math.max(1, prev - 1)) }
                      disabled={ currentPage === 1 }
                    >
                      <svg
                        style={ { width: 'clamp(14px, 1.3vw, 16px)', height: 'clamp(14px, 1.3vw, 16px)' } }
                        viewBox="0 0 24 24"
                        fill="none"
                      >
                        <path
                          d="M15 18L9 12L15 6"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                      </svg>
                    </button>

                    <span
                      className="text-gray-500"
                      style={ { fontSize: 'clamp(12px, 1.2vw, 14px)', padding: '0 clamp(4px, 1vw, 8px)' } }
                    >
                      {currentPage}
                      {' '}
                      /
                      {totalPages}
                    </span>

                    <button
                      className="rounded transition-colors disabled:cursor-not-allowed hover:bg-gray-100 disabled:opacity-50"
                      style={ { padding: 'clamp(4px, 0.5vw, 6px)' } }
                      onClick={ () => setCurrentPage(prev => Math.min(totalPages, prev + 1)) }
                      disabled={ currentPage === totalPages }
                    >
                      <svg
                        style={ { width: 'clamp(14px, 1.3vw, 16px)', height: 'clamp(14px, 1.3vw, 16px)' } }
                        viewBox="0 0 24 24"
                        fill="none"
                      >
                        <path
                          d="M9 18L15 12L9 6"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                      </svg>
                    </button>
                  </div>
                </div>
              </div>
            </>
          )}

          {/* Thinking状态 */}
          {showThinking && (
          /** 使用优化的StreamingThinkingStream组件 */
            <TextLinkThinkingStream show={ true } onComplete={ onThinkingComplete } />
          )}

          {/* 工作流 */}
          {showWorkflow && (
            <div className="mt-4">
              <p className="mb-4 text-gray-700" style={ { fontSize: '14px' } }>
                {workflowDescription}
              </p>

              <div
                className="relative rounded-lg bg-white"
                style={ {
                  border: '2px solid transparent',
                  backgroundImage: 'linear-gradient(to right, rgb(255, 255, 255), rgb(255, 255, 255)), linear-gradient(90deg, rgb(221, 157, 255) 0%, rgb(54, 211, 255) 100%)',
                  backgroundOrigin: 'border-box',
                  backgroundClip: 'content-box, border-box',
                  padding: '1px',
                  ...workflowContainerStyle,
                } }
              >
                <div className="rounded-lg bg-white p-6">
                  <GradientTitle title={ workflowTitle } className="mb-4" />

                  {isLoadingWorkflowSteps ? (
                    /** 加载状态时显示骨架屏 */
                    <div className="space-y-4">
                      {[1, 2, 3, 4].map(step => (
                        <div key={ step } className="flex animate-pulse items-start gap-4">
                          <div className="h-6 w-12 rounded bg-gray-200"></div>
                          <div className="h-8 w-44 rounded-full bg-gray-200"></div>
                          <div className="h-6 flex-1 rounded bg-gray-200"></div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    /** 渲染实际步骤 */
                    (() => {
                      console.log('🎨 渲染工作流步骤:', {
                        displayWorkflowSteps: displayWorkflowSteps.length,
                        renderedSteps,
                        stepsToRender: displayWorkflowSteps.slice(0, renderedSteps),
                      })
                      return displayWorkflowSteps.slice(0, renderedSteps).map((item, index) => (
                        <motion.div
                          key={ item.step }
                          initial={ { opacity: 0, y: 20 } }
                          animate={ { opacity: 1, y: 0 } }
                          transition={ { duration: 0.5 } }
                          className="flex items-start gap-4"
                          style={ { marginBottom: index < displayWorkflowSteps.length - 1
                            ? '32px'
                            : '0' } }
                        >
                          <span
                            className="flex-shrink-0 self-center text-gray-600"
                            style={ { fontSize: '14px', width: '50px' } }
                          >
                            Step
                            {' '}
                            {item.step}
                          </span>
                          <div
                            className="flex-shrink-0 self-center px-3 py-1 text-center text-gray-700"
                            style={ {
                              border: `1px solid ${'#36D3FF'}`,
                              borderRadius: '9999px',
                              fontSize: '14px',
                              minWidth: '180px',
                              ...item.agentStyle,
                            } }
                          >
                            {/* Agent名称使用打字机效果 */}
                            {index === renderedSteps - 1 ? (
                              <TypewriterText
                                text={ item.agent }
                                speed={ 30 }
                              />
                            ) : (
                              item.agent
                            )}
                          </div>
                          <div className="m-0 flex-1 self-center text-gray-600" style={ { fontSize: '14px' } }>
                            {/* 描述使用打字机效果 */}
                            {index === renderedSteps - 1 ? (
                              <TypewriterText
                                text={ item.description }
                                speed={ 15 }
                              />
                            ) : (
                              item.description
                            )}
                          </div>
                        </motion.div>
                      ))
                    })()
                  )}
                </div>
              </div>
              <p className="mb-4 mt-4 text-gray-700" style={ { fontSize: '14px' } }>
                {WorkflowEndingTag}
              </p>
              {/* <div className="flex justify-start">
                <button
                  className={ cn(
                    'px-6 py-2 spx-6 rounded-full text-sm font-medium transition-color bg-black text-white hover:bg-gray-800 ',
                  ) }
                  // disabled={ !isStartAiAnalysis }
                  onClick={ onStartAnalysis }
                >
                  { startAiAnalysisText }11
                </button>
              </div> */}
            </div>
          )}

          {/* 表单 */}
          {showForm && !showWorkflow && (
            <div
              className="relative mt-4 rounded-lg bg-white"
              style={ {
                border: '2px solid transparent',
                backgroundImage: 'linear-gradient(to right, rgb(255, 255, 255), rgb(255, 255, 255)), linear-gradient(90deg, rgb(221, 157, 255) 0%, rgb(54, 211, 255) 100%)',
                backgroundOrigin: 'border-box',
                backgroundClip: 'content-box, border-box',
                padding: '1px',
              } }
            >
              <div className="space-y-3" style={ { padding: 'clamp(12px, 2vw, 20px)' } }>
                <GradientTitle
                  title={ formTitle }
                  style={ { marginBottom: 'clamp(8px, 1vh, 12px)' } }
                />

                {/* 动态渲染表单字段 */}
                <div
                  className="grid grid-cols-1 lg:grid-cols-3 sm:grid-cols-2"
                  style={ { gap: 'clamp(8px, 1.5vw, 12px)', marginBottom: 'clamp(8px, 1vh, 12px)' } }
                >
                  {displayFormFields.map((field) => {
                    if (field.gridColumn === 'span 3')
                      return null // 跳过全宽字段

                    return (
                      <div key={ field.key }>
                        <label className="mb-1 block text-sm text-gray-700 font-bold">
                          {field.label}
                        </label>
                        {field.type === 'textarea'
                          ? (
                              <textarea
                                className={ cn(
                                  'w-full border rounded focus:border-blue-400 outline-none',
                                  formErrors[field.key]
                                    ? 'border-red-500'
                                    : 'border-gray-200',
                                ) }
                                rows={ field.rows || 2 }
                                style={ { padding: 'clamp(6px, 1vw, 8px)', fontSize: 'clamp(12px, 1.2vw, 14px)' } }
                                placeholder={ field.placeholder || 'Placeholder' }
                                value={ formData[field.key] || '' }
                                onChange={ e => onFormChange?.(field.key, e.target.value) }
                              />
                            )
                          : field.type === 'treeselect'
                            ? (
                                <TreeSelect
                                  options={ field.treeOptions || [] }
                                  value={ formData.industry_id || formData[field.key] }
                                  onChange={ (value, option) => {
                                    /** 设置行业显示名称 */
                                    onFormChange?.(field.key, option.label)
                                    /** 同时设置industry_id字段 */
                                    if (field.key === 'industry' && onFormChange) {
                                      onFormChange('industry_id' as keyof MarketStep1Params, value)
                                    }
                                  } }
                                  placeholder={ field.placeholder || '请选择行业' }
                                  disabled={ industriesLoading }
                                  className={ cn(
                                    formErrors[field.key]
                                      ? 'border-red-500'
                                      : '',
                                  ) }
                                />
                              )
                            : field.type === 'select'
                              ? (
                                  <select
                                    className={ cn(
                                      'w-full border rounded focus:border-blue-400 outline-none',
                                      formErrors[field.key]
                                        ? 'border-red-500'
                                        : 'border-gray-200',
                                    ) }
                                    style={ { padding: 'clamp(6px, 1vw, 8px)', fontSize: 'clamp(12px, 1.2vw, 14px)' } }
                                    value={ formData[field.key] || (field.options && field.options.length > 0
                                      ? field.options[0].value
                                      : '') }
                                    onChange={ e => onFormChange?.(field.key, e.target.value) }
                                    disabled={ industriesLoading }
                                  >
                                    {industriesLoading && (
                                      <option value="">Loading industries...</option>
                                    )}
                                    {field.options?.map(option => (
                                      <option key={ option.value } value={ option.value }>
                                        {option.label}
                                      </option>
                                    ))}
                                  </select>
                                )
                              : (
                                  <input
                                    type="text"
                                    className={ cn(
                                      'w-full border rounded focus:border-blue-400 outline-none',
                                      formErrors[field.key]
                                        ? 'border-red-500'
                                        : 'border-gray-200',
                                    ) }
                                    style={ { padding: 'clamp(6px, 1vw, 8px)', fontSize: 'clamp(12px, 1.2vw, 14px)' } }
                                    placeholder={ field.placeholder || 'Placeholder' }
                                    value={ formData[field.key] || '' }
                                    onChange={ e => onFormChange?.(field.key, e.target.value) }
                                  />
                                )}
                        {formErrors[field.key] && (
                          <p className="mt-1 text-xs text-red-500">{formErrors[field.key]}</p>
                        )}
                      </div>
                    )
                  })}
                </div>

                {/* 全宽字段 */}
                {displayFormFields.filter(f => f.gridColumn === 'span 3').map(field => (
                  <div key={ field.key }>
                    <label className="mb-1 block text-sm text-gray-700 font-bold">
                      {field.label}
                    </label>
                    {field.type === 'textarea'
                      ? (
                          <textarea
                            className={ cn(
                              'w-full border rounded focus:border-blue-400 outline-none',
                              formErrors[field.key]
                                ? 'border-red-500'
                                : 'border-gray-200',
                            ) }
                            rows={ field.rows || 2 }
                            style={ { padding: 'clamp(6px, 1vw, 8px)', fontSize: 'clamp(12px, 1.2vw, 14px)' } }
                            placeholder={ field.placeholder || 'Placeholder' }
                            value={ formData[field.key] || '' }
                            onChange={ e => onFormChange?.(field.key, e.target.value) }
                          />
                        )
                      : field.type === 'select'
                        ? (
                            <select
                              className={ cn(
                                'w-full border rounded focus:border-blue-400 outline-none',
                                formErrors[field.key]
                                  ? 'border-red-500'
                                  : 'border-gray-200',
                              ) }
                              style={ { padding: 'clamp(6px, 1vw, 8px)', fontSize: 'clamp(12px, 1.2vw, 14px)' } }
                              value={ formData[field.key] || (field.options && field.options.length > 0
                                ? field.options[0].value
                                : '') }
                              onChange={ e => onFormChange?.(field.key, e.target.value) }
                              disabled={ industriesLoading }
                            >
                              {industriesLoading && (
                                <option value="">Loading industries...</option>
                              )}
                              {field.options?.map(option => (
                                <option key={ option.value } value={ option.value }>
                                  {option.label}
                                </option>
                              ))}
                            </select>
                          )
                        : (
                            <input
                              type="text"
                              className={ cn(
                                'w-full border rounded focus:border-blue-400 outline-none',
                                formErrors[field.key]
                                  ? 'border-red-500'
                                  : 'border-gray-200',
                              ) }
                              placeholder={ field.placeholder || 'Placeholder' }
                              value={ formData[field.key] || '' }
                              onChange={ e => onFormChange?.(field.key, e.target.value) }
                            />
                          )}
                    {formErrors[field.key] && (
                      <p className="mt-1 text-xs text-red-500">{formErrors[field.key]}</p>
                    )}
                  </div>
                ))}

                {/* 图片上传 */}
                <div>
                  <label className="mb-1 block text-sm text-gray-700 font-bold">
                    Upload (Required)
                  </label>
                  <label className="inline-block cursor-pointer">
                    <input
                      type="file"
                      accept="image/*"
                      className="hidden"
                      onChange={ handleFileChange }
                    />
                    {formUploadedImage
                      ? (
                          <div className="relative" style={ { width: '104px', height: '104px' } }>
                            <img
                              src={ formUploadedImage }
                              alt="Uploaded"
                              className="h-full w-full border border-gray-200 rounded object-cover"
                            />
                            {/* 图片上传中的遮罩层 */}
                            {isUploadingImage && (
                              <div className="absolute inset-0 flex items-center justify-center rounded bg-black bg-opacity-50">
                                <div className="h-8 w-8 animate-spin border-b-2 border-white rounded-full"></div>
                              </div>
                            )}
                            {!isUploadingImage && (
                              <div className="absolute right-1 top-1">
                                <button
                                  type="button"
                                  onClick={ (e) => {
                                    e.preventDefault()
                                    onImageRemove?.()
                                  } }
                                  className="h-5 w-5 flex items-center justify-center rounded-full bg-red-500 text-xs text-white hover:bg-red-600"
                                >
                                  ×
                                </button>
                              </div>
                            )}
                          </div>
                        )
                      : (
                          <div
                            className={ cn(
                              'border-2 border-dashed rounded-lg flex flex-col items-center justify-center transition-colors',
                              isUploadingImage
                                ? 'bg-gray-100 cursor-not-allowed'
                                : 'hover:bg-gray-50',
                              formErrors.pic
                                ? 'border-red-500'
                                : 'border-gray-300',
                            ) }
                            style={ { width: 'clamp(80px, 10vw, 104px)', height: 'clamp(80px, 10vw, 104px)' } }
                          >
                            {isUploadingImage ? (
                              /** 上传中显示 loading */
                              <div className="flex flex-col items-center">
                                <div className="mb-1 h-6 w-6 animate-spin border-b-2 border-gray-600 rounded-full"></div>
                                <span className="text-xs text-gray-600">上传中...</span>
                              </div>
                            ) : (
                              /** 默认上传图标 */
                              <>
                                <div className="mb-1 h-8 w-8 flex items-center justify-center rounded-full bg-gray-100">
                                  <svg
                                    style={ { width: 'clamp(14px, 1.3vw, 16px)', height: 'clamp(14px, 1.3vw, 16px)' } }
                                    viewBox="0 0 24 24"
                                    fill="none"
                                  >
                                    <path d="M12 5v14M5 12h14" stroke="#666" strokeWidth="2" strokeLinecap="round" />
                                  </svg>
                                </div>
                                <p className="text-xs text-gray-500">Upload</p>
                              </>
                            )}
                          </div>
                        )}
                  </label>
                  {formErrors.pic && (
                    <p className="mt-1 text-xs text-red-500">{formErrors.pic}</p>
                  )}
                </div>

                {/* 提交按钮 */}
                <div className="flex justify-end">
                  <button
                    className={ cn(
                      'px-6 py-2 rounded-full text-sm font-medium transition-colors',
                      isSubmitting
                        ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                        : 'bg-black text-white hover:bg-gray-800',
                    ) }
                    disabled={ isSubmitting }
                    onClick={ onFormSubmit }
                  >
                    {isSubmitting
                      ? submitButtonLoadingText
                      : submitButtonText}
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Ask输入框 */}
          {showAskInput && (
            <div className="mt-4 flex justify-center">
              <div
                className="flex items-center gap-2 rounded-lg bg-gray-50"
                style={ {
                  maxWidth: '850px',
                  width: '100%',
                  padding: 'clamp(10px, 1.5vh, 12px) clamp(12px, 2vw, 16px)',
                  boxShadow: '0 0 15px rgba(0, 0, 0, 0.1)',
                } }
              >
                <input
                  type="text"
                  disabled={ askInputDisabled }
                  placeholder={ askInputPlaceholder }
                  value={ askInputValue }
                  onChange={ e => onAskInputChange?.(e.target.value) }
                  className="flex-1 bg-transparent text-gray-600 outline-none placeholder-gray-400"
                  style={ { fontSize: 'clamp(12px, 1.2vw, 14px)' } }
                />
                <button
                  className="text-gray-400 hover:text-gray-600"
                  onClick={ onAskInputSubmit }
                >
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                    <path d="M22 2L11 13" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                    <path d="M22 2L15 22L11 13L2 9L22 2Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                  </svg>
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default ChatWorkflow
