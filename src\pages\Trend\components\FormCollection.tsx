import type { StateStoreType } from '../stores'
import type { TrendStep1Params, XhsCategoryReq } from '@/api/TrendApi'
import type { Option } from '@/components/Select'
import { memo, useEffect, useState } from 'react'
import { FileAPI } from '@/api/FileAPI'
import { TrendApi } from '@/api/TrendApi'
import { Button } from '@/components/Button/Button'
import { Form, useForm } from '@/components/Form/Form'
import { GradientText } from '@/components/GradientText'
import { Input } from '@/components/Input'
import { RmBtn } from '@/components/RmBtn'
import { Select } from '@/components/Select'
import { Uploader } from '@/components/Uploader'
import { god } from '@/god'
import { useGetState } from '@/hooks'
import { cn } from '@/utils'

const formInitialValues = {
  brand: '卡姿兰',
  product_name: '无痕锁妆粉底液',
  industry_id: '83',
  industry: '彩妆',
  competitor1: '欧莱雅',
  product: '好用',
  role: '"\n卡姿兰作为中国时尚彩妆领导品牌，创立于 2001 年，秉承 \"活出经典\" 的品牌理念，以 \"年轻、轻经典\" 的创意灵感引领彩妆潮流趋势。品牌专注于国内市场，经过 24 年的发展，已成为技术领先的彩妆专家，长期位居中国彩妆行业市占率前五位置。\n核心定位要素：\n品牌主张：为时尚年轻女性而生，传递 \"活出经典\" 的美妆理念\n技术定位：专研彩妆 24 年的技术专家，拥有自主研发能力\n市场定位：国内市场领先的大众时尚彩妆品牌\n价值主张：让中国消费者用上全球最好的彩妆产品\n目标客户画像：\n卡姿兰的核心目标客户群体为 18-40 岁女性，这一群体具有以下特征：\n人口统计特征：\n年龄：18-40 岁，涵盖 Z 世代和千禧一代\n性别：女性为主\n收入水平：中等收入群体，追求性价比\n心理特征：\n对时尚敏感，但不盲从潮流\n注重产品品质和实用性\n具有民族自豪感，认可国货品牌\n追求个性化表达和自我价值实现\n消费行为特征：\n线上购买习惯成熟，活跃于抖音、小红书等社交平台\n注重产品口碑和使用体验\n对爆品和经典产品有持续复购需求\n价格敏感度适中，重视性价比\n"',
  company: '卡姿兰',
  ip: '专研彩妆',
  marketing_strategy: '好用',
  product_market: '',
  competitor_info: '女性彩妆',
}

/**
 * 递归转换API数据为Select组件所需的嵌套格式
 */
function transformIndustryTreeForSelect(nodes: XhsCategoryReq[]): Option[] {
  return nodes.map((node) => {
    const option: Option = {
      label: node.label,
      value: String(node.tagId),
    }
    if (node.subTagList && node.subTagList.length > 0) {
      option.children = transformIndustryTreeForSelect(node.subTagList)
    }
    return option
  })
}

/**
 * 在嵌套选项中递归查找指定值的标签
 */
function findLabelInOptions(options: Option[], value: string): string | null {
  for (const option of options) {
    if (option.value === value) {
      return option.label as string
    }
    if (option.children) {
      const foundLabel = findLabelInOptions(option.children, value)
      if (foundLabel) {
        return foundLabel
      }
    }
  }
  return null
}

export const FormCollection = memo<FormCollectionProps>((
  {
    style,
    className,

    stateStore,
  },
) => {
  const [productImage, setProductImage] = useGetState('', true)
  const [industryOptions, setIndustryOptions] = useState<Option[]>([])
  const [isLoadingIndustries, setIsLoadingIndustries] = useState(true)
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({})
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    const fetchIndustries = async () => {
      try {
        const treeData = await TrendApi.getTreeList()
        if (treeData) {
          const transformedOptions = transformIndustryTreeForSelect(treeData)
          setIndustryOptions(transformedOptions)
        }
      }
      catch (error) {
        console.error('Failed to fetch industries', error)
      }
      finally {
        setIsLoadingIndustries(false)
      }
    }
    fetchIndustries()
  }, [])

  const validateForm = (values: Record<string, any>): Record<string, string> => {
    const errors: Record<string, string> = {}

    /** 必填项验证 */
    const requiredFields = [
      'brand',
      'product_name',
      'industry_id',
      'competitor1',
      'product',
      'role',
      'company',
      'ip',
      'marketing_strategy',
      'competitor_info',
    ]

    requiredFields.forEach((field) => {
      if (!values[field]) {
        errors[field] = `Please fill ${field}`
      }
    })

    return errors
  }

  const handleSubmit = async (values: Record<string, any>) => {
    /** 验证表单 */
    const errors = validateForm(values)
    const picture = setProductImage.getLatest()
    !picture && (errors.picture = 'Please upload product image')

    if (Object.keys(errors).length > 0) {
      setValidationErrors(errors)
      god.messageWarn(`Please fill ${Object.keys(errors).join(', ')}`)
      return
    }

    setValidationErrors({})

    /** 实现生成功能 */
    const competitors = [
      values.competitor1,
    ].filter(Boolean).join('、')

    const selectedIndustryLabel = findLabelInOptions(industryOptions, values.industry_id)

    /** 组装TrendStep1Params所需参数 */
    const params: TrendStep1Params = {
      brand: values.brand,
      product_name: values.product_name,
      industry: selectedIndustryLabel || '',
      industry_id: Number(values.industry_id),
      competitor: competitors,
      product: values.product,
      role: values.role || '品牌',
      company: values.company,
      pic: setProductImage.getLatest() || '',
      ip: values.ip,
      marketing_strategy: values.marketing_strategy,
      product_market: values.product_market,
      competitor_info: values.competitor_info,
    }

    if (params.pic && !params.pic.startsWith('http')) {
      setLoading(true)
      const picRes = await FileAPI.upSingleBase64(params.pic).finally(() => {
        setLoading(false)
      })
      params.pic = picRes.url
    }

    /** 将表单数据存储到store中 */
    stateStore.formData = params
    stateStore.mode = 'chat'
  }

  /** 处理模拟数据按钮点击 */
  const handleUseMockData = () => {
    setProductImage('https://www.carslan.com.cn/uploads/upload/images/20241107/bcadeb2d185a620a711e8daa42520b76.png')
    TrendApi.isMock = true
    /** 使用mock数据并执行正常流程 */
    handleSubmit(formInitialValues)
  }

  return (
    <Form
      initialValues={ formInitialValues }
      onSubmit={ handleSubmit }
      className={ cn(
        'FormCollectionContainer w-full max-w-3xl mx-auto bg-white dark:bg-slate-900 p-4 md:p-6 rounded-lg',
        className,
      ) }
      style={ style }
    >
      <FormContent
        productImage={ productImage }
        setProductImage={ setProductImage }
        industryOptions={ industryOptions }
        isLoadingIndustries={ isLoadingIndustries }
        validationErrors={ validationErrors }
        loading={ loading }
        onUseMockData={ handleUseMockData }
      />
    </Form>
  )
})

/** 必填项标记组件 */
function RequiredMark() {
  return <span className="ml-1 text-red-500">*</span>
}

const FormContent = memo<FormContentProps>((
  {
    productImage,
    setProductImage,
    industryOptions,
    isLoadingIndustries,
    validationErrors,
    loading,
    onUseMockData,
  },
) => {
  const { state, handleSubmit, setFieldValue } = useForm()
  const { values } = state

  return (
    <>
      <div className="mb-6 text-center md:mb-8">
        <GradientText
          className="mb-2 rounded-none text-3xl font-bold md:mb-4 md:text-4xl"
          colors={ ['#9784FF', '#7E90FF', '#5C9FFF', '#39B0FF'] }
          showAnimate
        >
          Welcome to PhotoG !
        </GradientText>
        <p className="text-base text-gray-600 md:text-lg dark:text-gray-400">
          An efficient AI-Marketing team solve all your marketing problems.
        </p>
      </div>

      {/* 基本信息 - 三列布局 */ }
      <div className="grid grid-cols-1 mb-4 gap-3 md:grid-cols-3 md:gap-4">
        <div>
          <label className="mb-1 block text-sm text-gray-700 font-bold dark:text-gray-300">
            Company name
            <RequiredMark />
          </label>
          <Input
            value={ values.company }
            name="company"
            placeholder="Placeholder"
            className={ cn('w-full', validationErrors.company && 'border-red-500') }
          />
        </div>
        <div>
          <label className="mb-1 block text-sm text-gray-700 font-bold dark:text-gray-300">
            Brand name
            <RequiredMark />
          </label>
          <Input
            value={ values.brand }
            name="brand"
            placeholder="Placeholder"
            className={ cn('w-full', validationErrors.brand && 'border-red-500') }
          />
        </div>
        <div>
          <label className="mb-1 block text-sm text-gray-700 font-bold dark:text-gray-300">
            Industry
            <RequiredMark />
          </label>
          <Select
            options={ industryOptions }
            value={ values.industry_id }
            name="industry_id"
            placeholder="Select an industry"
            className={ cn('w-full', validationErrors.industry_id && 'border-red-500') }
            searchable
            loading={ isLoadingIndustries }
          />
        </div>
      </div>

      {/* 产品信息 - 三列布局 */ }
      <div className="grid grid-cols-1 mb-4 gap-3 md:grid-cols-3 md:gap-4">
        <div>
          <label className="mb-1 block text-sm text-gray-700 font-bold dark:text-gray-300">
            Product name
            <RequiredMark />
          </label>
          <Input
            value={ values.product_name }
            name="product_name"
            placeholder="Placeholder"
            className={ cn('w-full', validationErrors.product_name && 'border-red-500') }
          />
        </div>
        <div>
          <label className="mb-1 block text-sm text-gray-700 font-bold dark:text-gray-300">
            Product Key features
            <RequiredMark />
          </label>
          <Input
            value={ values.product }
            name="product"
            placeholder="Placeholder"
            className={ cn('w-full', validationErrors.product && 'border-red-500') }
          />
        </div>
        <div>
          <label className="mb-1 block text-sm text-gray-700 font-bold dark:text-gray-300">
            Product Market
          </label>
          <Input
            value={ values.product_market }
            name="product_market"
            placeholder="Placeholder"
            className="w-full"
          />
        </div>
      </div>

      {/* 品牌定位和竞争信息 - 三列布局 */ }
      <div className="grid grid-cols-1 mb-4 gap-3 md:grid-cols-3 md:gap-4">
        <div>
          <label className="mb-1 block text-sm text-gray-700 font-bold dark:text-gray-300">
            Brand role/position
            <RequiredMark />
          </label>
          <Input
            value={ values.role }
            name="role"
            placeholder="Placeholder"
            className={ cn('w-full', validationErrors.role && 'border-red-500') }
          />
        </div>
        <div>
          <label className="mb-1 block text-sm text-gray-700 font-bold dark:text-gray-300">
            IP information
            <RequiredMark />
          </label>
          <Input
            value={ values.ip }
            name="ip"
            placeholder="Placeholder"
            className={ cn('w-full', validationErrors.ip && 'border-red-500') }
          />
        </div>
        <div>
          <label className="mb-1 block text-sm text-gray-700 font-bold dark:text-gray-300">
            Marketing strategy
            <RequiredMark />
          </label>
          <Input
            value={ values.marketing_strategy }
            name="marketing_strategy"
            placeholder="Placeholder"
            className={ cn('w-full', validationErrors.marketing_strategy && 'border-red-500') }
          />
        </div>
      </div>

      {/* 竞争对手信息 - 三列布局 */ }
      <div className="grid grid-cols-1 mb-4 gap-3 md:grid-cols-3 md:gap-4">
        <div>
          <label className="mb-1 block text-sm text-gray-700 font-bold dark:text-gray-300">
            Your main competitors
            <RequiredMark />
          </label>
          <Input
            value={ values.competitor1 }
            name="competitor1"
            placeholder="Placeholder"
            className={ cn('w-full', validationErrors.competitor1 && 'border-red-500') }
          />
        </div>
        <div className="md:col-span-2">
          <label className="mb-1 block text-sm text-gray-700 font-bold dark:text-gray-300">
            Competitor Information
            <RequiredMark />
          </label>
          <Input
            value={ values.competitor_info }
            name="competitor_info"
            placeholder="Placeholder"
            className={ cn('w-full', validationErrors.competitor_info && 'border-red-500') }
          />
        </div>
      </div>

      <div className="mb-4 flex justify-center">
        { productImage
          ? (
              <div className="relative h-32 w-52 border border-gray-100 rounded-2xl">
                <img
                  src={ productImage }
                  alt="Product"
                  className="h-full w-full rounded-md object-contain"
                />
                <RmBtn onClick={ () => setProductImage('') }></RmBtn>
              </div>
            )
          : (
              <Uploader
                accept=".jpg,.jpeg,.png,.webp"
                className="h-32 w-52"
                onChange={ (files) => {
                  setProductImage(files?.[0]?.base64 || '')
                } }
              />
            ) }
      </div>

      <div className="flex justify-center gap-4">
        <Button
          loading={ loading }
          variant="primary"
          className="rounded-full px-5"
          onClick={ handleSubmit }
        >
          Generate
        </Button>
        <Button
          variant="warning"
          className="rounded-full px-5"
          onClick={ onUseMockData }
        >
          使用模拟数据
        </Button>
      </div>
    </>
  )
})

FormCollection.displayName = 'FormCollection'

export type FormCollectionProps = {
  stateStore: StateStoreType
}
& React.PropsWithChildren<React.HTMLAttributes<HTMLElement>>

type FormContentProps = {
  productImage: string
  setProductImage: (image: string) => void
  industryOptions: Option[]
  isLoadingIndustries: boolean
  validationErrors: Record<string, string>
  loading: boolean
  onUseMockData: () => void
}
