import type { StreamData, StreamId } from './StreamingDataManager'
import { motion } from 'framer-motion'
import { ChevronDown, Sparkles } from 'lucide-react'
import { memo, useEffect, useRef, useState } from 'react'
import { AnimateShow } from '@/components/Animate/AnimateShow'
import { Checkmark } from '@/components/Checkbox'
import { LoadingIcon } from '@/components/Loading/LoadingIcon'
import { typeTxt } from '@/utils/tool'
import { streamingDataManager } from './StreamingDataManager'

export interface StreamingThinkingStreamProps {
  streamId: StreamId | string
  className?: string
  autoStart?: boolean
  defaultExpanded?: boolean
  onComplete?: () => void
}

/**
 * 流式Thinking显示组件
 * 支持独立的数据流，避免数据混淆
 * 复用原有ThinkingStream的UI样式和交互逻辑
 */
export const StreamingThinkingStream = memo<StreamingThinkingStreamProps>(({
  streamId,
  className = '',
  autoStart = true,
  defaultExpanded = true,
  onComplete,
}) => {
  const contentRef = useRef<HTMLDivElement>(null)

  /** 流数据状态 */
  const [streamData, setStreamData] = useState<StreamData>({
    id: streamId,
    content: '',
    isActive: false,
    isCompleted: false,
    lastUpdated: Date.now(),
  })

  /** 折叠/展开状态 - 默认展开以便用户立即看到thinking内容 */
  const [thinkingExpanded, setThinkingExpanded] = useState(defaultExpanded)

  /** 打字机效果状态 */
  const [displayContent, setDisplayContent] = useState('')
  const [isTypingComplete, setIsTypingComplete] = useState(false)
  const stopTypingRef = useRef<(() => void) | null>(null)
  const lastContentRef = useRef('')
  const lastIndexRef = useRef(0)

  /** 判断是否完成 - 基于数据流状态 */
  const isDataComplete = streamData.content && !streamData.isActive && streamData.isCompleted

  /** 判断是否真正完成 - 需要数据完成且打字机效果完成 */
  const isThinkingDone = isDataComplete && isTypingComplete

  /** 监听完成状态，触发回调 */
  useEffect(() => {
    if (isThinkingDone && onComplete) {
      onComplete()
    }
  }, [isThinkingDone, onComplete, streamId])

  /** 订阅数据流 */
  useEffect(() => {
    const unsubscribe = streamingDataManager.subscribe(streamId, (data: StreamData) => {
      setStreamData(data)
    })

    /** 如果设置了自动开始，启动数据流 */
    if (autoStart) {
      streamingDataManager.startStream(streamId)
    }

    return unsubscribe
  }, [streamId, autoStart])

  /** 自动滚动到底部 */
  useEffect(() => {
    if (contentRef.current && displayContent) {
      contentRef.current.scrollTop = contentRef.current.scrollHeight
    }
  }, [displayContent])

  /** 处理打字机效果 - 完全复制ThinkingStream的逻辑 */
  useEffect(() => {
    /** 如果没有内容或只是占位内容，清空显示 */
    if (!streamData.content || streamData.content === '正在思考...' || streamData.content === '正在分析市场趋势和竞争对手信息，制定综合营销策略...') {
      setDisplayContent('')
      setIsTypingComplete(false)
      return
    }

    /** 如果内容变化，处理打字效果 */
    if (streamData.content !== lastContentRef.current) {
      /** 如果有正在进行的打字效果，先停止 */
      if (stopTypingRef.current) {
        stopTypingRef.current()
        stopTypingRef.current = null
      }

      /** 重置打字机完成状态 - 新内容到达时重置 */
      setIsTypingComplete(false)

      /** 确定继续的位置 */
      const continueFromIndex = streamData.content.startsWith(lastContentRef.current)
        ? lastIndexRef.current
        : 0

      /** 启动打字效果 */
      const { stop, promise } = typeTxt({
        content: streamData.content,
        continueFromIndex,
        speed: import.meta.env.DEV
          ? 3
          : 7,
        callback: (text) => {
          setDisplayContent(text)
          lastIndexRef.current = text.length
        },
      })

      /** 监听打字机效果完成 */
      promise.then(() => {
        setIsTypingComplete(true)
      })

      /** 保存停止函数和当前内容 */
      stopTypingRef.current = stop
      lastContentRef.current = streamData.content
    }
  }, [streamData.content])

  /** 清理打字效果 */
  useEffect(() => {
    return () => {
      if (stopTypingRef.current) {
        stopTypingRef.current()
      }
    }
  }, [])

  /** 如果没有内容且从未激活过，不显示组件 */
  if (!streamData.content && !streamData.isActive && !streamData.isCompleted) {
    return null
  }

  return (
    <motion.div
      initial={ { opacity: 0, y: 20, height: 0 } }
      animate={ { opacity: 1, y: 0, height: 'auto' } }
      exit={ { opacity: 0, y: -20, height: 0 } }
      transition={ { duration: 0.3 } }
      className={ `thinking-stream-container ${className}` }
    >
      <div className="mb-2 flex flex-col space-y-2">
        {/* 头部 - 完全复制 Distribution 页面的样式 */}
        <div
          className="w-fit flex cursor-pointer items-center rounded-2xl bg-slate-100 px-3 py-1.5 text-slate-500 space-x-2 dark:bg-slate-600 dark:text-slate-300"
          onClick={ () => setThinkingExpanded(!thinkingExpanded) }
        >
          <Sparkles size={ 14 } className="text-slate-400 dark:text-slate-400" />
          <span className="text-xs font-medium">
            {isThinkingDone
              ? 'Thinking done'
              : 'Thinking...'}
          </span>

          {isThinkingDone
            ? (
                <Checkmark size={ 14 } />
              )
            : (
                <LoadingIcon size={ 14 } />
              )}

          <motion.div
            animate={ {
              rotate: thinkingExpanded
                ? 180
                : 0,
            } }
            transition={ { duration: 0.2 } }
            className="ml-auto"
          >
            <ChevronDown className="h-3 w-3 text-slate-400" />
          </motion.div>
        </div>

        {/* 内容区域 - 完全复制 Distribution 页面的样式 */}
        <AnimateShow
          show={ thinkingExpanded }
          variants={ {
            initial: { opacity: 1, height: 'auto' },
            animate: { opacity: 1, height: 'auto' },
            exit: { opacity: 0, height: 0 },
          } }
        >
          <div
            ref={ contentRef }
            className="max-h-40 overflow-y-auto whitespace-pre-wrap border-l border-slate-200 pl-2 text-xs text-slate-500 dark:border-slate-700 dark:text-slate-400"
          >
            {/* 优先显示打字机效果的内容，然后是原始内容，最后是占位内容 */}
            {displayContent || (streamData.content && streamData.content !== '正在思考...' && streamData.content !== '正在分析市场趋势和竞争对手信息，制定综合营销策略...'
              ? streamData.content
              : '正在思考...')}
            {/* 光标效果 - 只在正在打字或等待时显示 */}
            {(!isThinkingDone && (
              (displayContent && displayContent.length < streamData.content.length)
              || !streamData.content
              || streamData.content === '正在思考...'
              || streamData.content === '正在分析市场趋势和竞争对手信息，制定综合营销策略...'
            )) && (
              <span className="ml-1 inline-block h-3 w-1 animate-pulse bg-slate-500" />
            )}
          </div>
        </AnimateShow>
      </div>
    </motion.div>
  )
})

StreamingThinkingStream.displayName = 'StreamingThinkingStream'
