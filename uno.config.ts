import presetWind3 from '@unocss/preset-wind3'
import {
  defineConfig,
  presetAttributify,
  transformerVariantGroup,
} from 'unocss'
import * as Variable from './src/styles/variable'

const shake = `
{
  10%,
  90% {
    transform: translate3d(-3px, 0, 0);
  }

  20%,
  80% {
    transform: translate3d(4px, 0, 0);
  }

  30%,
  50%,
  70% {
    transform: translate3d(-6px, 0, 0);
  }

  40%,
  60% {
    transform: translate3d(6px, 0, 0);
  }
}
`

export default defineConfig({
  presets: [
    presetWind3(),
    presetAttributify(),
  ],

  transformers: [
    transformerVariantGroup(),
  ],

  theme: {
    fontFamily: {
      system: ['Roboto', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'sans-serif'],
    },
    colors: {
      lightBg: Variable.lightBg,
      innerBg: Variable.innerBg,
      primary: Variable.primaryColor,
      border: Variable.borderColor,
      light: Variable.lightTextColor,
      success: Variable.successColor,
      info: Variable.infoColor,
      danger: Variable.dangerColor,
    },

    animation: {
      keyframes: {
        shake,
      },
      durations: {
        shake: '.4s',
      },
      timingFns: {
        shake: 'cubic-bezier(0.28, -0.44, 0.65, 1.55)',
      },
      properties: {
        shake: { 'animation-fill-mode': 'both' },
      },
      counts: {
        shake: '2',
      },
    },
  },

  shortcuts: {
    'hide-scroll': [
      // Firefox
      '[scrollbar-width:none]',
      // IE and Edge
      '[-ms-overflow-style:none]',
      // Safari and Chrome (using variant group for pseudo-element)
      '[&::-webkit-scrollbar]:hidden', // 'hidden' is a utility for 'display: none;'
    ].join(' '),

    'center-x': 'left-1/2 -translate-x-1/2',
    'center-y': 'top-1/2 -translate-y-1/2',
    'center': 'center-x center-y',

    'toning-green': `text-${Variable.greenTextColor} bg-${Variable.greenBgColor} dark:text-${Variable.greenDarkTextColor} dark:bg-${Variable.greenDarkBgColor}`,
    'toning-blue': `text-${Variable.blueTextColor} bg-${Variable.blueBgColor} dark:text-${Variable.blueDarkTextColor} dark:bg-${Variable.blueDarkBgColor}`,
    'toning-purple': `text-${Variable.purpleTextColor} bg-${Variable.purpleBgColor} dark:text-${Variable.purpleDarkTextColor} dark:bg-${Variable.purpleDarkBgColor}`,
    'toning-orange': `text-${Variable.orangeTextColor} bg-${Variable.orangeBgColor} dark:text-${Variable.orangeDarkTextColor} dark:bg-${Variable.orangeDarkBgColor}`,
    'toning-red': `text-${Variable.redTextColor} bg-${Variable.redBgColor} dark:text-${Variable.redDarkTextColor} dark:bg-${Variable.redDarkBgColor}`,
    'toning-yellow': `text-${Variable.yellowTextColor} bg-${Variable.yellowBgColor} dark:text-${Variable.yellowDarkTextColor} dark:bg-${Variable.yellowDarkBgColor}`,
    'toning-gray': `text-${Variable.grayTextColor} bg-${Variable.grayBgColor} dark:text-${Variable.grayDarkTextColor} dark:bg-${Variable.grayDarkBgColor}`,
    'toning-slate': `text-${Variable.slateTextColor} bg-${Variable.slateBgColor} dark:text-${Variable.slateDarkTextColor} dark:bg-${Variable.slateDarkBgColor}`,

    'toning-green-text': `text-${Variable.greenTextColor} dark:text-${Variable.greenDarkTextColor}`,
    'toning-blue-text': `text-${Variable.blueTextColor} dark:text-${Variable.blueDarkTextColor}`,
    'toning-purple-text': `text-${Variable.purpleTextColor} dark:text-${Variable.purpleDarkTextColor}`,
    'toning-orange-text': `text-${Variable.orangeTextColor} dark:text-${Variable.orangeDarkTextColor}`,
    'toning-red-text': `text-${Variable.redTextColor} dark:text-${Variable.redDarkTextColor}`,
    'toning-yellow-text': `text-${Variable.yellowTextColor} dark:text-${Variable.yellowDarkTextColor}`,
    'toning-gray-text': `text-${Variable.grayTextColor} dark:text-${Variable.grayDarkTextColor}`,
    'toning-slate-text': `text-${Variable.slateTextColor} dark:text-${Variable.slateDarkTextColor}`,

    'toning-green-border': `border-${Variable.greenBorderColor} dark:border-${Variable.greenDarkBorderColor}`,
    'toning-blue-border': `border-${Variable.blueBorderColor} dark:border-${Variable.blueDarkBorderColor}`,
    'toning-purple-border': `border-${Variable.purpleBorderColor} dark:border-${Variable.purpleDarkBorderColor}`,
    'toning-orange-border': `border-${Variable.orangeBorderColor} dark:border-${Variable.orangeDarkBorderColor}`,
    'toning-red-border': `border-${Variable.redBorderColor} dark:border-${Variable.redDarkBorderColor}`,
    'toning-yellow-border': `border-${Variable.yellowBorderColor} dark:border-${Variable.yellowDarkBorderColor}`,
    'toning-gray-border': `border-${Variable.grayBorderColor} dark:border-${Variable.grayDarkBorderColor}`,
    'toning-slate-border': `border-${Variable.slateBorderColor} dark:border-${Variable.slateDarkBorderColor}`,
  },

  content: {
    pipeline: {
      include: [
        /\.(vue|svelte|[jt]sx|vine.ts|mdx?|astro|elm|php|phtml|html)($|\?)/,
        // include js/ts files
        'src/**/*.{js,ts}',
      ],
      // exclude: []
    },
  },
})
